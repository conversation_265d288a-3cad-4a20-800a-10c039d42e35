<?php
/**
 * تشخيص مشاكل الذكاء الاصطناعي
 */

echo "<h1>🔍 تشخيص مشاكل الذكاء الاصطناعي</h1>";

// 1. فحص الملفات
echo "<h2>1. فحص الملفات:</h2>";
$files_to_check = [
    'backend/config/config.php',
    'backend/ai/ai-helper.php',
    'config.php'
];

foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "✅ $file موجود<br>";
    } else {
        echo "❌ $file غير موجود<br>";
    }
}

// 2. تحميل الإعدادات
echo "<h2>2. تحميل الإعدادات:</h2>";
if (file_exists('backend/config/config.php')) {
    require_once 'backend/config/config.php';
    echo "✅ تم تحميل backend/config/config.php<br>";
} elseif (file_exists('config.php')) {
    require_once 'config.php';
    echo "✅ تم تحميل config.php<br>";
} else {
    echo "❌ لم يتم العثور على ملف إعدادات<br>";
}

// 3. فحص الثوابت
echo "<h2>3. فحص الثوابت:</h2>";
$constants = ['OPENROUTER_API_KEY', 'OPENROUTER_API_URL', 'AI_MODEL'];
foreach ($constants as $const) {
    if (defined($const)) {
        $value = constant($const);
        echo "✅ $const: " . (strlen($value) > 30 ? substr($value, 0, 30) . '...' : $value) . "<br>";
    } else {
        echo "❌ $const غير محدد<br>";
    }
}

// 4. فحص الفئات
echo "<h2>4. فحص الفئات:</h2>";
if (file_exists('backend/ai/ai-helper.php')) {
    require_once 'backend/ai/ai-helper.php';
    echo "✅ تم تحميل ai-helper.php<br>";
    
    if (class_exists('AIHelper')) {
        echo "✅ فئة AIHelper موجودة<br>";
        
        if (method_exists('AIHelper', 'callOpenRouterAPI')) {
            echo "✅ دالة callOpenRouterAPI موجودة<br>";
        } else {
            echo "❌ دالة callOpenRouterAPI غير موجودة<br>";
        }
    } else {
        echo "❌ فئة AIHelper غير موجودة<br>";
    }
} else {
    echo "❌ ملف ai-helper.php غير موجود<br>";
}

// 5. اختبار API مباشر
echo "<h2>5. اختبار API مباشر:</h2>";

if (defined('OPENROUTER_API_KEY') && !empty(OPENROUTER_API_KEY)) {
    $test_message = "مرحبا، أجب بكلمة واحدة فقط: مرحبا";
    
    $data = [
        'model' => defined('AI_MODEL') ? AI_MODEL : 'deepseek/deepseek-chat-v3-0324:free',
        'messages' => [
            ['role' => 'user', 'content' => $test_message]
        ],
        'max_tokens' => 50,
        'temperature' => 0.7
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, defined('OPENROUTER_API_URL') ? OPENROUTER_API_URL : 'https://openrouter.ai/api/v1/chat/completions');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . OPENROUTER_API_KEY,
        'HTTP-Referer: http://localhost/Mohassebk',
        'X-Title: Mohassebk Debug Test'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    echo "📤 إرسال الطلب...<br>";
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    echo "📊 HTTP Code: $http_code<br>";
    echo "🔧 cURL Error: " . ($curl_error ?: 'لا يوجد') . "<br>";
    
    if ($response === false) {
        echo "❌ فشل في الحصول على رد<br>";
    } elseif ($http_code !== 200) {
        echo "❌ HTTP Error: $http_code<br>";
        echo "📄 الرد: " . htmlspecialchars(substr($response, 0, 300)) . "<br>";
    } else {
        echo "✅ تم الحصول على رد بنجاح<br>";
        
        $decoded = json_decode($response, true);
        if ($decoded === null) {
            echo "❌ فشل في تحليل JSON<br>";
            echo "📄 الرد الخام: " . htmlspecialchars(substr($response, 0, 300)) . "<br>";
        } elseif (isset($decoded['choices'][0]['message']['content'])) {
            $content = trim($decoded['choices'][0]['message']['content']);
            echo "🎉 <strong>نجح الاختبار!</strong><br>";
            echo "💬 الرد: " . htmlspecialchars($content) . "<br>";
        } else {
            echo "❌ لا يوجد محتوى في الرد<br>";
            echo "📄 البنية: " . htmlspecialchars(json_encode($decoded, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)) . "<br>";
        }
    }
} else {
    echo "❌ مفتاح API غير محدد<br>";
}

// 6. اختبار AIHelper إذا كان متاحاً
echo "<h2>6. اختبار AIHelper:</h2>";
if (class_exists('AIHelper') && method_exists('AIHelper', 'callOpenRouterAPI')) {
    try {
        $ai_response = AIHelper::callOpenRouterAPI("مرحبا، أجب بكلمة واحدة: مرحبا", 50, 0.7);
        if ($ai_response !== false && !empty($ai_response)) {
            echo "✅ AIHelper يعمل بشكل صحيح<br>";
            echo "💬 الرد: " . htmlspecialchars($ai_response) . "<br>";
        } else {
            echo "❌ AIHelper أرجع رد فارغ أو false<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في AIHelper: " . htmlspecialchars($e->getMessage()) . "<br>";
    }
} else {
    echo "❌ AIHelper غير متاح<br>";
}

echo "<h2>7. الخلاصة:</h2>";
echo "<p>إذا نجح الاختبار المباشر، فإن المشكلة في كود المساعد الذكي.</p>";
echo "<p>إذا فشل، فإن المشكلة في إعدادات API أو الاتصال.</p>";
?>
