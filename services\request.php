<?php
/**
 * Service Request Page
 * صفحة طلب الخدمة
 */

require_once '../backend/config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'client') {
    redirect(getUrl('login.php'));
}

// الحصول على قائمة الخدمات
$services = [];
try {
    $services = fetchAll("SELECT * FROM services WHERE is_active = 1 ORDER BY service_name_ar");
} catch (Exception $e) {
    logError("Services fetch error: " . $e->getMessage());
}

// الحصول على قائمة المحاسبين
$accountants = [];
try {
    $accountants = fetchAll("SELECT id, full_name FROM users WHERE role = 'accountant' AND is_active = 1 ORDER BY full_name");
} catch (Exception $e) {
    logError("Accountants fetch error: " . $e->getMessage());
}

// معالجة طلب الخدمة
$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $service_id = (int)($_POST['service_id'] ?? 0);
    $request_title = sanitize($_POST['request_title'] ?? '');
    $description = sanitize($_POST['description'] ?? '');
    $priority = sanitize($_POST['priority'] ?? 'medium');
    $preferred_accountant_id = (int)($_POST['preferred_accountant_id'] ?? 0);
    
    // التحقق من البيانات
    if (empty($service_id) || empty($request_title) || empty($description)) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } else {
        // التحقق من وجود الخدمة
        $service = fetchOne("SELECT * FROM services WHERE id = :id AND is_active = 1", ['id' => $service_id]);
        
        if (!$service) {
            $error_message = 'الخدمة المحددة غير متاحة';
        } else {
            try {
                global $database;
                
                // إنشاء طلب الخدمة
                $request_data = [
                    'user_id' => $user['id'],
                    'service_id' => $service_id,
                    'request_title' => $request_title,
                    'description' => $description,
                    'status' => 'pending',
                    'priority' => $priority,
                    'total_amount' => $service['base_price'],
                    'assigned_accountant_id' => $preferred_accountant_id > 0 ? $preferred_accountant_id : null
                ];
                
                $request_id = $database->insert('service_requests', $request_data);
                
                if ($request_id) {
                    // إنشاء إشعار للإدارة
                    $notification_data = [
                        'user_id' => 1, // المدير
                        'title' => 'طلب خدمة جديد',
                        'message' => "تم استلام طلب خدمة جديد من {$user['full_name']}",
                        'type' => 'info',
                        'related_request_id' => $request_id
                    ];
                    $database->insert('notifications', $notification_data);
                    
                    $success_message = 'تم إرسال طلبك بنجاح! سيتم توجيهك لصفحة الدفع';

                    // إعادة توجيه لصفحة الدفع
                    showMessage('تم إرسال طلب الخدمة بنجاح', 'success');
                    redirect(getUrl('payment/checkout.php?request_id=' . $request_id));
                } else {
                    $error_message = 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى';
                }
            } catch (Exception $e) {
                $error_message = 'حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى';
                logError("Service request error: " . $e->getMessage());
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلب خدمة - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Header -->
        <div class="mb-8">
            <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-4">
                <a href="<?php echo getUrl(); ?>" class="hover:text-earth-green transition-colors">الرئيسية</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <a href="<?php echo getUrl('dashboard/client'); ?>" class="hover:text-earth-green transition-colors">لوحة التحكم</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <span class="text-earth-green">طلب خدمة</span>
            </nav>
            
            <h1 class="text-3xl font-bold text-earth-green mb-2">
                طلب خدمة محاسبية
            </h1>
            <p class="text-gray-600">
                اختر الخدمة المطلوبة وأدخل تفاصيل طلبك
            </p>
        </div>
        
        <div class="grid lg:grid-cols-3 gap-8">
            
            <!-- Service Request Form -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-lg p-8">
                    
                    <!-- Error Message -->
                    <?php if ($error_message): ?>
                        <div class="mb-6 p-4 rounded-lg bg-red-100 text-red-700 border border-red-300">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-circle ml-2"></i>
                                <?php echo htmlspecialchars($error_message); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Success Message -->
                    <?php if ($success_message): ?>
                        <div class="mb-6 p-4 rounded-lg bg-green-100 text-green-700 border border-green-300">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle ml-2"></i>
                                <?php echo htmlspecialchars($success_message); ?>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" data-validate class="space-y-6">
                        
                        <!-- Service Selection -->
                        <div>
                            <label for="service_id" class="block text-sm font-medium text-gray-700 mb-2">
                                نوع الخدمة <span class="text-red-500">*</span>
                            </label>
                            <select id="service_id" 
                                    name="service_id" 
                                    required
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                                <option value="">اختر نوع الخدمة</option>
                                <?php foreach ($services as $service): ?>
                                    <option value="<?php echo $service['id']; ?>" 
                                            data-price="<?php echo $service['base_price']; ?>">
                                        <?php echo htmlspecialchars($service['service_name_ar']); ?>
                                        (<?php echo number_format($service['base_price'], 2); ?> دج)
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <!-- Request Title -->
                        <div>
                            <label for="request_title" class="block text-sm font-medium text-gray-700 mb-2">
                                عنوان الطلب <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="request_title" 
                                   name="request_title" 
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                                   placeholder="أدخل عنوان مختصر للطلب">
                        </div>
                        
                        <!-- Description -->
                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                                وصف تفصيلي للطلب <span class="text-red-500">*</span>
                            </label>
                            <textarea id="description" 
                                      name="description" 
                                      required
                                      rows="6"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                                      placeholder="اشرح بالتفصيل ما تحتاجه من هذه الخدمة..."></textarea>
                        </div>
                        
                        <!-- Priority -->
                        <div>
                            <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">
                                أولوية الطلب
                            </label>
                            <select id="priority"
                                    name="priority"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                                <option value="low">منخفضة</option>
                                <option value="medium" selected>متوسطة</option>
                                <option value="high">عالية</option>
                                <option value="urgent">عاجلة</option>
                            </select>
                        </div>

                        <!-- Preferred Accountant -->
                        <div>
                            <label for="preferred_accountant_id" class="block text-sm font-medium text-gray-700 mb-2">
                                المحاسب المفضل (اختياري)
                            </label>
                            <select id="preferred_accountant_id"
                                    name="preferred_accountant_id"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                                <option value="">اختر المحاسب (أو اتركه للإدارة)</option>
                                <?php foreach ($accountants as $accountant): ?>
                                    <option value="<?php echo $accountant['id']; ?>">
                                        <?php echo htmlspecialchars($accountant['full_name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="text-xs text-gray-500 mt-1">
                                يمكنك اختيار محاسب معين أو ترك الخيار للإدارة لتعيين المحاسب المناسب
                            </p>
                        </div>
                        
                        <!-- Submit Button -->
                        <div class="flex items-center justify-between pt-6">
                            <button type="submit" 
                                    class="bg-earth-green text-white px-8 py-3 rounded-lg font-semibold hover:bg-sand-brown transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-earth-green focus:ring-offset-2">
                                <i class="fas fa-paper-plane ml-2"></i>
                                إرسال الطلب
                            </button>
                            
                            <a href="<?php echo getUrl('dashboard/client'); ?>" 
                               class="text-gray-600 hover:text-earth-green transition-colors">
                                <i class="fas fa-arrow-right ml-2"></i>
                                العودة للوحة التحكم
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="space-y-6">
                
                <!-- Service Info -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-earth-green mb-4">
                        <i class="fas fa-info-circle ml-2"></i>
                        معلومات مهمة
                    </h3>
                    <div class="space-y-3 text-sm text-gray-600">
                        <div class="flex items-start space-x-3 space-x-reverse">
                            <i class="fas fa-clock text-earth-green mt-1"></i>
                            <p>سيتم الرد على طلبك خلال 24 ساعة</p>
                        </div>
                        <div class="flex items-start space-x-3 space-x-reverse">
                            <i class="fas fa-file-upload text-earth-green mt-1"></i>
                            <p>يمكنك رفع المستندات بعد إرسال الطلب</p>
                        </div>
                        <div class="flex items-start space-x-3 space-x-reverse">
                            <i class="fas fa-shield-alt text-earth-green mt-1"></i>
                            <p>جميع بياناتك محمية ومشفرة</p>
                        </div>
                        <div class="flex items-start space-x-3 space-x-reverse">
                            <i class="fas fa-headset text-earth-green mt-1"></i>
                            <p>دعم فني متاح 24/7</p>
                        </div>
                    </div>
                </div>
                
                <!-- Contact Info -->
                <div class="bg-earth-green text-white rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-semibold mb-4">
                        <i class="fas fa-phone ml-2"></i>
                        تحتاج مساعدة؟
                    </h3>
                    <div class="space-y-3 text-sm">
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <i class="fas fa-envelope"></i>
                            <span><?php echo SITE_EMAIL; ?></span>
                        </div>
                        <div class="flex items-center space-x-3 space-x-reverse">
                            <i class="fas fa-phone"></i>
                            <span><?php echo SITE_PHONE; ?></span>
                        </div>
                    </div>
                    <a href="<?php echo getUrl('contact.php'); ?>" 
                       class="block mt-4 bg-warm-beige text-earth-green px-4 py-2 rounded-lg text-center font-semibold hover:bg-opacity-90 transition-colors">
                        تواصل معنا
                    </a>
                </div>
                
                <!-- Recent Services -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-earth-green mb-4">
                        <i class="fas fa-star ml-2"></i>
                        الخدمات الأكثر طلباً
                    </h3>
                    <div class="space-y-3">
                        <?php 
                        $popular_services = array_slice($services, 0, 3);
                        foreach ($popular_services as $service): 
                        ?>
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-sm"><?php echo htmlspecialchars($service['service_name_ar']); ?></p>
                                    <p class="text-xs text-gray-600"><?php echo number_format($service['base_price'], 2); ?> دج</p>
                                </div>
                                <button onclick="selectService(<?php echo $service['id']; ?>)" 
                                        class="text-earth-green hover:text-sand-brown transition-colors">
                                    <i class="fas fa-plus-circle"></i>
                                </button>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <?php include '../frontend/components/footer.php'; ?>
    
    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        // Select service from sidebar
        function selectService(serviceId) {
            const serviceSelect = document.getElementById('service_id');
            serviceSelect.value = serviceId;
            serviceSelect.dispatchEvent(new Event('change'));
        }
        
        // Update price display when service changes
        document.getElementById('service_id').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            const price = selectedOption.dataset.price;
            
            if (price) {
                // You can add price display logic here
                console.log('Selected service price:', price);
            }
        });
    </script>
</body>
</html>
