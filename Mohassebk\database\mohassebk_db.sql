-- قاعدة بيانات محاسبك الرقمي
-- Digital Accountant Database - Consolidated Version

-- CREATE DATABASE IF NOT EXISTS mohassebk_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- <PERSON><PERSON> mohassebk_db;

-- جدو<PERSON> المستخدمين (Users Table)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    role ENUM('client', 'accountant', 'admin') DEFAULT 'client',
    company_name VARCHAR(100),
    tax_number VARCHAR(50),
    address TEXT,
    profile_image VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول الخدمات المحاسبية (Accounting Services Table)
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    service_name VARCHAR(100) NOT NULL,
    service_name_ar VARCHAR(100) NOT NULL,
    description TEXT,
    description_ar TEXT,
    base_price DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_service_name_ar (service_name_ar)
);

-- جدول طلبات الخدمات (Service Requests Table)
CREATE TABLE IF NOT EXISTS service_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    service_id INT NOT NULL,
    request_title VARCHAR(200) NOT NULL,
    description TEXT,
    status ENUM('pending', 'in_progress', 'under_review', 'completed', 'cancelled') DEFAULT 'pending',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    assigned_accountant_id INT,
    estimated_completion DATE,
    actual_completion DATE,
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    payment_status ENUM('pending', 'paid', 'refunded') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id),
    FOREIGN KEY (assigned_accountant_id) REFERENCES users(id)
);

-- جدول المستندات المرفوعة (Uploaded Documents Table)
CREATE TABLE IF NOT EXISTS documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_id INT NOT NULL,
    user_id INT NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_processed BOOLEAN DEFAULT FALSE,
    ai_analysis TEXT,
    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الرسائل والتواصل (Messages Table)
CREATE TABLE IF NOT EXISTS messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_id INT NOT NULL,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    message_text TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول الإشعارات (Notifications Table)
CREATE TABLE IF NOT EXISTS notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT FALSE,
    related_request_id INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (related_request_id) REFERENCES service_requests(id) ON DELETE SET NULL
);

-- جدول المدفوعات (Payments Table)
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    request_id INT,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'DZD',
    payment_method ENUM('credit_card', 'bank_transfer', 'paypal', 'cash') DEFAULT 'credit_card',
    transaction_id VARCHAR(100) UNIQUE,
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES service_requests(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول إعدادات النظام (System Settings Table)
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description_ar VARCHAR(255),
    description_en VARCHAR(255),
    is_editable BOOLEAN DEFAULT TRUE,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول رسائل التواصل من صفحة اتصل بنا (Contact Messages Table)
CREATE TABLE IF NOT EXISTS contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    company VARCHAR(100),
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    status ENUM('new', 'read', 'replied', 'closed') DEFAULT 'new',
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول محادثات الذكاء الاصطناعي (AI Conversations Table)
CREATE TABLE IF NOT EXISTS ai_conversations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_id VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    response TEXT NOT NULL,
    model_used VARCHAR(100),
    tokens_used INT DEFAULT 0,
    processing_time DECIMAL(5,3) DEFAULT 0.000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- إدراج الخدمات الأساسية
INSERT IGNORE INTO services (service_name, service_name_ar, description_ar, base_price) VALUES
('Tax Calculation', 'حساب الضرائب', 'حساب دقيق للضرائب المستحقة وفقاً للقوانين الجزائرية', 5000.00),
('Financial Audit', 'المراجعة والتدقيق المالي', 'مراجعة شاملة للقوائم المالية والحسابات', 15000.00),
('Financial Statements', 'إعداد القوائم المالية', 'إعداد قوائم مالية احترافية ومطابقة للمعايير', 10000.00),
('Account Reconciliation', 'تسوية الحسابات المالية', 'تسوية ومطابقة الحسابات المالية', 3000.00),
('Profit & Tax Management', 'إدارة الأرباح والضرائب', 'تخطيط وإدارة الأرباح والضرائب', 8000.00),
('Debt Management', 'إدارة الديون', 'متابعة وإدارة المستحقات والديون', 4000.00),
('Financial Consulting', 'خدمات الاستشارة المالية', 'استشارات مالية متخصصة', 12000.00);

-- إدراج إعدادات النظام الأساسية
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description_ar, description_en, is_public) VALUES
('site_name', 'المحاسبك الرقمي', 'string', 'اسم الموقع', 'Site Name', TRUE),
('site_email', '<EMAIL>', 'string', 'البريد الإلكتروني للموقع', 'Site Email', TRUE),
('site_phone', '+************', 'string', 'رقم هاتف الموقع', 'Site Phone', TRUE),
('site_address', 'الجزائر العاصمة، الجزائر', 'string', 'عنوان الموقع', 'Site Address', TRUE),
('site_description', 'منصة الخدمات المحاسبية الرقمية', 'string', 'وصف الموقع', 'Site Description', TRUE),
('currency', 'DZD', 'string', 'العملة المستخدمة', 'Currency', TRUE),
('timezone', 'Africa/Algiers', 'string', 'المنطقة الزمنية', 'Timezone', FALSE),
('max_file_size', '10', 'number', 'الحد الأقصى لحجم الملف بالميجابايت', 'Maximum File Size (MB)', FALSE),
('allowed_file_types', 'pdf,doc,docx,xls,xlsx,jpg,jpeg,png', 'string', 'أنواع الملفات المسموحة', 'Allowed File Types', FALSE),
('ai_enabled', '1', 'boolean', 'تفعيل الذكاء الاصطناعي', 'AI Enabled', FALSE),
('ai_model', 'deepseek/deepseek-chat-v3-0324:free', 'string', 'نموذج الذكاء الاصطناعي', 'AI Model', FALSE),
('openrouter_api_key', '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', 'string', 'مفتاح API للذكاء الاصطناعي', 'OpenRouter API Key', FALSE),
('email_notifications', '1', 'boolean', 'تفعيل إشعارات البريد الإلكتروني', 'Email Notifications', FALSE);

-- إنشاء مستخدم إداري افتراضي
INSERT IGNORE INTO users (username, email, password, full_name, role, is_active, email_verified) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', TRUE, TRUE);

-- إنشاء محاسب تجريبي
INSERT IGNORE INTO users (username, email, password, full_name, role, is_active, email_verified) VALUES
('accountant1', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'أحمد المحاسب', 'accountant', TRUE, TRUE);
