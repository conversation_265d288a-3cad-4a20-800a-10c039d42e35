<?php
/**
 * Accountant Profile Management
 * إدارة الملف الشخصي للمحاسب
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'accountant') {
    redirect(getUrl('login.php'));
}

// معالجة تحديث الملف الشخصي
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_profile'])) {
    $full_name = sanitize($_POST['full_name']);
    $email = sanitize($_POST['email']);
    $phone = sanitize($_POST['phone']);
    $company_name = sanitize($_POST['company_name']);
    $address = sanitize($_POST['address']);
    
    // التحقق من صحة البيانات
    $errors = [];
    
    if (empty($full_name)) {
        $errors[] = 'الاسم الكامل مطلوب';
    }
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'البريد الإلكتروني غير صحيح';
    }
    
    // التحقق من عدم تكرار البريد الإلكتروني
    if ($email !== $user['email']) {
        $existing_user = fetchOne(
            "SELECT id FROM users WHERE email = :email AND id != :user_id",
            ['email' => $email, 'user_id' => $user['id']]
        );
        
        if ($existing_user) {
            $errors[] = 'البريد الإلكتروني مستخدم من قبل مستخدم آخر';
        }
    }
    
    if (empty($errors)) {
        try {
            $sql = "UPDATE users SET 
                    full_name = :full_name,
                    email = :email,
                    phone = :phone,
                    company_name = :company_name,
                    address = :address,
                    updated_at = NOW()
                    WHERE id = :user_id";
            
            executeQuery($sql, [
                'full_name' => $full_name,
                'email' => $email,
                'phone' => $phone,
                'company_name' => $company_name,
                'address' => $address,
                'user_id' => $user['id']
            ]);
            
            // تحديث بيانات المستخدم في الجلسة
            $_SESSION['user'] = fetchOne("SELECT * FROM users WHERE id = :id", ['id' => $user['id']]);
            $user = $_SESSION['user'];
            
            showMessage('تم تحديث الملف الشخصي بنجاح', 'success');
            redirect(getUrl('dashboard/accountant/profile.php'));
        } catch (Exception $e) {
            $errors[] = 'حدث خطأ أثناء تحديث البيانات';
            logError("Profile update error: " . $e->getMessage());
        }
    }
}

// معالجة تغيير كلمة المرور
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    $password_errors = [];
    
    if (empty($current_password)) {
        $password_errors[] = 'كلمة المرور الحالية مطلوبة';
    } elseif (!password_verify($current_password, $user['password'])) {
        $password_errors[] = 'كلمة المرور الحالية غير صحيحة';
    }
    
    if (empty($new_password)) {
        $password_errors[] = 'كلمة المرور الجديدة مطلوبة';
    } elseif (strlen($new_password) < 6) {
        $password_errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    
    if ($new_password !== $confirm_password) {
        $password_errors[] = 'تأكيد كلمة المرور غير متطابق';
    }
    
    if (empty($password_errors)) {
        try {
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            
            $sql = "UPDATE users SET password = :password, updated_at = NOW() WHERE id = :user_id";
            executeQuery($sql, [
                'password' => $hashed_password,
                'user_id' => $user['id']
            ]);
            
            showMessage('تم تغيير كلمة المرور بنجاح', 'success');
            redirect(getUrl('dashboard/accountant/profile.php'));
        } catch (Exception $e) {
            $password_errors[] = 'حدث خطأ أثناء تغيير كلمة المرور';
            logError("Password change error: " . $e->getMessage());
        }
    }
}

// إحصائيات المحاسب
$stats = [
    'total_requests' => 0,
    'completed_requests' => 0,
    'total_documents' => 0,
    'ai_conversations' => 0
];

try {
    // إجمالي الطلبات المعينة
    $stats['total_requests'] = fetchOne(
        "SELECT COUNT(*) as count FROM service_requests WHERE assigned_accountant_id = :accountant_id",
        ['accountant_id' => $user['id']]
    )['count'];
    
    // الطلبات المكتملة
    $stats['completed_requests'] = fetchOne(
        "SELECT COUNT(*) as count FROM service_requests WHERE assigned_accountant_id = :accountant_id AND status = 'completed'",
        ['accountant_id' => $user['id']]
    )['count'];
    
    // إجمالي المستندات
    $stats['total_documents'] = fetchOne(
        "SELECT COUNT(*) as count FROM documents d 
         INNER JOIN service_requests sr ON d.request_id = sr.id 
         WHERE sr.assigned_accountant_id = :accountant_id",
        ['accountant_id' => $user['id']]
    )['count'];
    
    // محادثات الذكاء الاصطناعي
    $stats['ai_conversations'] = fetchOne(
        "SELECT COUNT(*) as count FROM ai_conversations WHERE user_id = :user_id",
        ['user_id' => $user['id']]
    )['count'];
    
} catch (Exception $e) {
    logError("Profile stats error: " . $e->getMessage());
}

// الحصول على رسالة الفلاش
$flash_message = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($flash_message): ?>
            <div class="mb-6 p-4 rounded-lg <?php echo $flash_message['type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                <div class="flex items-center">
                    <i class="fas <?php echo $flash_message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($flash_message['message']); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Errors -->
        <?php if (!empty($errors)): ?>
            <div class="mb-6 p-4 rounded-lg bg-red-100 text-red-700 border border-red-300">
                <div class="flex items-center mb-2">
                    <i class="fas fa-exclamation-circle ml-2"></i>
                    <span class="font-semibold">يرجى تصحيح الأخطاء التالية:</span>
                </div>
                <ul class="list-disc list-inside">
                    <?php foreach ($errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($password_errors)): ?>
            <div class="mb-6 p-4 rounded-lg bg-red-100 text-red-700 border border-red-300">
                <div class="flex items-center mb-2">
                    <i class="fas fa-exclamation-circle ml-2"></i>
                    <span class="font-semibold">أخطاء في تغيير كلمة المرور:</span>
                </div>
                <ul class="list-disc list-inside">
                    <?php foreach ($password_errors as $error): ?>
                        <li><?php echo htmlspecialchars($error); ?></li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-earth-green mb-2">الملف الشخصي</h1>
                    <p class="text-gray-600">إدارة بياناتك الشخصية وإعدادات الحساب</p>
                </div>
                <a href="<?php echo getUrl('dashboard/accountant'); ?>" 
                   class="bg-earth-green text-white px-4 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للوحة التحكم
                </a>
            </div>
        </div>
        
        <!-- Profile Stats -->
        <div class="grid md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                <div class="w-12 h-12 bg-earth-green rounded-lg flex items-center justify-center text-white mx-auto mb-3">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="text-2xl font-bold text-earth-green"><?php echo $stats['total_requests']; ?></div>
                <div class="text-sm text-gray-600">إجمالي الطلبات</div>
            </div>
            
            <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center text-white mx-auto mb-3">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="text-2xl font-bold text-green-600"><?php echo $stats['completed_requests']; ?></div>
                <div class="text-sm text-gray-600">طلبات مكتملة</div>
            </div>
            
            <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center text-white mx-auto mb-3">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="text-2xl font-bold text-blue-600"><?php echo $stats['total_documents']; ?></div>
                <div class="text-sm text-gray-600">المستندات</div>
            </div>
            
            <div class="bg-white rounded-lg shadow-lg p-6 text-center">
                <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center text-white mx-auto mb-3">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="text-2xl font-bold text-purple-600"><?php echo $stats['ai_conversations']; ?></div>
                <div class="text-sm text-gray-600">محادثات الذكاء الاصطناعي</div>
            </div>
        </div>

        <!-- Profile Forms -->
        <div class="grid lg:grid-cols-2 gap-8">

            <!-- Personal Information -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-earth-green mb-6">
                    <i class="fas fa-user ml-2"></i>
                    المعلومات الشخصية
                </h2>

                <form method="POST">
                    <input type="hidden" name="update_profile" value="1">

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                        <input type="text"
                               name="full_name"
                               value="<?php echo htmlspecialchars($user['full_name']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green"
                               required>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                        <input type="email"
                               name="email"
                               value="<?php echo htmlspecialchars($user['email']); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green"
                               required>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                        <input type="tel"
                               name="phone"
                               value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green">
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">اسم الشركة</label>
                        <input type="text"
                               name="company_name"
                               value="<?php echo htmlspecialchars($user['company_name'] ?? ''); ?>"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green">
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">العنوان</label>
                        <textarea name="address"
                                  rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green"><?php echo htmlspecialchars($user['address'] ?? ''); ?></textarea>
                    </div>

                    <button type="submit"
                            class="w-full bg-earth-green text-white py-3 rounded-lg hover:bg-sand-brown transition-colors">
                        <i class="fas fa-save ml-2"></i>
                        حفظ التغييرات
                    </button>
                </form>
            </div>

            <!-- Change Password -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-earth-green mb-6">
                    <i class="fas fa-lock ml-2"></i>
                    تغيير كلمة المرور
                </h2>

                <form method="POST">
                    <input type="hidden" name="change_password" value="1">

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الحالية *</label>
                        <input type="password"
                               name="current_password"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green"
                               required>
                    </div>

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور الجديدة *</label>
                        <input type="password"
                               name="new_password"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green"
                               required>
                        <p class="text-xs text-gray-500 mt-1">يجب أن تكون 6 أحرف على الأقل</p>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور الجديدة *</label>
                        <input type="password"
                               name="confirm_password"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green"
                               required>
                    </div>

                    <button type="submit"
                            class="w-full bg-red-600 text-white py-3 rounded-lg hover:bg-red-700 transition-colors">
                        <i class="fas fa-key ml-2"></i>
                        تغيير كلمة المرور
                    </button>
                </form>
            </div>
        </div>

        <!-- Account Information -->
        <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold text-earth-green mb-6">
                <i class="fas fa-info-circle ml-2"></i>
                معلومات الحساب
            </h2>

            <div class="grid md:grid-cols-2 gap-6">
                <div>
                    <h3 class="font-semibold text-gray-900 mb-3">تفاصيل الحساب</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">نوع الحساب:</span>
                            <span class="font-medium text-earth-green">محاسب</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">تاريخ الإنشاء:</span>
                            <span class="font-medium"><?php echo formatArabicDate($user['created_at']); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">آخر تحديث:</span>
                            <span class="font-medium"><?php echo formatArabicDate($user['updated_at']); ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">حالة الحساب:</span>
                            <span class="font-medium <?php echo $user['is_active'] ? 'text-green-600' : 'text-red-600'; ?>">
                                <?php echo $user['is_active'] ? 'نشط' : 'غير نشط'; ?>
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">البريد الإلكتروني مؤكد:</span>
                            <span class="font-medium <?php echo $user['email_verified'] ? 'text-green-600' : 'text-yellow-600'; ?>">
                                <?php echo $user['email_verified'] ? 'مؤكد' : 'غير مؤكد'; ?>
                            </span>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="font-semibold text-gray-900 mb-3">إحصائيات الأداء</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-600">معدل الإنجاز:</span>
                            <span class="font-medium text-green-600">
                                <?php
                                $completion_rate = $stats['total_requests'] > 0 ?
                                    round(($stats['completed_requests'] / $stats['total_requests']) * 100, 1) : 0;
                                echo $completion_rate . '%';
                                ?>
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">متوسط المستندات لكل طلب:</span>
                            <span class="font-medium">
                                <?php
                                $avg_docs = $stats['total_requests'] > 0 ?
                                    round($stats['total_documents'] / $stats['total_requests'], 1) : 0;
                                echo $avg_docs;
                                ?>
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">استخدام الذكاء الاصطناعي:</span>
                            <span class="font-medium text-purple-600"><?php echo $stats['ai_conversations']; ?> محادثة</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>
</body>
</html>
