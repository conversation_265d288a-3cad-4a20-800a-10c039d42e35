<?php
/**
 * Admin System Settings Page
 * صفحة إعدادات النظام
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    redirect(getUrl('dashboard/client'));
}

// معالجة حفظ الإعدادات
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = sanitize($_POST['action']);
        
        try {
            global $database;
            
            switch ($action) {
                case 'update_site_settings':
                    $settings = [
                        'site_name' => sanitize($_POST['site_name']),
                        'site_email' => sanitize($_POST['site_email']),
                        'site_phone' => sanitize($_POST['site_phone']),
                        'site_address' => sanitize($_POST['site_address']),
                        'site_description' => sanitize($_POST['site_description']),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                    
                    // حفظ الإعدادات في جدول الإعدادات أو ملف التكوين
                    foreach ($settings as $key => $value) {
                        $database->query(
                            "INSERT INTO system_settings (setting_key, setting_value, updated_at) 
                             VALUES (:key, :value, NOW()) 
                             ON DUPLICATE KEY UPDATE setting_value = :value, updated_at = NOW()",
                            ['key' => $key, 'value' => $value]
                        );
                    }
                    
                    $success_message = 'تم حفظ إعدادات الموقع بنجاح';
                    break;
                    
                case 'update_ai_settings':
                    $ai_settings = [
                        'openrouter_api_key' => sanitize($_POST['openrouter_api_key']),
                        'ai_model' => sanitize($_POST['ai_model']),
                        'ai_max_tokens' => (int)($_POST['ai_max_tokens'] ?? 1000),
                        'ai_temperature' => (float)($_POST['ai_temperature'] ?? 0.7),
                        'ai_enabled' => isset($_POST['ai_enabled']) ? 1 : 0
                    ];
                    
                    foreach ($ai_settings as $key => $value) {
                        $database->query(
                            "INSERT INTO system_settings (setting_key, setting_value, updated_at) 
                             VALUES (:key, :value, NOW()) 
                             ON DUPLICATE KEY UPDATE setting_value = :value, updated_at = NOW()",
                            ['key' => $key, 'value' => $value]
                        );
                    }
                    
                    $success_message = 'تم حفظ إعدادات الذكاء الاصطناعي بنجاح';
                    break;
                    
                case 'update_file_settings':
                    $file_settings = [
                        'max_file_size' => (int)($_POST['max_file_size'] ?? 10),
                        'allowed_file_types' => sanitize($_POST['allowed_file_types']),
                        'upload_path' => sanitize($_POST['upload_path']),
                        'auto_delete_files' => isset($_POST['auto_delete_files']) ? 1 : 0,
                        'file_retention_days' => (int)($_POST['file_retention_days'] ?? 365)
                    ];
                    
                    foreach ($file_settings as $key => $value) {
                        $database->query(
                            "INSERT INTO system_settings (setting_key, setting_value, updated_at) 
                             VALUES (:key, :value, NOW()) 
                             ON DUPLICATE KEY UPDATE setting_value = :value, updated_at = NOW()",
                            ['key' => $key, 'value' => $value]
                        );
                    }
                    
                    $success_message = 'تم حفظ إعدادات الملفات بنجاح';
                    break;
                    
                case 'update_email_settings':
                    $email_settings = [
                        'smtp_host' => sanitize($_POST['smtp_host']),
                        'smtp_port' => (int)($_POST['smtp_port'] ?? 587),
                        'smtp_username' => sanitize($_POST['smtp_username']),
                        'smtp_password' => sanitize($_POST['smtp_password']),
                        'smtp_encryption' => sanitize($_POST['smtp_encryption']),
                        'email_from_name' => sanitize($_POST['email_from_name']),
                        'email_from_address' => sanitize($_POST['email_from_address']),
                        'email_notifications' => isset($_POST['email_notifications']) ? 1 : 0
                    ];
                    
                    foreach ($email_settings as $key => $value) {
                        $database->query(
                            "INSERT INTO system_settings (setting_key, setting_value, updated_at) 
                             VALUES (:key, :value, NOW()) 
                             ON DUPLICATE KEY UPDATE setting_value = :value, updated_at = NOW()",
                            ['key' => $key, 'value' => $value]
                        );
                    }
                    
                    $success_message = 'تم حفظ إعدادات البريد الإلكتروني بنجاح';
                    break;
                    
                case 'update_security_settings':
                    $security_settings = [
                        'session_timeout' => (int)($_POST['session_timeout'] ?? 3600),
                        'max_login_attempts' => (int)($_POST['max_login_attempts'] ?? 5),
                        'password_min_length' => (int)($_POST['password_min_length'] ?? 8),
                        'require_email_verification' => isset($_POST['require_email_verification']) ? 1 : 0,
                        'enable_two_factor' => isset($_POST['enable_two_factor']) ? 1 : 0,
                        'backup_frequency' => sanitize($_POST['backup_frequency']),
                        'backup_retention_days' => (int)($_POST['backup_retention_days'] ?? 30)
                    ];
                    
                    foreach ($security_settings as $key => $value) {
                        $database->query(
                            "INSERT INTO system_settings (setting_key, setting_value, updated_at) 
                             VALUES (:key, :value, NOW()) 
                             ON DUPLICATE KEY UPDATE setting_value = :value, updated_at = NOW()",
                            ['key' => $key, 'value' => $value]
                        );
                    }
                    
                    $success_message = 'تم حفظ إعدادات الأمان بنجاح';
                    break;
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء حفظ الإعدادات';
            logError("Settings update error: " . $e->getMessage());
        }
    }
}

// جلب الإعدادات الحالية
$current_settings = [];
try {
    $settings_result = fetchAll("SELECT setting_key, setting_value FROM system_settings");
    foreach ($settings_result as $setting) {
        $current_settings[$setting['setting_key']] = $setting['setting_value'];
    }
} catch (Exception $e) {
    logError("Settings fetch error: " . $e->getMessage());
}

// إعدادات افتراضية
$default_settings = [
    'site_name' => SITE_NAME ?? 'المحاسبك',
    'site_email' => SITE_EMAIL ?? '<EMAIL>',
    'site_phone' => SITE_PHONE ?? '+213501234567',
    'site_address' => 'الجزائر العاصمة، الجزائر',
    'site_description' => 'منصة الخدمات المحاسبية الرقمية',
    'openrouter_api_key' => OPENROUTER_API_KEY ?? '',
    'ai_model' => AI_MODEL ?? 'anthropic/claude-3-haiku',
    'ai_max_tokens' => 1000,
    'ai_temperature' => 0.7,
    'ai_enabled' => 1,
    'max_file_size' => 10,
    'allowed_file_types' => 'pdf,doc,docx,xls,xlsx,jpg,jpeg,png',
    'upload_path' => 'uploads/',
    'auto_delete_files' => 0,
    'file_retention_days' => 365,
    'smtp_host' => '',
    'smtp_port' => 587,
    'smtp_username' => '',
    'smtp_password' => '',
    'smtp_encryption' => 'tls',
    'email_from_name' => 'المحاسبك',
    'email_from_address' => '<EMAIL>',
    'email_notifications' => 1,
    'session_timeout' => 3600,
    'max_login_attempts' => 5,
    'password_min_length' => 8,
    'require_email_verification' => 1,
    'enable_two_factor' => 0,
    'backup_frequency' => 'daily',
    'backup_retention_days' => 30
];

// دمج الإعدادات الحالية مع الافتراضية
$settings = array_merge($default_settings, $current_settings);
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات النظام - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($success_message): ?>
            <div class="mb-6 p-4 rounded-lg bg-green-100 text-green-700 border border-green-300">
                <div class="flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="mb-6 p-4 rounded-lg bg-red-100 text-red-700 border border-red-300">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle ml-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-4">
                <a href="<?php echo getUrl(); ?>" class="hover:text-earth-green transition-colors">الرئيسية</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <a href="<?php echo getUrl('dashboard/admin'); ?>" class="hover:text-earth-green transition-colors">لوحة الإدارة</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <span class="text-earth-green">إعدادات النظام</span>
            </nav>
            
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-earth-green mb-2">إعدادات النظام</h1>
                    <p class="text-gray-600">إدارة وتكوين إعدادات النظام العامة</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <button onclick="backupSystem()" 
                            class="bg-sand-brown text-white px-6 py-2 rounded-lg hover:bg-earth-green transition-colors">
                        <i class="fas fa-download ml-2"></i>
                        نسخ احتياطي
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Settings Tabs -->
        <div class="bg-white rounded-lg shadow-lg mb-8">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 space-x-reverse px-6">
                    <button onclick="showTab('site')" 
                            class="settings-tab active py-4 px-2 border-b-2 border-earth-green text-earth-green font-medium text-sm">
                        <i class="fas fa-globe ml-2"></i>
                        إعدادات الموقع
                    </button>
                    <button onclick="showTab('ai')" 
                            class="settings-tab py-4 px-2 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                        <i class="fas fa-robot ml-2"></i>
                        الذكاء الاصطناعي
                    </button>
                    <button onclick="showTab('files')" 
                            class="settings-tab py-4 px-2 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                        <i class="fas fa-file-upload ml-2"></i>
                        إعدادات الملفات
                    </button>
                    <button onclick="showTab('email')" 
                            class="settings-tab py-4 px-2 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                        <i class="fas fa-envelope ml-2"></i>
                        البريد الإلكتروني
                    </button>
                    <button onclick="showTab('security')" 
                            class="settings-tab py-4 px-2 border-b-2 border-transparent text-gray-500 hover:text-gray-700 font-medium text-sm">
                        <i class="fas fa-shield-alt ml-2"></i>
                        الأمان
                    </button>
                </nav>
            </div>
        </div>

        <!-- Site Settings Tab -->
        <div id="siteTab" class="settings-content bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold text-earth-green mb-6">إعدادات الموقع العامة</h2>

            <form method="POST" class="space-y-6">
                <input type="hidden" name="action" value="update_site_settings">

                <div class="grid md:grid-cols-2 gap-6">
                    <!-- Site Name -->
                    <div>
                        <label for="site_name" class="block text-sm font-medium text-gray-700 mb-2">
                            اسم الموقع <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="site_name"
                               name="site_name"
                               value="<?php echo htmlspecialchars($settings['site_name']); ?>"
                               required
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                    </div>

                    <!-- Site Email -->
                    <div>
                        <label for="site_email" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني <span class="text-red-500">*</span>
                        </label>
                        <input type="email"
                               id="site_email"
                               name="site_email"
                               value="<?php echo htmlspecialchars($settings['site_email']); ?>"
                               required
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                    </div>

                    <!-- Site Phone -->
                    <div>
                        <label for="site_phone" class="block text-sm font-medium text-gray-700 mb-2">
                            رقم الهاتف
                        </label>
                        <input type="tel"
                               id="site_phone"
                               name="site_phone"
                               value="<?php echo htmlspecialchars($settings['site_phone']); ?>"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                    </div>

                    <!-- Site Address -->
                    <div>
                        <label for="site_address" class="block text-sm font-medium text-gray-700 mb-2">
                            العنوان
                        </label>
                        <input type="text"
                               id="site_address"
                               name="site_address"
                               value="<?php echo htmlspecialchars($settings['site_address']); ?>"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                    </div>
                </div>

                <!-- Site Description -->
                <div>
                    <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">
                        وصف الموقع
                    </label>
                    <textarea id="site_description"
                              name="site_description"
                              rows="3"
                              class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors resize-vertical"><?php echo htmlspecialchars($settings['site_description']); ?></textarea>
                </div>

                <div class="flex justify-end">
                    <button type="submit"
                            class="bg-earth-green text-white px-6 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                        <i class="fas fa-save ml-2"></i>
                        حفظ الإعدادات
                    </button>
                </div>
            </form>
        </div>

        <!-- AI Settings Tab -->
        <div id="aiTab" class="settings-content bg-white rounded-lg shadow-lg p-6 hidden">
            <h2 class="text-xl font-semibold text-earth-green mb-6">إعدادات الذكاء الاصطناعي</h2>

            <form method="POST" class="space-y-6">
                <input type="hidden" name="action" value="update_ai_settings">

                <div class="grid md:grid-cols-2 gap-6">
                    <!-- OpenRouter API Key -->
                    <div class="md:col-span-2">
                        <label for="openrouter_api_key" class="block text-sm font-medium text-gray-700 mb-2">
                            مفتاح OpenRouter API <span class="text-red-500">*</span>
                        </label>
                        <input type="password"
                               id="openrouter_api_key"
                               name="openrouter_api_key"
                               value="<?php echo htmlspecialchars($settings['openrouter_api_key']); ?>"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                        <p class="text-xs text-gray-500 mt-1">احصل على مفتاح API من <a href="https://openrouter.ai" target="_blank" class="text-earth-green hover:underline">OpenRouter.ai</a></p>
                    </div>

                    <!-- AI Model -->
                    <div>
                        <label for="ai_model" class="block text-sm font-medium text-gray-700 mb-2">
                            نموذج الذكاء الاصطناعي
                        </label>
                        <select id="ai_model"
                                name="ai_model"
                                class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                            <option value="anthropic/claude-3-haiku" <?php echo $settings['ai_model'] === 'anthropic/claude-3-haiku' ? 'selected' : ''; ?>>Claude 3 Haiku</option>
                            <option value="anthropic/claude-3-sonnet" <?php echo $settings['ai_model'] === 'anthropic/claude-3-sonnet' ? 'selected' : ''; ?>>Claude 3 Sonnet</option>
                            <option value="openai/gpt-3.5-turbo" <?php echo $settings['ai_model'] === 'openai/gpt-3.5-turbo' ? 'selected' : ''; ?>>GPT-3.5 Turbo</option>
                            <option value="openai/gpt-4" <?php echo $settings['ai_model'] === 'openai/gpt-4' ? 'selected' : ''; ?>>GPT-4</option>
                        </select>
                    </div>

                    <!-- Max Tokens -->
                    <div>
                        <label for="ai_max_tokens" class="block text-sm font-medium text-gray-700 mb-2">
                            الحد الأقصى للرموز
                        </label>
                        <input type="number"
                               id="ai_max_tokens"
                               name="ai_max_tokens"
                               value="<?php echo htmlspecialchars($settings['ai_max_tokens']); ?>"
                               min="100"
                               max="4000"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                    </div>

                    <!-- Temperature -->
                    <div>
                        <label for="ai_temperature" class="block text-sm font-medium text-gray-700 mb-2">
                            درجة الحرارة (الإبداع)
                        </label>
                        <input type="number"
                               id="ai_temperature"
                               name="ai_temperature"
                               value="<?php echo htmlspecialchars($settings['ai_temperature']); ?>"
                               min="0"
                               max="2"
                               step="0.1"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                    </div>
                </div>

                <!-- AI Enabled -->
                <div class="flex items-center">
                    <input type="checkbox"
                           id="ai_enabled"
                           name="ai_enabled"
                           <?php echo $settings['ai_enabled'] ? 'checked' : ''; ?>
                           class="rounded border-gray-300 text-earth-green focus:ring-earth-green">
                    <label for="ai_enabled" class="mr-2 text-sm text-gray-700">
                        تفعيل الذكاء الاصطناعي
                    </label>
                </div>

                <div class="flex justify-end">
                    <button type="submit"
                            class="bg-earth-green text-white px-6 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                        <i class="fas fa-save ml-2"></i>
                        حفظ الإعدادات
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>

    <script>
        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.settings-content').forEach(tab => {
                tab.classList.add('hidden');
            });

            // Remove active class from all tab buttons
            document.querySelectorAll('.settings-tab').forEach(btn => {
                btn.classList.remove('active', 'border-earth-green', 'text-earth-green');
                btn.classList.add('border-transparent', 'text-gray-500');
            });

            // Show selected tab
            document.getElementById(tabName + 'Tab').classList.remove('hidden');

            // Add active class to selected tab button
            event.target.classList.add('active', 'border-earth-green', 'text-earth-green');
            event.target.classList.remove('border-transparent', 'text-gray-500');
        }

        // Backup system function
        function backupSystem() {
            if (confirm('هل أنت متأكد من إنشاء نسخة احتياطية للنظام؟')) {
                alert('سيتم إضافة وظيفة النسخ الاحتياطي قريباً');
            }
        }
    </script>
</body>
</html>
