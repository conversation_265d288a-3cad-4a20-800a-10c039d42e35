<?php
/**
 * Login Page
 * صفحة تسجيل الدخول
 */

require_once 'backend/config/config.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isLoggedIn()) {
    $user = getCurrentUser();
    if ($user['role'] === 'admin' || $user['role'] === 'accountant') {
        redirect(getUrl('dashboard/admin'));
    } else {
        redirect(getUrl('dashboard/client'));
    }
}

// معالجة تسجيل الدخول
$error_message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitize($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    if (empty($email) || empty($password)) {
        $error_message = 'يرجى إدخال البريد الإلكتروني وكلمة المرور';
    } else {
        // البحث عن المستخدم
        $sql = "SELECT * FROM users WHERE email = :email AND is_active = 1";
        $user = fetchOne($sql, ['email' => $email]);
        
        if ($user && verifyPassword($password, $user['password'])) {
            // تسجيل الدخول بنجاح
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_role'] = $user['role'];
            $_SESSION['user_name'] = $user['full_name'];
            
            // تحديث آخر تسجيل دخول
            $update_sql = "UPDATE users SET updated_at = NOW() WHERE id = :id";
            executeQuery($update_sql, ['id' => $user['id']]);
            
            // إعادة التوجيه حسب الدور
            if ($user['role'] === 'admin') {
                redirect(getUrl('dashboard/admin'));
            } elseif ($user['role'] === 'accountant') {
                redirect(getUrl('dashboard/accountant'));
            } else {
                redirect(getUrl('dashboard/client'));
            }
        } else {
            $error_message = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
        }
    }
}

// الحصول على رسالة الفلاش
$flash_message = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray min-h-screen">
    
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            
            <!-- Header -->
            <div class="text-center">
                <a href="<?php echo getUrl(); ?>" class="inline-flex items-center space-x-3 space-x-reverse">
                    <div class="w-12 h-12 bg-earth-green rounded-lg flex items-center justify-center">
                        <i class="fas fa-calculator text-white text-2xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-earth-green"><?php echo SITE_NAME; ?></span>
                </a>
                <h2 class="mt-6 text-3xl font-bold text-gray-900">
                    تسجيل الدخول
                </h2>
                <p class="mt-2 text-sm text-gray-600">
                    أو 
                    <a href="<?php echo getUrl('register.php'); ?>" class="font-medium text-earth-green hover:text-sand-brown transition-colors">
                        إنشاء حساب جديد
                    </a>
                </p>
            </div>
            
            <!-- Login Form -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                
                <!-- Flash Messages -->
                <?php if ($flash_message): ?>
                    <div class="mb-6 p-4 rounded-lg <?php echo $flash_message['type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                        <div class="flex items-center">
                            <i class="fas <?php echo $flash_message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> ml-2"></i>
                            <?php echo htmlspecialchars($flash_message['message']); ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Error Message -->
                <?php if ($error_message): ?>
                    <div class="mb-6 p-4 rounded-lg bg-red-100 text-red-700 border border-red-300">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle ml-2"></i>
                            <?php echo htmlspecialchars($error_message); ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <form method="POST" data-validate class="space-y-6">
                    
                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني
                        </label>
                        <div class="relative">
                            <input type="email" 
                                   id="email" 
                                   name="email" 
                                   required
                                   value="<?php echo htmlspecialchars($email ?? ''); ?>"
                                   class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                                   placeholder="أدخل بريدك الإلكتروني">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Password -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            كلمة المرور
                        </label>
                        <div class="relative">
                            <input type="password" 
                                   id="password" 
                                   name="password" 
                                   required
                                   class="w-full px-4 py-3 pr-12 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                                   placeholder="أدخل كلمة المرور">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <button type="button" 
                                    id="toggle-password"
                                    class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                <i class="fas fa-eye text-gray-400 hover:text-gray-600 transition-colors"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Remember Me & Forgot Password -->
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input type="checkbox" 
                                   id="remember" 
                                   name="remember"
                                   class="h-4 w-4 text-earth-green focus:ring-earth-green border-gray-300 rounded">
                            <label for="remember" class="mr-2 block text-sm text-gray-700">
                                تذكرني
                            </label>
                        </div>
                        <a href="<?php echo getUrl('forgot-password.php'); ?>" 
                           class="text-sm text-earth-green hover:text-sand-brown transition-colors">
                            نسيت كلمة المرور؟
                        </a>
                    </div>
                    
                    <!-- Submit Button -->
                    <button type="submit" 
                            class="w-full bg-earth-green text-white py-3 px-4 rounded-lg font-semibold hover:bg-sand-brown transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-earth-green focus:ring-offset-2">
                        <i class="fas fa-sign-in-alt ml-2"></i>
                        تسجيل الدخول
                    </button>
                    
                </form>
                
                <!-- Demo Accounts -->
                <div class="mt-8 pt-6 border-t border-gray-200">
                    <h3 class="text-sm font-medium text-gray-700 mb-4 text-center">حسابات تجريبية للاختبار</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <button onclick="fillDemoAccount('<EMAIL>', 'password')" 
                                class="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-3 rounded transition-colors">
                            <i class="fas fa-user-shield ml-1"></i>
                            حساب إداري
                        </button>
                        <button onclick="fillDemoAccount('<EMAIL>', 'password')" 
                                class="text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-3 rounded transition-colors">
                            <i class="fas fa-calculator ml-1"></i>
                            حساب محاسب
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Back to Home -->
            <div class="text-center">
                <a href="<?php echo getUrl(); ?>" 
                   class="text-sm text-gray-600 hover:text-earth-green transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // Toggle password visibility
        document.getElementById('toggle-password').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'fas fa-eye-slash text-gray-400 hover:text-gray-600 transition-colors';
            } else {
                passwordInput.type = 'password';
                icon.className = 'fas fa-eye text-gray-400 hover:text-gray-600 transition-colors';
            }
        });
        
        // Fill demo account credentials
        function fillDemoAccount(email, password) {
            document.getElementById('email').value = email;
            document.getElementById('password').value = password;
        }
    </script>
</body>
</html>
