<?php
/**
 * AI Service Recommendations
 * توصيات الخدمات بالذكاء الاصطناعي
 */

require_once '../config/config.php';
require_once 'ai-helper.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح لك بالوصول']);
    exit;
}

$user = getCurrentUser();

// الحصول على بيانات المستخدم للتحليل
$user_data = AIHelper::getUserAnalysisData($user['id']);

try {
    // الحصول على توصيات الخدمات
    $recommendations = getServiceRecommendations($user_data);
    
    echo json_encode([
        'success' => true,
        'recommendations' => $recommendations
    ]);
    
} catch (Exception $e) {
    logError("Service recommendations error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ أثناء الحصول على التوصيات'
    ]);
}

// تم نقل الدوال إلى ai-helper.php

/**
 * الحصول على توصيات الخدمات من الذكاء الاصطناعي
 */
function getServiceRecommendations($user_data) {
    if (!$user_data) {
        return [];
    }

    // استدعاء الذكاء الاصطناعي
    $ai_response = AIHelper::getServiceRecommendations($user_data);

    if (!$ai_response) {
        return AIHelper::getDefaultRecommendations($user_data);
    }

    // تحليل استجابة الذكاء الاصطناعي
    return AIHelper::parseServiceRecommendations($ai_response);
}

// تم نقل جميع الدوال إلى ai-helper.php
?>
