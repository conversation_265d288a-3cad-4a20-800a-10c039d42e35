<?php
/**
 * Accountant AI Tools
 * أدوات الذكاء الاصطناعي للمحاسب
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'accountant') {
    redirect(getUrl('login.php'));
}

// معالجة طلب الذكاء الاصطناعي
$ai_response = '';
$ai_error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['ai_query'])) {
    $query = sanitize($_POST['query']);
    $tool_type = sanitize($_POST['tool_type']);
    
    if (!empty($query)) {
        try {
            // استدعاء خدمة الذكاء الاصطناعي
            $ai_response = callAIService($query, $tool_type);
            
            // حفظ المحادثة في قاعدة البيانات
            $sql = "INSERT INTO ai_conversations (user_id, session_id, message, response, model_used, created_at) 
                    VALUES (:user_id, :session_id, :message, :response, :model_used, NOW())";
            executeQuery($sql, [
                'user_id' => $user['id'],
                'session_id' => session_id(),
                'message' => $query,
                'response' => $ai_response,
                'model_used' => 'deepseek/deepseek-chat:free'
            ]);
            
        } catch (Exception $e) {
            $ai_error = 'حدث خطأ أثناء معالجة الطلب: ' . $e->getMessage();
            logError("AI query error: " . $e->getMessage());
        }
    } else {
        $ai_error = 'يرجى إدخال استفسار صحيح';
    }
}

// الحصول على المحادثات السابقة
$recent_conversations = [];
try {
    $recent_conversations = fetchAll(
        "SELECT * FROM ai_conversations 
         WHERE user_id = :user_id 
         ORDER BY created_at DESC 
         LIMIT 10",
        ['user_id' => $user['id']]
    );
} catch (Exception $e) {
    logError("Fetch conversations error: " . $e->getMessage());
}

// أدوات الذكاء الاصطناعي المتاحة
$ai_tools = [
    'document_analysis' => [
        'name' => 'تحليل المستندات',
        'description' => 'تحليل المستندات المالية واستخراج البيانات المهمة',
        'icon' => 'fa-file-invoice',
        'color' => 'bg-blue-500'
    ],
    'tax_calculation' => [
        'name' => 'حساب الضرائب',
        'description' => 'مساعدة في حساب الضرائب وفقاً للقوانين الجزائرية',
        'icon' => 'fa-calculator',
        'color' => 'bg-green-500'
    ],
    'financial_advice' => [
        'name' => 'الاستشارة المالية',
        'description' => 'تقديم نصائح مالية ومحاسبية متخصصة',
        'icon' => 'fa-chart-line',
        'color' => 'bg-purple-500'
    ],
    'audit_assistance' => [
        'name' => 'مساعدة المراجعة',
        'description' => 'مساعدة في عمليات المراجعة والتدقيق المالي',
        'icon' => 'fa-search-dollar',
        'color' => 'bg-orange-500'
    ],
    'report_generation' => [
        'name' => 'إنشاء التقارير',
        'description' => 'مساعدة في إنشاء التقارير المالية والمحاسبية',
        'icon' => 'fa-file-alt',
        'color' => 'bg-red-500'
    ],
    'general_query' => [
        'name' => 'استفسار عام',
        'description' => 'أسئلة عامة حول المحاسبة والمالية',
        'icon' => 'fa-question-circle',
        'color' => 'bg-gray-500'
    ]
];

// الحصول على رسالة الفلاش
$flash_message = getFlashMessage();

// دالة استدعاء خدمة الذكاء الاصطناعي
function callAIService($query, $tool_type) {
    // إعداد السياق حسب نوع الأداة
    $context = getAIContext($tool_type);
    $full_prompt = $context . "\n\nالاستفسار: " . $query;
    
    // استدعاء API الذكاء الاصطناعي
    $api_key = getSetting('openrouter_api_key');
    $model = getSetting('ai_model', 'deepseek/deepseek-chat:free');
    
    $data = [
        'model' => $model,
        'messages' => [
            [
                'role' => 'system',
                'content' => $context
            ],
            [
                'role' => 'user',
                'content' => $query
            ]
        ],
        'max_tokens' => 1000,
        'temperature' => 0.7
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, 'https://openrouter.ai/api/v1/chat/completions');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $api_key,
        'HTTP-Referer: ' . SITE_URL,
        'X-Title: ' . SITE_NAME
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code !== 200) {
        throw new Exception('فشل في الاتصال بخدمة الذكاء الاصطناعي');
    }
    
    $result = json_decode($response, true);
    
    if (!isset($result['choices'][0]['message']['content'])) {
        throw new Exception('استجابة غير صحيحة من خدمة الذكاء الاصطناعي');
    }
    
    return $result['choices'][0]['message']['content'];
}

// دالة الحصول على السياق حسب نوع الأداة
function getAIContext($tool_type) {
    $contexts = [
        'document_analysis' => 'أنت محاسب خبير متخصص في تحليل المستندات المالية. ساعد في تحليل المستندات واستخراج البيانات المهمة.',
        'tax_calculation' => 'أنت محاسب خبير في الضرائب الجزائرية. ساعد في حساب الضرائب وفقاً للقوانين الجزائرية الحالية.',
        'financial_advice' => 'أنت مستشار مالي خبير. قدم نصائح مالية ومحاسبية متخصصة ومفيدة.',
        'audit_assistance' => 'أنت مراجع مالي خبير. ساعد في عمليات المراجعة والتدقيق المالي.',
        'report_generation' => 'أنت محاسب خبير في إعداد التقارير المالية. ساعد في إنشاء تقارير مالية احترافية.',
        'general_query' => 'أنت محاسب خبير. أجب على الأسئلة المحاسبية والمالية بطريقة واضحة ومفيدة.'
    ];
    
    return $contexts[$tool_type] ?? $contexts['general_query'];
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>أدوات الذكاء الاصطناعي - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($flash_message): ?>
            <div class="mb-6 p-4 rounded-lg <?php echo $flash_message['type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                <div class="flex items-center">
                    <i class="fas <?php echo $flash_message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($flash_message['message']); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-earth-green mb-2">أدوات الذكاء الاصطناعي</h1>
                    <p class="text-gray-600">استخدم أدوات الذكاء الاصطناعي لمساعدتك في المهام المحاسبية</p>
                </div>
                <a href="<?php echo getUrl('dashboard/accountant'); ?>" 
                   class="bg-earth-green text-white px-4 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للوحة التحكم
                </a>
            </div>
        </div>
        
        <!-- AI Tools Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <?php foreach ($ai_tools as $tool_key => $tool): ?>
                <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow cursor-pointer" 
                     onclick="selectTool('<?php echo $tool_key; ?>', '<?php echo htmlspecialchars($tool['name']); ?>')">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 <?php echo $tool['color']; ?> rounded-lg flex items-center justify-center text-white mr-4">
                            <i class="fas <?php echo $tool['icon']; ?> text-xl"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900"><?php echo $tool['name']; ?></h3>
                        </div>
                    </div>
                    <p class="text-gray-600 text-sm"><?php echo $tool['description']; ?></p>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- AI Query Form -->
        <div class="grid lg:grid-cols-2 gap-8">

            <!-- Query Form -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-earth-green mb-6">
                    <i class="fas fa-robot ml-2"></i>
                    استفسار الذكاء الاصطناعي
                </h2>

                <form method="POST" id="aiQueryForm">
                    <input type="hidden" name="ai_query" value="1">
                    <input type="hidden" name="tool_type" id="selectedTool" value="general_query">

                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">الأداة المحددة</label>
                        <div class="p-3 bg-gray-50 rounded-lg">
                            <span id="selectedToolName" class="font-semibold text-earth-green">استفسار عام</span>
                        </div>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">استفسارك</label>
                        <textarea name="query"
                                  rows="6"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green"
                                  placeholder="اكتب استفسارك هنا..."
                                  required></textarea>
                    </div>

                    <button type="submit"
                            class="w-full bg-earth-green text-white py-3 rounded-lg hover:bg-sand-brown transition-colors">
                        <i class="fas fa-paper-plane ml-2"></i>
                        إرسال الاستفسار
                    </button>
                </form>

                <!-- AI Response -->
                <?php if (!empty($ai_response) || !empty($ai_error)): ?>
                    <div class="mt-6 p-4 rounded-lg <?php echo !empty($ai_error) ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'; ?>">
                        <h3 class="font-semibold mb-3 <?php echo !empty($ai_error) ? 'text-red-800' : 'text-green-800'; ?>">
                            <i class="fas <?php echo !empty($ai_error) ? 'fa-exclamation-triangle' : 'fa-robot'; ?> ml-2"></i>
                            <?php echo !empty($ai_error) ? 'خطأ' : 'استجابة الذكاء الاصطناعي'; ?>
                        </h3>
                        <div class="<?php echo !empty($ai_error) ? 'text-red-700' : 'text-green-700'; ?> whitespace-pre-wrap">
                            <?php echo htmlspecialchars(!empty($ai_error) ? $ai_error : $ai_response); ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Conversations -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-earth-green mb-6">
                    <i class="fas fa-history ml-2"></i>
                    المحادثات السابقة
                </h2>

                <?php if (empty($recent_conversations)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-comments text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600">لا توجد محادثات سابقة</p>
                        <p class="text-sm text-gray-500">ابدأ بطرح استفسار لرؤية المحادثات هنا</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4 max-h-96 overflow-y-auto">
                        <?php foreach ($recent_conversations as $conversation): ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="mb-3">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-gray-900">استفسارك:</span>
                                        <span class="text-xs text-gray-500">
                                            <?php echo formatArabicDate($conversation['created_at']); ?>
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-700 bg-gray-50 p-2 rounded">
                                        <?php echo htmlspecialchars(substr($conversation['message'], 0, 100)) . (strlen($conversation['message']) > 100 ? '...' : ''); ?>
                                    </p>
                                </div>

                                <div>
                                    <span class="text-sm font-medium text-earth-green">الاستجابة:</span>
                                    <p class="text-sm text-gray-700 mt-1">
                                        <?php echo htmlspecialchars(substr($conversation['response'], 0, 150)) . (strlen($conversation['response']) > 150 ? '...' : ''); ?>
                                    </p>
                                </div>

                                <button onclick="expandConversation(<?php echo $conversation['id']; ?>)"
                                        class="text-xs text-earth-green hover:text-sand-brown mt-2">
                                    عرض كامل <i class="fas fa-expand-alt mr-1"></i>
                                </button>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Quick Tips -->
        <div class="mt-8 bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold text-earth-green mb-4">
                <i class="fas fa-lightbulb ml-2"></i>
                نصائح للاستخدام الأمثل
            </h2>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="p-4 bg-blue-50 rounded-lg">
                    <h3 class="font-semibold text-blue-800 mb-2">كن محدداً</h3>
                    <p class="text-sm text-blue-700">اطرح أسئلة محددة وواضحة للحصول على إجابات أكثر دقة</p>
                </div>

                <div class="p-4 bg-green-50 rounded-lg">
                    <h3 class="font-semibold text-green-800 mb-2">استخدم السياق</h3>
                    <p class="text-sm text-green-700">قدم معلومات كافية عن الحالة أو المشكلة التي تواجهها</p>
                </div>

                <div class="p-4 bg-purple-50 rounded-lg">
                    <h3 class="font-semibold text-purple-800 mb-2">اختر الأداة المناسبة</h3>
                    <p class="text-sm text-purple-700">حدد نوع الأداة المناسبة لاستفسارك للحصول على أفضل النتائج</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Conversation Detail Modal -->
    <div id="conversationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-96 overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">تفاصيل المحادثة</h3>
                        <button onclick="closeConversationModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div id="conversationContent">
                        <!-- Content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>

    <script>
        function selectTool(toolKey, toolName) {
            document.getElementById('selectedTool').value = toolKey;
            document.getElementById('selectedToolName').textContent = toolName;

            // Highlight selected tool
            document.querySelectorAll('.bg-white.rounded-lg.shadow-lg.p-6').forEach(el => {
                el.classList.remove('ring-2', 'ring-earth-green');
            });
            event.currentTarget.classList.add('ring-2', 'ring-earth-green');
        }

        function expandConversation(conversationId) {
            // Show modal with full conversation
            document.getElementById('conversationModal').classList.remove('hidden');

            // Load conversation details via AJAX
            fetch('<?php echo getUrl("backend/get-conversation.php"); ?>?id=' + conversationId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        document.getElementById('conversationContent').innerHTML = `
                            <div class="mb-4">
                                <h4 class="font-semibold text-gray-900 mb-2">الاستفسار:</h4>
                                <div class="bg-gray-50 p-3 rounded-lg">
                                    <p class="whitespace-pre-wrap">${data.conversation.message}</p>
                                </div>
                            </div>
                            <div>
                                <h4 class="font-semibold text-earth-green mb-2">الاستجابة:</h4>
                                <div class="bg-green-50 p-3 rounded-lg">
                                    <p class="whitespace-pre-wrap">${data.conversation.response}</p>
                                </div>
                            </div>
                            <div class="mt-4 text-sm text-gray-500">
                                <i class="fas fa-calendar ml-1"></i>
                                ${data.conversation.created_at}
                            </div>
                        `;
                    } else {
                        document.getElementById('conversationContent').innerHTML = '<p class="text-red-600">حدث خطأ في تحميل المحادثة</p>';
                    }
                })
                .catch(error => {
                    document.getElementById('conversationContent').innerHTML = '<p class="text-red-600">حدث خطأ في الاتصال</p>';
                });
        }

        function closeConversationModal() {
            document.getElementById('conversationModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('conversationModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeConversationModal();
            }
        });
    </script>
</body>
</html>
