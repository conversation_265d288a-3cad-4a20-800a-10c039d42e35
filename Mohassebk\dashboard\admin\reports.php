<?php
/**
 * Admin Reports and Analytics Page
 * صفحة التقارير والإحصائيات
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    redirect(getUrl('dashboard/client'));
}

// معاملات التصفية
$date_from = sanitize($_GET['date_from'] ?? date('Y-m-01')); // بداية الشهر الحالي
$date_to = sanitize($_GET['date_to'] ?? date('Y-m-d')); // اليوم الحالي
$report_type = sanitize($_GET['report_type'] ?? 'overview');

// التحقق من صحة التواريخ
if (!strtotime($date_from)) $date_from = date('Y-m-01');
if (!strtotime($date_to)) $date_to = date('Y-m-d');

// إحصائيات عامة
$overview_stats = [];
try {
    // إحصائيات المستخدمين
    $user_stats = fetchOne("
        SELECT 
            COUNT(*) as total_users,
            SUM(CASE WHEN role = 'client' THEN 1 ELSE 0 END) as clients,
            SUM(CASE WHEN role = 'accountant' THEN 1 ELSE 0 END) as accountants,
            SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_users,
            SUM(CASE WHEN DATE(created_at) BETWEEN :date_from AND :date_to THEN 1 ELSE 0 END) as new_users
        FROM users
    ", ['date_from' => $date_from, 'date_to' => $date_to]);
    
    // إحصائيات الطلبات
    $request_stats = fetchOne("
        SELECT 
            COUNT(*) as total_requests,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_requests,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
            SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress_requests,
            SUM(CASE WHEN DATE(created_at) BETWEEN :date_from AND :date_to THEN 1 ELSE 0 END) as period_requests
        FROM service_requests
    ", ['date_from' => $date_from, 'date_to' => $date_to]);
    
    // إحصائيات المدفوعات
    $payment_stats = fetchOne("
        SELECT 
            COUNT(*) as total_payments,
            SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END) as total_revenue,
            SUM(CASE WHEN payment_status = 'pending' THEN amount ELSE 0 END) as pending_amount,
            SUM(CASE WHEN DATE(created_at) BETWEEN :date_from AND :date_to THEN amount ELSE 0 END) as period_revenue,
            AVG(CASE WHEN payment_status = 'completed' THEN amount ELSE NULL END) as avg_payment
        FROM payments
    ", ['date_from' => $date_from, 'date_to' => $date_to]);
    
    $overview_stats = array_merge($user_stats, $request_stats, $payment_stats);
    
} catch (Exception $e) {
    logError("Reports stats error: " . $e->getMessage());
    $overview_stats = [
        'total_users' => 0, 'clients' => 0, 'accountants' => 0, 'active_users' => 0, 'new_users' => 0,
        'total_requests' => 0, 'completed_requests' => 0, 'pending_requests' => 0, 'in_progress_requests' => 0, 'period_requests' => 0,
        'total_payments' => 0, 'total_revenue' => 0, 'pending_amount' => 0, 'period_revenue' => 0, 'avg_payment' => 0
    ];
}

// الخدمات الأكثر طلباً
$popular_services = [];
try {
    $popular_services = fetchAll("
        SELECT 
            s.service_name_ar,
            COUNT(sr.id) as request_count,
            SUM(sr.total_amount) as total_revenue,
            AVG(sr.total_amount) as avg_amount
        FROM services s
        LEFT JOIN service_requests sr ON s.id = sr.service_id 
        WHERE sr.created_at BETWEEN :date_from AND :date_to
        GROUP BY s.id, s.service_name_ar
        ORDER BY request_count DESC
        LIMIT 10
    ", ['date_from' => $date_from, 'date_to' => $date_to]);
} catch (Exception $e) {
    logError("Popular services error: " . $e->getMessage());
}

// تطور التسجيلات الشهرية
$monthly_registrations = [];
try {
    $monthly_registrations = fetchAll("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            COUNT(*) as registrations
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month ASC
    ");
} catch (Exception $e) {
    logError("Monthly registrations error: " . $e->getMessage());
}

// تطور الإيرادات الشهرية
$monthly_revenue = [];
try {
    $monthly_revenue = fetchAll("
        SELECT 
            DATE_FORMAT(created_at, '%Y-%m') as month,
            SUM(CASE WHEN payment_status = 'completed' THEN amount ELSE 0 END) as revenue,
            COUNT(*) as payment_count
        FROM payments 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
        GROUP BY DATE_FORMAT(created_at, '%Y-%m')
        ORDER BY month ASC
    ");
} catch (Exception $e) {
    logError("Monthly revenue error: " . $e->getMessage());
}

// أحدث العملاء
$recent_clients = [];
try {
    $recent_clients = fetchAll("
        SELECT full_name, email, company_name, created_at
        FROM users 
        WHERE role = 'client' 
        ORDER BY created_at DESC 
        LIMIT 10
    ");
} catch (Exception $e) {
    logError("Recent clients error: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير والإحصائيات - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Header -->
        <div class="mb-8">
            <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-4">
                <a href="<?php echo getUrl(); ?>" class="hover:text-earth-green transition-colors">الرئيسية</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <a href="<?php echo getUrl('dashboard/admin'); ?>" class="hover:text-earth-green transition-colors">لوحة الإدارة</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <span class="text-earth-green">التقارير والإحصائيات</span>
            </nav>
            
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-earth-green mb-2">التقارير والإحصائيات</h1>
                    <p class="text-gray-600">تحليل شامل لأداء النظام والأعمال</p>
                </div>
                <div class="mt-4 md:mt-0 flex items-center space-x-4 space-x-reverse">
                    <button onclick="exportReport()" 
                            class="bg-sand-brown text-white px-6 py-2 rounded-lg hover:bg-earth-green transition-colors">
                        <i class="fas fa-download ml-2"></i>
                        تصدير التقرير
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Date Filter -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <form method="GET" class="grid md:grid-cols-4 gap-4 items-end">
                
                <!-- Date From -->
                <div>
                    <label for="date_from" class="block text-sm font-medium text-gray-700 mb-2">من تاريخ</label>
                    <input type="date" 
                           id="date_from" 
                           name="date_from" 
                           value="<?php echo $date_from; ?>"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                </div>
                
                <!-- Date To -->
                <div>
                    <label for="date_to" class="block text-sm font-medium text-gray-700 mb-2">إلى تاريخ</label>
                    <input type="date" 
                           id="date_to" 
                           name="date_to" 
                           value="<?php echo $date_to; ?>"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                </div>
                
                <!-- Report Type -->
                <div>
                    <label for="report_type" class="block text-sm font-medium text-gray-700 mb-2">نوع التقرير</label>
                    <select id="report_type" 
                            name="report_type"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                        <option value="overview" <?php echo $report_type === 'overview' ? 'selected' : ''; ?>>نظرة عامة</option>
                        <option value="financial" <?php echo $report_type === 'financial' ? 'selected' : ''; ?>>التقرير المالي</option>
                        <option value="users" <?php echo $report_type === 'users' ? 'selected' : ''; ?>>تقرير المستخدمين</option>
                        <option value="services" <?php echo $report_type === 'services' ? 'selected' : ''; ?>>تقرير الخدمات</option>
                    </select>
                </div>
                
                <!-- Submit Button -->
                <div>
                    <button type="submit" 
                            class="w-full bg-earth-green text-white px-6 py-2 rounded-lg font-semibold hover:bg-sand-brown transition-all duration-300">
                        <i class="fas fa-chart-bar ml-2"></i>
                        تحديث التقرير
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Overview Statistics -->
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            
            <!-- Total Revenue -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                        <p class="text-2xl font-bold text-green-600"><?php echo number_format($overview_stats['total_revenue'], 2); ?></p>
                        <p class="text-xs text-gray-500">دينار جزائري</p>
                    </div>
                    <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-white text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 text-sm">
                    <span class="text-green-600">+<?php echo number_format($overview_stats['period_revenue'], 2); ?></span>
                    <span class="text-gray-500">في الفترة المحددة</span>
                </div>
            </div>
            
            <!-- Total Users -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
                        <p class="text-2xl font-bold text-blue-600"><?php echo $overview_stats['total_users']; ?></p>
                        <p class="text-xs text-gray-500"><?php echo $overview_stats['active_users']; ?> نشط</p>
                    </div>
                    <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 text-sm">
                    <span class="text-blue-600">+<?php echo $overview_stats['new_users']; ?></span>
                    <span class="text-gray-500">مستخدم جديد</span>
                </div>
            </div>
            
            <!-- Total Requests -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                        <p class="text-2xl font-bold text-purple-600"><?php echo $overview_stats['total_requests']; ?></p>
                        <p class="text-xs text-gray-500"><?php echo $overview_stats['completed_requests']; ?> مكتمل</p>
                    </div>
                    <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-file-alt text-white text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 text-sm">
                    <span class="text-purple-600">+<?php echo $overview_stats['period_requests']; ?></span>
                    <span class="text-gray-500">في الفترة المحددة</span>
                </div>
            </div>
            
            <!-- Average Payment -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">متوسط الدفعة</p>
                        <p class="text-2xl font-bold text-orange-600"><?php echo number_format($overview_stats['avg_payment'], 2); ?></p>
                        <p class="text-xs text-gray-500">دينار جزائري</p>
                    </div>
                    <div class="w-12 h-12 bg-orange-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                </div>
                <div class="mt-4 text-sm">
                    <span class="text-gray-500"><?php echo $overview_stats['total_payments']; ?> معاملة</span>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid lg:grid-cols-2 gap-8 mb-8">

            <!-- Monthly Revenue Chart -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-earth-green mb-4">تطور الإيرادات الشهرية</h3>
                <div class="h-64">
                    <canvas id="revenueChart"></canvas>
                </div>
            </div>

            <!-- Monthly Registrations Chart -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-earth-green mb-4">تطور التسجيلات الشهرية</h3>
                <div class="h-64">
                    <canvas id="registrationsChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Tables Section -->
        <div class="grid lg:grid-cols-2 gap-8 mb-8">

            <!-- Popular Services -->
            <div class="bg-white rounded-lg shadow-lg">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-earth-green">الخدمات الأكثر طلباً</h3>
                </div>
                <div class="overflow-x-auto">
                    <?php if (empty($popular_services)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-chart-bar text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600">لا توجد بيانات للفترة المحددة</p>
                        </div>
                    <?php else: ?>
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الخدمة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">عدد الطلبات</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإيرادات</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">متوسط المبلغ</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($popular_services as $service): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900">
                                                <?php echo htmlspecialchars($service['service_name_ar']); ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo $service['request_count']; ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo formatCurrency($service['total_revenue']); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo formatCurrency($service['avg_amount']); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Recent Clients -->
            <div class="bg-white rounded-lg shadow-lg">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-earth-green">أحدث العملاء</h3>
                </div>
                <div class="overflow-x-auto">
                    <?php if (empty($recent_clients)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600">لا توجد عملاء جدد</p>
                        </div>
                    <?php else: ?>
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العميل</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الشركة</th>
                                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ التسجيل</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($recent_clients as $client): ?>
                                    <tr>
                                        <td class="px-6 py-4">
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($client['full_name']); ?>
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    <?php echo htmlspecialchars($client['email']); ?>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                            <?php echo htmlspecialchars($client['company_name'] ?? 'غير محدد'); ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <?php echo date('Y-m-d', strtotime($client['created_at'])); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>

    <script>
        // Revenue Chart
        const revenueCtx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: [
                    <?php
                    foreach ($monthly_revenue as $data) {
                        echo "'" . date('M Y', strtotime($data['month'] . '-01')) . "',";
                    }
                    ?>
                ],
                datasets: [{
                    label: 'الإيرادات (دينار)',
                    data: [
                        <?php
                        foreach ($monthly_revenue as $data) {
                            echo $data['revenue'] . ',';
                        }
                        ?>
                    ],
                    borderColor: '#52796F',
                    backgroundColor: 'rgba(82, 121, 111, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' د.ج';
                            }
                        }
                    }
                }
            }
        });

        // Registrations Chart
        const registrationsCtx = document.getElementById('registrationsChart').getContext('2d');
        const registrationsChart = new Chart(registrationsCtx, {
            type: 'bar',
            data: {
                labels: [
                    <?php
                    foreach ($monthly_registrations as $data) {
                        echo "'" . date('M Y', strtotime($data['month'] . '-01')) . "',";
                    }
                    ?>
                ],
                datasets: [{
                    label: 'التسجيلات',
                    data: [
                        <?php
                        foreach ($monthly_registrations as $data) {
                            echo $data['registrations'] . ',';
                        }
                        ?>
                    ],
                    backgroundColor: '#8D7B68',
                    borderColor: '#8D7B68',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });

        // Export functions
        function exportReport() {
            alert('سيتم إضافة وظيفة التصدير قريباً');
        }

        function exportToPDF() {
            window.print();
        }

        function exportToExcel() {
            alert('سيتم إضافة وظيفة تصدير Excel قريباً');
        }

        function printReport() {
            window.print();
        }
    </script>
</body>
</html>
