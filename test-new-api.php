<?php
/**
 * اختبار مفتاح API الجديد
 */

// مفتاح API الجديد
$api_key = '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';
$api_url = 'https://openrouter.ai/api/v1/chat/completions';
$model = 'deepseek/deepseek-chat-v3-0324:free';

function testNewAPI($message, $api_key, $api_url, $model) {
    $data = [
        'model' => $model,
        'messages' => [
            [
                'role' => 'system',
                'content' => 'أنت مساعد محاسبي ذكي متخصص في المحاسبة والضرائب في الجزائر. أجب باللغة العربية بشكل مفصل ومفيد.'
            ],
            [
                'role' => 'user',
                'content' => $message
            ]
        ],
        'max_tokens' => 500,
        'temperature' => 0.7
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $api_url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $api_key,
        'HTTP-Referer: http://localhost/Mohassebk',
        'X-Title: Mohassebk AI Assistant'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => $http_code === 200 && $response !== false,
        'http_code' => $http_code,
        'curl_error' => $curl_error,
        'response' => $response
    ];
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مفتاح API الجديد</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            direction: rtl;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            border: 1px solid;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }
        .response-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔑 اختبار مفتاح API الجديد</h1>
        
        <div class="info">
            <strong>مفتاح API الجديد:</strong> <?php echo substr($api_key, 0, 25) . '...'; ?><br>
            <strong>النموذج:</strong> <?php echo $model; ?><br>
            <strong>الرابط:</strong> <?php echo $api_url; ?>
        </div>

        <?php if (isset($_POST['test_message'])): ?>
            <div class="test-section">
                <h2>🧪 نتيجة الاختبار</h2>
                
                <?php
                $message = $_POST['test_message'];
                echo "<div class='info'><strong>الرسالة:</strong> " . htmlspecialchars($message) . "</div>";
                
                $start_time = microtime(true);
                $result = testNewAPI($message, $api_key, $api_url, $model);
                $end_time = microtime(true);
                $processing_time = round(($end_time - $start_time) * 1000);
                
                echo "<div class='info'><strong>وقت المعالجة:</strong> {$processing_time} مللي ثانية</div>";
                echo "<div class='info'><strong>HTTP Code:</strong> {$result['http_code']}</div>";
                
                if ($result['curl_error']) {
                    echo "<div class='error'><strong>cURL Error:</strong> {$result['curl_error']}</div>";
                }
                
                if ($result['success']) {
                    $decoded = json_decode($result['response'], true);
                    if ($decoded && isset($decoded['choices'][0]['message']['content'])) {
                        $content = trim($decoded['choices'][0]['message']['content']);
                        echo "<div class='status success'>✅ <strong>نجح الاختبار!</strong> API يعمل بشكل ممتاز</div>";
                        echo "<div class='response-box'><strong>رد الذكاء الاصطناعي:</strong>\n\n" . htmlspecialchars($content) . "</div>";
                        
                        // معلومات إضافية
                        if (isset($decoded['usage'])) {
                            echo "<div class='info'>";
                            echo "<strong>إحصائيات الاستخدام:</strong><br>";
                            echo "• Tokens المستخدمة: " . ($decoded['usage']['total_tokens'] ?? 'غير محدد') . "<br>";
                            echo "• Prompt Tokens: " . ($decoded['usage']['prompt_tokens'] ?? 'غير محدد') . "<br>";
                            echo "• Completion Tokens: " . ($decoded['usage']['completion_tokens'] ?? 'غير محدد');
                            echo "</div>";
                        }
                    } else {
                        echo "<div class='error'>❌ تم الحصول على رد لكن بدون محتوى</div>";
                        echo "<div class='response-box'>" . htmlspecialchars(substr($result['response'], 0, 500)) . "</div>";
                    }
                } else {
                    echo "<div class='error'>❌ فشل الاختبار</div>";
                    echo "<div class='response-box'>" . htmlspecialchars(substr($result['response'], 0, 500)) . "</div>";
                }
                ?>
            </div>
        <?php endif; ?>

        <div class="test-section">
            <h2>📝 اختبار رسالة مخصصة</h2>
            <form method="POST">
                <p>
                    <label><strong>اكتب رسالتك:</strong></label><br>
                    <textarea name="test_message" rows="4" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 6px; font-family: inherit;" required><?php echo isset($_POST['test_message']) ? htmlspecialchars($_POST['test_message']) : 'كيف أحسب ضريبة القيمة المضافة في الجزائر؟'; ?></textarea>
                </p>
                <button type="submit">🚀 اختبار API</button>
            </form>
        </div>

        <div class="test-section">
            <h2>⚡ اختبارات سريعة</h2>
            <p>اختبر API بأسئلة جاهزة:</p>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="test_message" value="مرحبا، أنا أحتاج مساعدة في المحاسبة">
                <button type="submit">👋 تحية</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="test_message" value="كيف أحسب ضريبة القيمة المضافة في الجزائر؟">
                <button type="submit">💰 ضريبة القيمة المضافة</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="test_message" value="ما هي أنواع الشركات في الجزائر وما هي متطلبات كل نوع؟">
                <button type="submit">🏢 أنواع الشركات</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="test_message" value="كيف أسجل شركة جديدة في الجزائر؟ ما هي الخطوات والوثائق المطلوبة؟">
                <button type="submit">📋 تسجيل شركة</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="test_message" value="ما هي الضرائب المطلوبة للشركات في الجزائر ومتى يجب تقديم التصريحات؟">
                <button type="submit">📊 الضرائب</button>
            </form>
        </div>

        <div class="test-section">
            <h2>📋 التوجيهات</h2>
            <ul>
                <li><strong>إذا نجح الاختبار:</strong> مفتاح API يعمل بشكل ممتاز ويمكن استخدامه في المساعد الذكي</li>
                <li><strong>إذا فشل الاختبار:</strong> تحقق من صحة مفتاح API أو الاتصال بالإنترنت</li>
                <li><strong>للاستخدام:</strong> انسخ مفتاح API إلى ملفات الإعدادات في النظام</li>
                <li><strong>للمراقبة:</strong> راقب استهلاك Tokens لتجنب تجاوز الحدود</li>
            </ul>
        </div>
    </div>
</body>
</html>
