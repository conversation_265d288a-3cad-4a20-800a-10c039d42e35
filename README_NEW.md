# نظام المحاسبك - Mohassebk System

## نظام إدارة الخدمات المحاسبية الذكي
### Intelligent Accounting Services Management System

---

## 📋 نظرة عامة / Overview

**المحاسبك** هو نظام شامل لإدارة الخدمات المحاسبية يجمع بين التقنيات الحديثة والذكاء الاصطناعي لتقديم تجربة محاسبية متطورة.

**Mohassebk** is a comprehensive accounting services management system that combines modern technologies with artificial intelligence to provide an advanced accounting experience.

### ✨ المميزات الرئيسية / Key Features

- 🔐 **نظام مصادقة متقدم** - Advanced Authentication System
- 👥 **إدارة متعددة المستويات** - Multi-level User Management  
- 📊 **لوحات تحكم تفاعلية** - Interactive Dashboards
- 📁 **نظام إدارة المستندات** - Document Management System
- 🤖 **مساعد ذكي بالذكاء الاصطناعي** - AI-Powered Assistant
- 💳 **نظام دفع متكامل** - Integrated Payment System
- 📱 **تصميم متجاوب** - Responsive Design
- 🔒 **أمان عالي المستوى** - High-Level Security

---

## 🏗️ البنية التقنية / Technical Architecture

### التقنيات المستخدمة / Technologies Used

#### Backend
- **PHP 7.4+** - Server-side programming
- **MySQL 5.7+** - Database management
- **PDO** - Database abstraction layer

#### Frontend  
- **HTML5 & CSS3** - Structure and styling
- **Tailwind CSS** - Utility-first CSS framework
- **JavaScript (ES6+)** - Client-side interactivity
- **Font Awesome** - Icon library

#### AI Integration
- **OpenRouter API** - AI service provider
- **Claude 3 Haiku** - Language model for assistance

---

## 🚀 التثبيت السريع / Quick Installation

### المتطلبات / Requirements
- PHP 7.4+
- MySQL 5.7+
- Apache/Nginx
- Composer (optional)

### خطوات التثبيت / Installation Steps

1. **استنساخ المشروع / Clone the project**
```bash
git clone https://github.com/your-repo/mohassebk.git
cd mohassebk
```

2. **إعداد قاعدة البيانات / Setup database**
```sql
CREATE DATABASE mohassebk_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
mysql -u root -p mohassebk_db < database/mohassebk_db.sql
```

3. **تكوين النظام / Configure system**
```bash
cp backend/config/config.example.php backend/config/config.php
# Edit config.php with your database credentials
```

4. **إعداد الصلاحيات / Set permissions**
```bash
chmod -R 755 .
chmod -R 777 uploads/
chmod -R 777 logs/
```

5. **اختبار النظام / Test system**
```
Visit: http://localhost/mohassebk/tests/system-test.php
```

---

## 👥 أنواع المستخدمين / User Types

### 🔧 المدير / Administrator
- إدارة شاملة للنظام
- مراقبة جميع العمليات
- إدارة المستخدمين والخدمات
- عرض التقارير والإحصائيات

### 📊 المحاسب / Accountant  
- معالجة طلبات العملاء
- إدارة المستندات
- تحديث حالة الطلبات
- استخدام أدوات الذكاء الاصطناعي

### 👤 العميل / Client
- طلب الخدمات المحاسبية
- رفع المستندات
- متابعة حالة الطلبات
- إجراء المدفوعات

---

## 🤖 الذكاء الاصطناعي / AI Features

### المساعد الذكي / AI Assistant
- إجابة الأسئلة المحاسبية
- تقديم النصائح المالية
- شرح القوانين الضريبية
- مساعدة في اتخاذ القرارات

### تحليل المستندات / Document Analysis
- تحليل تلقائي للمستندات المرفوعة
- استخراج المعلومات المهمة
- تصنيف أنواع المستندات
- اقتراح الإجراءات المطلوبة

### توصيات الخدمات / Service Recommendations
- تحليل احتياجات العميل
- اقتراح الخدمات المناسبة
- تحسين تجربة المستخدم
- زيادة الكفاءة التشغيلية

---

## 💳 نظام الدفع / Payment System

### المميزات / Features
- دفع آمن ومشفر
- دعم متعدد لطرق الدفع
- معالجة فورية للمدفوعات
- سجل شامل للمعاملات

### طرق الدفع المدعومة / Supported Payment Methods
- 💳 البطاقات الائتمانية / Credit Cards
- 🏦 التحويل البنكي / Bank Transfer
- 💰 PayPal
- 📱 المحافظ الرقمية / Digital Wallets

---

## 🔒 الأمان / Security

### إجراءات الأمان / Security Measures
- تشفير كلمات المرور باستخدام bcrypt
- حماية من هجمات SQL Injection
- تشفير البيانات الحساسة
- جلسات آمنة ومحدودة الوقت
- تسجيل شامل للعمليات

---

## 🧪 الاختبار / Testing

### تشغيل الاختبارات / Running Tests
```bash
# اختبار شامل للنظام
php tests/system-test.php

# اختبار قاعدة البيانات
php tests/database-test.php
```

### أنواع الاختبارات / Test Types
- ✅ اختبارات الوحدة / Unit Tests
- ✅ اختبارات التكامل / Integration Tests  
- ✅ اختبارات الأمان / Security Tests
- ✅ اختبارات الأداء / Performance Tests

---

## 📚 التوثيق / Documentation

### الأدلة المتاحة / Available Guides
- 📖 دليل المستخدم / User Guide
- 🔧 دليل المطور / Developer Guide  
- 🚀 [دليل النشر](DEPLOYMENT.md) / Deployment Guide
- 🔌 دليل API / API Guide

---

## 🤝 المساهمة / Contributing

نرحب بمساهماتكم في تطوير النظام!

We welcome your contributions to system development!

### كيفية المساهمة / How to Contribute
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push للفرع
5. إنشاء Pull Request

---

## 📄 الترخيص / License

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 📞 التواصل والدعم / Contact & Support

### معلومات التواصل / Contact Information
- 📧 **البريد الإلكتروني**: <EMAIL>
- 📱 **الهاتف**: +966-XX-XXX-XXXX
- 🌐 **الموقع**: https://mohassebk.com
- 💬 **الدعم الفني**: https://mohassebk.com/support

### ساعات الدعم / Support Hours
- **الأحد - الخميس**: 9:00 ص - 6:00 م (توقيت الرياض)
- **الجمعة - السبت**: دعم طوارئ فقط





### الإصدارات القادمة / Upcoming Releases

#### v2.0 (Q2 2024)
- تطبيق الهاتف المحمول
- تكامل مع البنوك السعودية
- تقارير متقدمة بالذكاء الاصطناعي

#### v2.1 (Q3 2024)  
- دعم اللغة الإنجليزية
- تكامل مع أنظمة ERP
- أتمتة العمليات المحاسبية
