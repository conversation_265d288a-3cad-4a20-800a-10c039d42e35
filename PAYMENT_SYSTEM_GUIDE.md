# 💳 دليل نظام الدفع - محاسبك الرقمي
# Payment System Guide - Mohassebk Digital

## 🎯 نظرة عامة على النظام
### System Overview

نظام الدفع في محاسبك الرقمي هو نظام **تجريبي (Mock Payment)** مصمم للاختبار والعرض التوضيحي. يحاكي عملية الدفع الحقيقية دون معالجة مدفوعات فعلية.

---

## 🔄 تدفق عملية الدفع
### Payment Flow

### 1. **طلب الخدمة**
```
العميل → services/request.php → إنشاء طلب جديد → توجيه لصفحة الدفع
```

### 2. **صفحة الدفع**
```
payment/checkout.php → عرض تفاصيل الطلب → نموذج الدفع → معالجة الدفع
```

### 3. **نجاح الدفع**
```
payment/success.php → تأكيد الدفع → إنشاء سجل دفع → إشعارات
```

---

## 📁 ملفات النظام
### System Files

### **الملفات الأساسية:**
- `payment/checkout.php` - صفحة الدفع الرئيسية
- `payment/success.php` - صفحة نجاح الدفع
- `dashboard/client/payments.php` - سجل المدفوعات للعميل

### **قاعدة البيانات:**
- جدول `payments` - سجلات المدفوعات
- جدول `service_requests` - طلبات الخدمات مع حالة الدفع

---

## 🗄️ هيكل قاعدة البيانات
### Database Structure

### جدول `payments`:
```sql
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    request_id INT NOT NULL,
    user_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('credit_card', 'bank_transfer', 'paypal', 'cash'),
    transaction_id VARCHAR(100) UNIQUE,
    payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    payment_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (request_id) REFERENCES service_requests(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### جدول `service_requests` (أعمدة الدفع):
```sql
payment_status ENUM('unpaid', 'paid', 'partial') DEFAULT 'unpaid'
```

---

## 💰 طرق الدفع المدعومة
### Supported Payment Methods

### 1. **بطاقة ائتمان (Credit Card)**
- محاكاة نموذج بطاقة ائتمان
- التحقق من صحة رقم البطاقة (تجريبي)
- رموز CVV وتاريخ الانتهاء

### 2. **تحويل بنكي (Bank Transfer)**
- معلومات الحساب البنكي
- رقم المرجع

### 3. **PayPal**
- محاكاة دفع PayPal
- إعادة توجيه تجريبية

---

## 🔧 كيفية عمل النظام
### How the System Works

### **1. إنشاء طلب خدمة:**
```php
// في services/request.php
$request_id = $database->insert('service_requests', $request_data);

// توجيه لصفحة الدفع
redirect(getUrl('payment/checkout.php?request_id=' . $request_id));
```

### **2. معالجة الدفع:**
```php
// في payment/checkout.php
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // إنشاء معرف معاملة فريد
    $transaction_id = 'TXN_' . time() . '_' . rand(1000, 9999);
    
    // حفظ بيانات الدفع
    $payment_data = [
        'request_id' => $request_id,
        'user_id' => $user['id'],
        'amount' => $request['total_amount'],
        'payment_method' => $_POST['payment_method'],
        'transaction_id' => $transaction_id,
        'payment_status' => 'completed', // تجريبي - دائماً ناجح
        'payment_date' => date('Y-m-d H:i:s')
    ];
    
    $database->insert('payments', $payment_data);
    
    // تحديث حالة الطلب
    $database->update('service_requests', 
        ['payment_status' => 'paid'], 
        ['id' => $request_id]
    );
}
```

### **3. عرض النتيجة:**
```php
// في payment/success.php
// عرض تفاصيل الدفع الناجح
// إنشاء إشعارات
// روابط للعودة أو طباعة الإيصال
```

---

## 🎨 واجهة المستخدم
### User Interface

### **صفحة الدفع تتضمن:**
- ✅ تفاصيل الطلب والخدمة
- ✅ المبلغ المطلوب بالدينار الجزائري
- ✅ نماذج طرق الدفع المختلفة
- ✅ تشفير تجريبي للبيانات الحساسة
- ✅ رسائل التأكيد والأخطاء

### **سجل المدفوعات يعرض:**
- ✅ جميع المعاملات المالية
- ✅ حالة كل دفعة (مكتمل/معلق/فاشل)
- ✅ تفاصيل الطلب المرتبط
- ✅ إمكانية طباعة الإيصالات
- ✅ إعادة المحاولة للدفعات الفاشلة

---

## 🔗 نقاط الربط في النظام
### Integration Points

### **1. في لوحة تحكم العميل:**
```php
// dashboard/client/index.php
// زر الدفع للطلبات غير المدفوعة
<?php if ($request['payment_status'] !== 'paid'): ?>
    <a href="<?php echo getUrl('payment/checkout.php?request_id=' . $request['id']); ?>">
        <i class="fas fa-credit-card"></i> ادفع الآن
    </a>
<?php endif; ?>
```

### **2. في navbar:**
```php
// frontend/components/navbar.php
<a href="<?php echo getUrl('dashboard/client/payments.php'); ?>">
    <i class="fas fa-credit-card"></i> المدفوعات
</a>
```

### **3. في صفحة طلب الخدمة:**
```php
// services/request.php
// توجيه تلقائي لصفحة الدفع بعد إنشاء الطلب
redirect(getUrl('payment/checkout.php?request_id=' . $request_id));
```

---

## 🧪 بيانات الاختبار
### Test Data

### **بطاقات ائتمان تجريبية:**
- `4111 1111 1111 1111` - Visa (ناجحة)
- `4000 0000 0000 0002` - Visa (فاشلة)
- `5555 5555 5555 4444` - Mastercard (ناجحة)

### **معلومات تجريبية:**
- CVV: `123` أو `456`
- تاريخ الانتهاء: أي تاريخ مستقبلي
- الاسم: أي اسم

---

## 🚀 التطوير المستقبلي
### Future Development

### **لتحويل النظام لدفع حقيقي:**

1. **ربط بوابة دفع حقيقية:**
   - PayPal API
   - Stripe
   - Square
   - بوابات دفع محلية جزائرية

2. **تحسينات الأمان:**
   - تشفير SSL إجباري
   - تشفير بيانات البطاقات
   - التحقق من PCI DSS

3. **ميزات إضافية:**
   - الدفع بالتقسيط
   - الخصومات والكوبونات
   - تقارير مالية متقدمة
   - إشعارات SMS/Email

---

## 🔒 الأمان
### Security

### **الإجراءات الحالية:**
- ✅ التحقق من صحة البيانات
- ✅ منع SQL Injection
- ✅ التحقق من صلاحيات المستخدم
- ✅ تشفير كلمات المرور

### **للإنتاج الحقيقي:**
- 🔄 HTTPS إجباري
- 🔄 تشفير بيانات البطاقات
- 🔄 مراجعة أمنية شاملة
- 🔄 سجلات مراجعة مفصلة

---

## 📞 الدعم والصيانة
### Support & Maintenance

### **ملفات السجل:**
- `logs/payment_errors.log` - أخطاء الدفع
- `logs/transactions.log` - سجل المعاملات

### **المراقبة:**
- معدل نجاح المعاملات
- أخطاء الدفع الشائعة
- أداء صفحات الدفع

---

## 🎯 الخلاصة
### Summary

نظام الدفع الحالي هو **نظام تجريبي متكامل** يوفر:
- ✅ تجربة مستخدم سلسة
- ✅ واجهات جميلة ومتجاوبة
- ✅ إدارة شاملة للمدفوعات
- ✅ تكامل مع باقي أجزاء النظام
- ✅ قابلية التطوير للدفع الحقيقي

**للاستخدام الحقيقي:** يحتاج ربط بوابة دفع حقيقية وتحسينات أمنية إضافية.
