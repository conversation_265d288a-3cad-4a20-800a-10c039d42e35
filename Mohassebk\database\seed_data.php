<?php
/**
 * ملف إضافة البيانات التجريبية العشوائية
 * Random Test Data Seeder
 */

require_once __DIR__ . '/../backend/config/db.php';

class DataSeeder {
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * تشغيل جميع البيانات التجريبية
     */
    public function run() {
        echo "بدء إضافة البيانات التجريبية...\n";
        
        $this->seedUsers();
        $this->seedServices();
        $this->seedServiceRequests();
        $this->seedNotifications();
        $this->seedPayments();
        $this->seedAIConversations();
        $this->seedSystemSettings();
        
        echo "تم إضافة جميع البيانات التجريبية بنجاح!\n";
    }
    
    /**
     * إضافة مستخدمين تجريبيين
     */
    private function seedUsers() {
        echo "إضافة المستخدمين...\n";
        
        $users = [
            [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'full_name' => 'مدير النظام',
                'phone' => '+************',
                'role' => 'admin',
                'company_name' => 'محاسبك الرقمي',
                'is_active' => 1,
                'email_verified' => 1
            ],
            [
                'username' => 'accountant1',
                'email' => '<EMAIL>',
                'password' => password_hash('acc123', PASSWORD_DEFAULT),
                'full_name' => 'أحمد محمد المحاسب',
                'phone' => '+************',
                'role' => 'accountant',
                'company_name' => 'مكتب المحاسبة المتقدم',
                'is_active' => 1,
                'email_verified' => 1
            ],
            [
                'username' => 'accountant2',
                'email' => '<EMAIL>',
                'password' => password_hash('acc123', PASSWORD_DEFAULT),
                'full_name' => 'فاطمة علي المحاسبة',
                'phone' => '+************',
                'role' => 'accountant',
                'company_name' => 'مكتب الخبرة المحاسبية',
                'is_active' => 1,
                'email_verified' => 1
            ]
        ];
        
        // إضافة عملاء عشوائيين
        $client_names = [
            'محمد بن أحمد', 'فاطمة بنت علي', 'عبد الرحمن الجزائري', 'خديجة المغربية',
            'يوسف التونسي', 'عائشة السورية', 'عمر المصري', 'زينب اللبنانية',
            'حسن العراقي', 'مريم الأردنية', 'سعد السعودي', 'نور الكويتية'
        ];
        
        $companies = [
            'شركة التجارة العامة', 'مؤسسة الخدمات المتكاملة', 'شركة التكنولوجيا الحديثة',
            'مكتب الاستشارات القانونية', 'شركة البناء والتعمير', 'مؤسسة التسويق الرقمي',
            'شركة الصناعات الغذائية', 'مكتب الهندسة المعمارية', 'شركة النقل واللوجستيك',
            'مؤسسة التعليم والتدريب', 'شركة الطاقة المتجددة', 'مكتب المحاماة والقانون'
        ];
        
        for ($i = 0; $i < 15; $i++) {
            $users[] = [
                'username' => 'client' . ($i + 1),
                'email' => 'client' . ($i + 1) . '@example.com',
                'password' => password_hash('client123', PASSWORD_DEFAULT),
                'full_name' => $client_names[array_rand($client_names)],
                'phone' => '+21355500' . str_pad($i + 10, 4, '0', STR_PAD_LEFT),
                'role' => 'client',
                'company_name' => $companies[array_rand($companies)],
                'tax_number' => '40' . str_pad(rand(1000000, 9999999), 7, '0', STR_PAD_LEFT),
                'address' => 'الجزائر العاصمة، الجزائر',
                'is_active' => 1,
                'email_verified' => rand(0, 1)
            ];
        }
        
        foreach ($users as $user) {
            $sql = "INSERT IGNORE INTO users (username, email, password, full_name, phone, role, company_name, tax_number, address, is_active, email_verified) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            $this->pdo->prepare($sql)->execute([
                $user['username'], $user['email'], $user['password'], $user['full_name'],
                $user['phone'], $user['role'], $user['company_name'], 
                $user['tax_number'] ?? null, $user['address'] ?? null,
                $user['is_active'], $user['email_verified']
            ]);
        }
        
        echo "تم إضافة " . count($users) . " مستخدم\n";
    }
    
    /**
     * إضافة خدمات تجريبية
     */
    private function seedServices() {
        echo "إضافة الخدمات...\n";
        
        $services = [
            [
                'name' => 'إعداد الدفاتر المحاسبية',
                'description' => 'إعداد وتنظيم الدفاتر المحاسبية للشركات والمؤسسات',
                'category' => 'محاسبة',
                'price' => 15000.00,
                'duration_days' => 7,
                'is_active' => 1
            ],
            [
                'name' => 'إعداد التصريحات الضريبية',
                'description' => 'إعداد وتقديم التصريحات الضريبية الشهرية والسنوية',
                'category' => 'ضرائب',
                'price' => 8000.00,
                'duration_days' => 3,
                'is_active' => 1
            ],
            [
                'name' => 'مراجعة الحسابات',
                'description' => 'مراجعة وتدقيق الحسابات المالية للشركات',
                'category' => 'تدقيق',
                'price' => 25000.00,
                'duration_days' => 14,
                'is_active' => 1
            ],
            [
                'name' => 'إعداد الميزانية العمومية',
                'description' => 'إعداد الميزانية العمومية وقائمة الدخل',
                'category' => 'تقارير مالية',
                'price' => 12000.00,
                'duration_days' => 5,
                'is_active' => 1
            ],
            [
                'name' => 'استشارات ضريبية',
                'description' => 'تقديم الاستشارات الضريبية والقانونية',
                'category' => 'استشارات',
                'price' => 5000.00,
                'duration_days' => 1,
                'is_active' => 1
            ],
            [
                'name' => 'إعداد كشوف المرتبات',
                'description' => 'إعداد كشوف المرتبات والتأمينات الاجتماعية',
                'category' => 'رواتب',
                'price' => 3000.00,
                'duration_days' => 2,
                'is_active' => 1
            ]
        ];
        
        foreach ($services as $service) {
            $sql = "INSERT IGNORE INTO services (service_name_ar, service_name, description_ar, description, category, base_price, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            $this->pdo->prepare($sql)->execute([
                $service['name'], // service_name_ar
                $service['name'], // service_name (نفس الاسم العربي مؤقتاً)
                $service['description'], // description_ar
                $service['description'], // description (نفس الوصف العربي مؤقتاً)
                $service['category'],
                $service['price'], // base_price
                $service['is_active']
            ]);
        }
        
        echo "تم إضافة " . count($services) . " خدمة\n";
    }
    
    /**
     * إضافة طلبات خدمات تجريبية
     */
    private function seedServiceRequests() {
        echo "إضافة طلبات الخدمات...\n";
        
        $statuses = ['pending', 'in_progress', 'under_review', 'completed', 'cancelled'];
        $priorities = ['low', 'medium', 'high', 'urgent'];
        
        // الحصول على معرفات المستخدمين والخدمات
        $users = $this->pdo->query("SELECT id FROM users WHERE role = 'client'")->fetchAll();
        $services = $this->pdo->query("SELECT id, name, price FROM services")->fetchAll();
        $accountants = $this->pdo->query("SELECT id FROM users WHERE role = 'accountant'")->fetchAll();
        
        for ($i = 0; $i < 30; $i++) {
            $user = $users[array_rand($users)];
            $service = $services[array_rand($services)];
            $accountant = $accountants[array_rand($accountants)];
            $status = $statuses[array_rand($statuses)];
            
            $sql = "INSERT INTO service_requests (user_id, service_id, request_title, description, status, priority, assigned_accountant_id, total_amount, estimated_completion) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL ? DAY))";
            
            $this->pdo->prepare($sql)->execute([
                $user['id'],
                $service['id'],
                'طلب ' . $service['name'],
                'وصف تفصيلي لطلب الخدمة رقم ' . ($i + 1),
                $status,
                $priorities[array_rand($priorities)],
                $accountant['id'],
                $service['price'],
                rand(1, 30)
            ]);
        }
        
        echo "تم إضافة 30 طلب خدمة\n";
    }
    
    /**
     * إضافة إشعارات تجريبية
     */
    private function seedNotifications() {
        echo "إضافة الإشعارات...\n";
        
        $users = $this->pdo->query("SELECT id FROM users")->fetchAll();
        $types = ['info', 'success', 'warning', 'error'];
        
        $notifications = [
            ['title' => 'مرحباً بك في محاسبك الرقمي', 'message' => 'نرحب بك في منصتنا للخدمات المحاسبية', 'type' => 'success'],
            ['title' => 'تم قبول طلبك', 'message' => 'تم قبول طلب الخدمة وسيتم البدء في المعالجة قريباً', 'type' => 'success'],
            ['title' => 'تذكير بالدفع', 'message' => 'يرجى إكمال عملية الدفع لطلب الخدمة', 'type' => 'warning'],
            ['title' => 'تم إكمال الخدمة', 'message' => 'تم إكمال طلب الخدمة بنجاح', 'type' => 'success'],
            ['title' => 'تحديث في النظام', 'message' => 'تم تحديث النظام بميزات جديدة', 'type' => 'info']
        ];
        
        for ($i = 0; $i < 50; $i++) {
            $user = $users[array_rand($users)];
            $notification = $notifications[array_rand($notifications)];
            
            $sql = "INSERT INTO notifications (user_id, title, message, type, is_read) VALUES (?, ?, ?, ?, ?)";
            $this->pdo->prepare($sql)->execute([
                $user['id'],
                $notification['title'],
                $notification['message'],
                $notification['type'],
                rand(0, 1)
            ]);
        }
        
        echo "تم إضافة 50 إشعار\n";
    }
    
    /**
     * إضافة مدفوعات تجريبية
     */
    private function seedPayments() {
        echo "إضافة المدفوعات...\n";
        
        $requests = $this->pdo->query("SELECT id, user_id, total_amount FROM service_requests")->fetchAll();
        $methods = ['credit_card', 'bank_transfer', 'paypal', 'cash'];
        $statuses = ['pending', 'completed', 'failed'];
        
        foreach ($requests as $request) {
            if (rand(0, 1)) { // 50% احتمال وجود دفعة
                $sql = "INSERT INTO payments (request_id, user_id, amount, payment_method, transaction_id, payment_status) 
                        VALUES (?, ?, ?, ?, ?, ?)";
                
                $this->pdo->prepare($sql)->execute([
                    $request['id'],
                    $request['user_id'],
                    $request['total_amount'],
                    $methods[array_rand($methods)],
                    'TXN' . time() . rand(1000, 9999),
                    $statuses[array_rand($statuses)]
                ]);
            }
        }
        
        echo "تم إضافة المدفوعات\n";
    }
    
    /**
     * إضافة محادثات الذكاء الاصطناعي
     */
    private function seedAIConversations() {
        echo "إضافة محادثات الذكاء الاصطناعي...\n";
        
        $users = $this->pdo->query("SELECT id FROM users")->fetchAll();
        
        $conversations = [
            ['message' => 'ما هي الضرائب المستحقة على الشركات؟', 'response' => 'الضرائب على الشركات في الجزائر تشمل ضريبة أرباح الشركات بنسبة 25% وضريبة القيمة المضافة بنسبة 19%.'],
            ['message' => 'كيف أحسب الضريبة على الدخل؟', 'response' => 'يتم حساب الضريبة على الدخل حسب الشرائح الضريبية المحددة في القانون الجزائري.'],
            ['message' => 'ما هي المستندات المطلوبة للتصريح الضريبي؟', 'response' => 'تشمل المستندات: كشوف الحسابات، الفواتير، كشوف المرتبات، وسجلات المبيعات.'],
            ['message' => 'متى موعد تقديم التصريح السنوي؟', 'response' => 'يجب تقديم التصريح السنوي قبل 31 مارس من كل سنة.']
        ];
        
        for ($i = 0; $i < 100; $i++) {
            $user = $users[array_rand($users)];
            $conversation = $conversations[array_rand($conversations)];
            
            $sql = "INSERT INTO ai_conversations (user_id, session_id, message, response, model_used, tokens_used, processing_time) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $this->pdo->prepare($sql)->execute([
                $user['id'],
                'session_' . time() . '_' . rand(1000, 9999),
                $conversation['message'],
                $conversation['response'],
                'deepseek/deepseek-chat:free',
                rand(50, 200),
                rand(500, 2000) / 1000
            ]);
        }
        
        echo "تم إضافة 100 محادثة ذكاء اصطناعي\n";
    }
    
    /**
     * إضافة إعدادات النظام
     */
    private function seedSystemSettings() {
        echo "إضافة إعدادات النظام...\n";
        
        $settings = [
            ['maintenance_mode', '0', 'boolean', 'وضع الصيانة', 'Maintenance Mode', 0],
            ['max_file_size', '10485760', 'integer', 'الحد الأقصى لحجم الملف', 'Max File Size', 0],
            ['allowed_file_types', 'pdf,doc,docx,xls,xlsx,jpg,png', 'string', 'أنواع الملفات المسموحة', 'Allowed File Types', 0],
            ['ai_enabled', '1', 'boolean', 'تفعيل الذكاء الاصطناعي', 'AI Enabled', 0],
            ['email_notifications', '1', 'boolean', 'إشعارات البريد الإلكتروني', 'Email Notifications', 0]
        ];
        
        foreach ($settings as $setting) {
            $sql = "INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description_ar, description_en, is_public) 
                    VALUES (?, ?, ?, ?, ?, ?)";
            $this->pdo->prepare($sql)->execute($setting);
        }
        
        echo "تم إضافة إعدادات النظام\n";
    }
}

// تشغيل البيانات التجريبية إذا تم استدعاء الملف مباشرة
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    try {
        $seeder = new DataSeeder($pdo);
        $seeder->run();
        echo "\n✅ تم إضافة جميع البيانات التجريبية بنجاح!\n";
    } catch (Exception $e) {
        echo "\n❌ خطأ في إضافة البيانات: " . $e->getMessage() . "\n";
    }
}
?>
