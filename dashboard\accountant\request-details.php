<?php
/**
 * Accountant Request Details
 * تفاصيل الطلب للمحاسب
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'accountant') {
    redirect(getUrl('login.php'));
}

// الحصول على معرف الطلب
$request_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($request_id <= 0) {
    showMessage('معرف الطلب غير صحيح', 'error');
    redirect(getUrl('dashboard/accountant/requests.php'));
}

// الحصول على تفاصيل الطلب
$request = null;
try {
    $request = fetchOne(
        "SELECT sr.*, s.service_name, s.service_name_ar, u.full_name as client_name, 
                u.email as client_email, u.phone as client_phone, u.company_name as client_company
         FROM service_requests sr 
         LEFT JOIN services s ON sr.service_id = s.id 
         LEFT JOIN users u ON sr.user_id = u.id 
         WHERE sr.id = :id AND sr.assigned_accountant_id = :accountant_id",
        ['id' => $request_id, 'accountant_id' => $user['id']]
    );
} catch (Exception $e) {
    logError("Fetch request details error: " . $e->getMessage());
}

if (!$request) {
    showMessage('الطلب غير موجود أو غير مصرح لك بالوصول إليه', 'error');
    redirect(getUrl('dashboard/accountant/requests.php'));
}

// الحصول على المستندات المرتبطة بالطلب
$documents = [];
try {
    $documents = fetchAll(
        "SELECT * FROM documents WHERE request_id = :request_id ORDER BY upload_date DESC",
        ['request_id' => $request_id]
    );
} catch (Exception $e) {
    logError("Fetch documents error: " . $e->getMessage());
}

// الحصول على الرسائل المرتبطة بالطلب
$messages = [];
try {
    $messages = fetchAll(
        "SELECT m.*, u.full_name as sender_name, u.role as sender_role
         FROM messages m 
         LEFT JOIN users u ON m.sender_id = u.id 
         WHERE m.request_id = :request_id 
         ORDER BY m.sent_at ASC",
        ['request_id' => $request_id]
    );
} catch (Exception $e) {
    logError("Fetch messages error: " . $e->getMessage());
}

// معالجة إرسال رسالة جديدة
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['send_message'])) {
    $message_text = sanitize($_POST['message_text']);
    
    if (!empty($message_text)) {
        try {
            $sql = "INSERT INTO messages (request_id, sender_id, receiver_id, message_text, sent_at) 
                    VALUES (:request_id, :sender_id, :receiver_id, :message_text, NOW())";
            executeQuery($sql, [
                'request_id' => $request_id,
                'sender_id' => $user['id'],
                'receiver_id' => $request['user_id'],
                'message_text' => $message_text
            ]);
            
            showMessage('تم إرسال الرسالة بنجاح', 'success');
            redirect(getUrl('dashboard/accountant/request-details.php?id=' . $request_id));
        } catch (Exception $e) {
            showMessage('حدث خطأ أثناء إرسال الرسالة', 'error');
            logError("Send message error: " . $e->getMessage());
        }
    } else {
        showMessage('يرجى كتابة نص الرسالة', 'error');
    }
}

// معالجة تحديث حالة الطلب
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_status'])) {
    $new_status = sanitize($_POST['status']);
    $notes = sanitize($_POST['notes'] ?? '');
    
    try {
        $sql = "UPDATE service_requests SET status = :status, notes = :notes, updated_at = NOW() WHERE id = :id";
        executeQuery($sql, [
            'status' => $new_status,
            'notes' => $notes,
            'id' => $request_id
        ]);
        
        // إذا تم إكمال الطلب، تحديث تاريخ الإكمال
        if ($new_status === 'completed') {
            $sql = "UPDATE service_requests SET actual_completion = NOW() WHERE id = :id";
            executeQuery($sql, ['id' => $request_id]);
        }
        
        // إرسال إشعار للعميل
        $notification_sql = "INSERT INTO notifications (user_id, title, message, type, related_request_id, created_at) 
                            VALUES (:user_id, :title, :message, :type, :request_id, NOW())";
        executeQuery($notification_sql, [
            'user_id' => $request['user_id'],
            'title' => 'تحديث حالة الطلب',
            'message' => 'تم تحديث حالة طلبك: ' . $request['request_title'],
            'type' => 'info',
            'request_id' => $request_id
        ]);
        
        showMessage('تم تحديث حالة الطلب بنجاح', 'success');
        redirect(getUrl('dashboard/accountant/request-details.php?id=' . $request_id));
    } catch (Exception $e) {
        showMessage('حدث خطأ أثناء تحديث الطلب', 'error');
        logError("Update request status error: " . $e->getMessage());
    }
}

// الحصول على رسالة الفلاش
$flash_message = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الطلب - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($flash_message): ?>
            <div class="mb-6 p-4 rounded-lg <?php echo $flash_message['type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                <div class="flex items-center">
                    <i class="fas <?php echo $flash_message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($flash_message['message']); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-earth-green mb-2">تفاصيل الطلب</h1>
                    <p class="text-gray-600">عرض وإدارة تفاصيل الطلب المحاسبي</p>
                </div>
                <div class="flex space-x-3 space-x-reverse">
                    <a href="<?php echo getUrl('dashboard/accountant/requests.php'); ?>" 
                       class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للطلبات
                    </a>
                    <button onclick="openStatusModal()" 
                            class="bg-earth-green text-white px-4 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                        <i class="fas fa-edit ml-2"></i>
                        تحديث الحالة
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Request Details -->
        <div class="grid lg:grid-cols-3 gap-8 mb-8">
            
            <!-- Main Request Info -->
            <div class="lg:col-span-2 bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-semibold text-earth-green">معلومات الطلب</h2>
                    <?php
                    $status_classes = [
                        'pending' => 'bg-yellow-100 text-yellow-800',
                        'in_progress' => 'bg-blue-100 text-blue-800',
                        'under_review' => 'bg-purple-100 text-purple-800',
                        'completed' => 'bg-green-100 text-green-800',
                        'cancelled' => 'bg-red-100 text-red-800'
                    ];
                    
                    $status_text = [
                        'pending' => 'معلق',
                        'in_progress' => 'قيد التنفيذ',
                        'under_review' => 'قيد المراجعة',
                        'completed' => 'مكتمل',
                        'cancelled' => 'ملغي'
                    ];
                    ?>
                    <span class="px-4 py-2 rounded-full text-sm font-semibold <?php echo $status_classes[$request['status']] ?? 'bg-gray-100 text-gray-800'; ?>">
                        <?php echo $status_text[$request['status']] ?? $request['status']; ?>
                    </span>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-2">عنوان الطلب</h3>
                        <p class="text-gray-700"><?php echo htmlspecialchars($request['request_title']); ?></p>
                    </div>
                    
                    <?php if (!empty($request['description'])): ?>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">وصف الطلب</h3>
                            <p class="text-gray-700 whitespace-pre-wrap"><?php echo htmlspecialchars($request['description']); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">الخدمة المطلوبة</h3>
                            <p class="text-gray-700"><?php echo htmlspecialchars($request['service_name_ar'] ?? 'خدمة محاسبية'); ?></p>
                        </div>
                        
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">المبلغ المتوقع</h3>
                            <p class="text-gray-700 font-semibold text-earth-green"><?php echo formatCurrency($request['total_amount']); ?></p>
                        </div>
                        
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">الأولوية</h3>
                            <?php
                            $priority_classes = [
                                'low' => 'bg-gray-100 text-gray-800',
                                'medium' => 'bg-blue-100 text-blue-800',
                                'high' => 'bg-orange-100 text-orange-800',
                                'urgent' => 'bg-red-100 text-red-800'
                            ];
                            
                            $priority_text = [
                                'low' => 'منخفضة',
                                'medium' => 'متوسطة',
                                'high' => 'عالية',
                                'urgent' => 'عاجلة'
                            ];
                            ?>
                            <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo $priority_classes[$request['priority']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                <?php echo $priority_text[$request['priority']] ?? $request['priority']; ?>
                            </span>
                        </div>
                        
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">تاريخ الإنشاء</h3>
                            <p class="text-gray-700"><?php echo formatArabicDate($request['created_at']); ?></p>
                        </div>
                    </div>
                    
                    <?php if (!empty($request['notes'])): ?>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">ملاحظات المحاسب</h3>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <p class="text-gray-700 whitespace-pre-wrap"><?php echo htmlspecialchars($request['notes']); ?></p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <!-- Client Info -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-earth-green mb-6">معلومات العميل</h2>
                
                <div class="space-y-4">
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-2">الاسم الكامل</h3>
                        <p class="text-gray-700"><?php echo htmlspecialchars($request['client_name'] ?? 'غير محدد'); ?></p>
                    </div>
                    
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-2">البريد الإلكتروني</h3>
                        <p class="text-gray-700">
                            <a href="mailto:<?php echo htmlspecialchars($request['client_email']); ?>" 
                               class="text-earth-green hover:text-sand-brown">
                                <?php echo htmlspecialchars($request['client_email']); ?>
                            </a>
                        </p>
                    </div>
                    
                    <?php if (!empty($request['client_phone'])): ?>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">رقم الهاتف</h3>
                            <p class="text-gray-700">
                                <a href="tel:<?php echo htmlspecialchars($request['client_phone']); ?>" 
                                   class="text-earth-green hover:text-sand-brown">
                                    <?php echo htmlspecialchars($request['client_phone']); ?>
                                </a>
                            </p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($request['client_company'])): ?>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">اسم الشركة</h3>
                            <p class="text-gray-700"><?php echo htmlspecialchars($request['client_company']); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Quick Actions -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <h3 class="font-semibold text-gray-900 mb-3">إجراءات سريعة</h3>
                    <div class="space-y-2">
                        <a href="mailto:<?php echo htmlspecialchars($request['client_email']); ?>" 
                           class="w-full bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors text-center block">
                            <i class="fas fa-envelope ml-2"></i>
                            إرسال بريد إلكتروني
                        </a>
                        
                        <?php if (!empty($request['client_phone'])): ?>
                            <a href="tel:<?php echo htmlspecialchars($request['client_phone']); ?>" 
                               class="w-full bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors text-center block">
                                <i class="fas fa-phone ml-2"></i>
                                اتصال هاتفي
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Documents and Messages -->
        <div class="grid lg:grid-cols-2 gap-8">

            <!-- Documents -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-earth-green mb-6">
                    <i class="fas fa-folder ml-2"></i>
                    المستندات المرفقة (<?php echo count($documents); ?>)
                </h2>

                <?php if (empty($documents)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-file-upload text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600">لا توجد مستندات مرفقة</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($documents as $document): ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <?php
                                        $file_icon = 'fa-file';
                                        $icon_color = 'text-gray-500';

                                        switch (strtolower($document['file_type'])) {
                                            case 'pdf':
                                                $file_icon = 'fa-file-pdf';
                                                $icon_color = 'text-red-500';
                                                break;
                                            case 'jpg':
                                            case 'jpeg':
                                            case 'png':
                                            case 'gif':
                                                $file_icon = 'fa-file-image';
                                                $icon_color = 'text-blue-500';
                                                break;
                                            case 'doc':
                                            case 'docx':
                                                $file_icon = 'fa-file-word';
                                                $icon_color = 'text-blue-600';
                                                break;
                                            case 'xls':
                                            case 'xlsx':
                                                $file_icon = 'fa-file-excel';
                                                $icon_color = 'text-green-600';
                                                break;
                                        }
                                        ?>
                                        <i class="fas <?php echo $file_icon; ?> text-2xl <?php echo $icon_color; ?> ml-3"></i>
                                        <div>
                                            <h3 class="font-semibold text-gray-900">
                                                <?php echo htmlspecialchars($document['original_filename']); ?>
                                            </h3>
                                            <p class="text-sm text-gray-500">
                                                <?php echo number_format($document['file_size'] / 1024, 1); ?> KB •
                                                <?php echo formatArabicDate($document['upload_date']); ?>
                                            </p>
                                        </div>
                                    </div>

                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <?php if ($document['is_processed']): ?>
                                            <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                                معالج
                                            </span>
                                        <?php else: ?>
                                            <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                                                غير معالج
                                            </span>
                                        <?php endif; ?>

                                        <a href="<?php echo getUrl('backend/view-document.php?id=' . $document['id']); ?>"
                                           target="_blank"
                                           class="text-earth-green hover:text-sand-brown transition-colors"
                                           title="عرض الملف">
                                            <i class="fas fa-eye"></i>
                                        </a>

                                        <a href="<?php echo getUrl('backend/download-document.php?id=' . $document['id']); ?>"
                                           class="text-blue-600 hover:text-blue-800 transition-colors"
                                           title="تحميل الملف">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </div>
                                </div>

                                <?php if (!empty($document['ai_analysis'])): ?>
                                    <div class="mt-3 p-3 bg-purple-50 rounded-lg">
                                        <h4 class="text-sm font-semibold text-purple-800 mb-1">تحليل الذكاء الاصطناعي:</h4>
                                        <p class="text-sm text-purple-700"><?php echo htmlspecialchars(substr($document['ai_analysis'], 0, 150)) . (strlen($document['ai_analysis']) > 150 ? '...' : ''); ?></p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Messages -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-earth-green mb-6">
                    <i class="fas fa-comments ml-2"></i>
                    المحادثات (<?php echo count($messages); ?>)
                </h2>

                <!-- Messages List -->
                <div class="space-y-4 mb-6 max-h-96 overflow-y-auto">
                    <?php if (empty($messages)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-comments text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600">لا توجد رسائل</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($messages as $message): ?>
                            <div class="<?php echo $message['sender_id'] == $user['id'] ? 'text-left' : 'text-right'; ?>">
                                <div class="inline-block max-w-xs lg:max-w-md px-4 py-2 rounded-lg <?php echo $message['sender_id'] == $user['id'] ? 'bg-earth-green text-white' : 'bg-gray-100 text-gray-900'; ?>">
                                    <p class="text-sm"><?php echo htmlspecialchars($message['message_text']); ?></p>
                                    <p class="text-xs mt-1 <?php echo $message['sender_id'] == $user['id'] ? 'text-green-100' : 'text-gray-500'; ?>">
                                        <?php echo htmlspecialchars($message['sender_name']); ?> •
                                        <?php echo formatArabicDate($message['sent_at']); ?>
                                    </p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Send Message Form -->
                <form method="POST" class="border-t border-gray-200 pt-4">
                    <input type="hidden" name="send_message" value="1">

                    <div class="flex space-x-3 space-x-reverse">
                        <textarea name="message_text"
                                  rows="2"
                                  class="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green resize-none"
                                  placeholder="اكتب رسالتك هنا..."
                                  required></textarea>

                        <button type="submit"
                                class="bg-earth-green text-white px-4 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Status Update Modal -->
    <div id="statusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">تحديث حالة الطلب</h3>
                        <button onclick="closeStatusModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form method="POST">
                        <input type="hidden" name="update_status" value="1">

                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">الحالة الجديدة</label>
                            <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green">
                                <option value="pending" <?php echo $request['status'] === 'pending' ? 'selected' : ''; ?>>معلق</option>
                                <option value="in_progress" <?php echo $request['status'] === 'in_progress' ? 'selected' : ''; ?>>قيد التنفيذ</option>
                                <option value="under_review" <?php echo $request['status'] === 'under_review' ? 'selected' : ''; ?>>قيد المراجعة</option>
                                <option value="completed" <?php echo $request['status'] === 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                            </select>
                        </div>

                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات</label>
                            <textarea name="notes"
                                      rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green"
                                      placeholder="أضف ملاحظات حول تحديث الحالة..."><?php echo htmlspecialchars($request['notes'] ?? ''); ?></textarea>
                        </div>

                        <div class="flex justify-end space-x-3 space-x-reverse">
                            <button type="button"
                                    onclick="closeStatusModal()"
                                    class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                                إلغاء
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 bg-earth-green text-white rounded-lg hover:bg-sand-brown transition-colors">
                                تحديث الحالة
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>

    <script>
        function openStatusModal() {
            document.getElementById('statusModal').classList.remove('hidden');
        }

        function closeStatusModal() {
            document.getElementById('statusModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('statusModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeStatusModal();
            }
        });

        // Auto-scroll messages to bottom
        const messagesContainer = document.querySelector('.max-h-96.overflow-y-auto');
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    </script>
</body>
</html>
