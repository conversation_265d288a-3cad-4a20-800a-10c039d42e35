/**
 * محاسبك الرقمي - ملف الجافاسكريبت الرئيسي
 * Digital Accountant - Main JavaScript File
 */

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // تهيئة الأنيميشن
    initializeAnimations();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة التنبيهات
    initializeAlerts();
    
    // تهيئة التحميل التدريجي للصور
    initializeLazyLoading();
    
    // تهيئة التمرير السلس
    initializeSmoothScrolling();
    
    console.log('تم تحميل محاسبك الرقمي بنجاح');
}

/**
 * تهيئة الأنيميشن
 */
function initializeAnimations() {
    // تأثير الظهور التدريجي للعناصر
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // مراقبة العناصر التي تحتوي على كلاس fade-in
    document.querySelectorAll('.fade-in').forEach(el => {
        observer.observe(el);
    });
    
    // تأثير hover للبطاقات
    document.querySelectorAll('.card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
    // التحقق من صحة النماذج
    const forms = document.querySelectorAll('form[data-validate]');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
        
        // التحقق الفوري من الحقول
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                validateField(this);
            });
            
            input.addEventListener('input', function() {
                clearFieldError(this);
            });
        });
    });
    
    // تحسين تجربة رفع الملفات
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            handleFileUpload(this);
        });
    });
}

/**
 * التحقق من صحة النموذج
 */
function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    
    inputs.forEach(input => {
        if (!validateField(input)) {
            isValid = false;
        }
    });
    
    return isValid;
}

/**
 * التحقق من صحة حقل واحد
 */
function validateField(field) {
    const value = field.value.trim();
    const type = field.type;
    const required = field.hasAttribute('required');
    
    // إزالة الأخطاء السابقة
    clearFieldError(field);
    
    // التحقق من الحقول المطلوبة
    if (required && !value) {
        showFieldError(field, 'هذا الحقل مطلوب');
        return false;
    }
    
    // التحقق من البريد الإلكتروني
    if (type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
            showFieldError(field, 'يرجى إدخال بريد إلكتروني صحيح');
            return false;
        }
    }
    
    // التحقق من رقم الهاتف
    if (field.name === 'phone' && value) {
        const phoneRegex = /^[+]?[0-9\s\-\(\)]{10,}$/;
        if (!phoneRegex.test(value)) {
            showFieldError(field, 'يرجى إدخال رقم هاتف صحيح');
            return false;
        }
    }
    
    // التحقق من كلمة المرور
    if (type === 'password' && value) {
        if (value.length < 8) {
            showFieldError(field, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل');
            return false;
        }
    }
    
    // التحقق من تطابق كلمة المرور
    if (field.name === 'confirm_password' && value) {
        const passwordField = field.form.querySelector('input[name="password"]');
        if (passwordField && value !== passwordField.value) {
            showFieldError(field, 'كلمات المرور غير متطابقة');
            return false;
        }
    }
    
    return true;
}

/**
 * إظهار خطأ في الحقل
 */
function showFieldError(field, message) {
    field.classList.add('error');
    
    let errorElement = field.parentNode.querySelector('.form-error');
    if (!errorElement) {
        errorElement = document.createElement('div');
        errorElement.className = 'form-error';
        field.parentNode.appendChild(errorElement);
    }
    
    errorElement.textContent = message;
}

/**
 * إزالة خطأ من الحقل
 */
function clearFieldError(field) {
    field.classList.remove('error');
    
    const errorElement = field.parentNode.querySelector('.form-error');
    if (errorElement) {
        errorElement.remove();
    }
}

/**
 * معالجة رفع الملفات
 */
function handleFileUpload(input) {
    const files = input.files;
    const maxSize = 10 * 1024 * 1024; // 10 MB
    const allowedTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png'];
    
    for (let file of files) {
        // التحقق من حجم الملف
        if (file.size > maxSize) {
            showAlert('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت', 'error');
            input.value = '';
            return;
        }
        
        // التحقق من نوع الملف
        const extension = file.name.split('.').pop().toLowerCase();
        if (!allowedTypes.includes(extension)) {
            showAlert('نوع الملف غير مدعوم', 'error');
            input.value = '';
            return;
        }
    }
    
    // إظهار معلومات الملف
    if (files.length > 0) {
        const fileInfo = document.createElement('div');
        fileInfo.className = 'file-info mt-2 text-sm text-gray-600';
        fileInfo.innerHTML = `
            <i class="fas fa-file mr-2"></i>
            ${files[0].name} (${formatFileSize(files[0].size)})
        `;
        
        // إزالة معلومات الملف السابقة
        const existingInfo = input.parentNode.querySelector('.file-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        
        input.parentNode.appendChild(fileInfo);
    }
}

/**
 * تنسيق حجم الملف
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 بايت';
    
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * تهيئة التنبيهات
 */
function initializeAlerts() {
    // إغلاق التنبيهات تلقائياً
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        // إضافة زر الإغلاق
        if (!alert.querySelector('.alert-close')) {
            const closeBtn = document.createElement('button');
            closeBtn.className = 'alert-close absolute top-2 left-2 text-gray-500 hover:text-gray-700';
            closeBtn.innerHTML = '<i class="fas fa-times"></i>';
            closeBtn.onclick = () => closeAlert(alert);
            
            alert.style.position = 'relative';
            alert.appendChild(closeBtn);
        }
        
        // إغلاق تلقائي بعد 5 ثوان
        setTimeout(() => {
            closeAlert(alert);
        }, 5000);
    });
}

/**
 * إظهار تنبيه
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container') || createAlertContainer();
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} mb-4 opacity-0 transform translate-y-2`;
    alert.innerHTML = `
        ${message}
        <button class="alert-close absolute top-2 left-2 text-gray-500 hover:text-gray-700">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    alert.style.position = 'relative';
    alertContainer.appendChild(alert);
    
    // تأثير الظهور
    setTimeout(() => {
        alert.classList.remove('opacity-0', 'translate-y-2');
        alert.classList.add('opacity-100', 'translate-y-0');
    }, 100);
    
    // إضافة حدث الإغلاق
    alert.querySelector('.alert-close').onclick = () => closeAlert(alert);
    
    // إغلاق تلقائي
    setTimeout(() => {
        closeAlert(alert);
    }, 5000);
}

/**
 * إغلاق التنبيه
 */
function closeAlert(alert) {
    alert.classList.add('opacity-0', 'transform', 'translate-y-2');
    setTimeout(() => {
        if (alert.parentNode) {
            alert.parentNode.removeChild(alert);
        }
    }, 300);
}

/**
 * إنشاء حاوية التنبيهات
 */
function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alert-container';
    container.className = 'fixed top-4 right-4 z-50 max-w-md';
    document.body.appendChild(container);
    return container;
}

/**
 * تهيئة التحميل التدريجي للصور
 */
function initializeLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

/**
 * تهيئة التمرير السلس
 */
function initializeSmoothScrolling() {
    // التمرير السلس للروابط الداخلية
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * دالة مساعدة لإرسال طلبات AJAX
 */
function sendAjaxRequest(url, data, method = 'POST') {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open(method, url, true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        resolve(xhr.responseText);
                    }
                } else {
                    reject(new Error('خطأ في الشبكة'));
                }
            }
        };
        
        xhr.send(JSON.stringify(data));
    });
}

/**
 * دالة لتحديث الوقت بالتوقيت المحلي
 */
function updateLocalTime() {
    const timeElements = document.querySelectorAll('[data-time]');
    
    timeElements.forEach(element => {
        const timestamp = element.dataset.time;
        const date = new Date(timestamp * 1000);
        element.textContent = date.toLocaleString('ar-SA');
    });
}

// تحديث الوقت كل دقيقة
setInterval(updateLocalTime, 60000);

/**
 * دالة لتنسيق الأرقام بالعربية
 */
function formatArabicNumber(number) {
    const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
    return number.toString().replace(/[0-9]/g, (w) => arabicNumbers[+w]);
}

/**
 * دالة لتنسيق العملة
 */
function formatCurrency(amount, currency = 'DZD') {
    const formatter = new Intl.NumberFormat('ar-DZ', {
        style: 'currency',
        currency: currency
    });

    return formatter.format(amount);
}

// تصدير الدوال للاستخدام العام
window.MohassebkApp = {
    showAlert,
    closeAlert,
    validateForm,
    validateField,
    sendAjaxRequest,
    formatCurrency,
    formatArabicNumber
};
