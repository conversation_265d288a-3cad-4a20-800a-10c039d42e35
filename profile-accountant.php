<?php
/**
 * Accountant Profile Page
 * صفحة الملف الشخصي للمحاسب
 */

require_once 'backend/config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'accountant') {
    redirect(getUrl('dashboard/accountant'));
}

// معالجة تحديث البيانات
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = sanitize($_POST['action']);
        
        try {
            global $database;
            
            switch ($action) {
                case 'update_profile':
                    $update_data = [
                        'full_name' => sanitize($_POST['full_name']),
                        'email' => sanitize($_POST['email']),
                        'phone' => sanitize($_POST['phone']),
                        'company_name' => sanitize($_POST['company_name']),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                    
                    // التحقق من عدم تكرار البريد الإلكتروني
                    $existing_email = fetchOne("SELECT id FROM users WHERE email = :email AND id != :user_id", 
                        ['email' => $update_data['email'], 'user_id' => $user['id']]);
                    
                    if ($existing_email) {
                        $error_message = 'البريد الإلكتروني مستخدم من قبل مستخدم آخر';
                    } else {
                        $database->update('users', $update_data, 'id = :id', ['id' => $user['id']]);
                        $success_message = 'تم تحديث البيانات الشخصية بنجاح';
                        
                        // تحديث بيانات المستخدم في الجلسة
                        $_SESSION['user'] = array_merge($_SESSION['user'], $update_data);
                        $user = getCurrentUser();
                    }
                    break;
                    
                case 'change_password':
                    $current_password = $_POST['current_password'];
                    $new_password = $_POST['new_password'];
                    $confirm_password = $_POST['confirm_password'];
                    
                    if (!password_verify($current_password, $user['password'])) {
                        $error_message = 'كلمة المرور الحالية غير صحيحة';
                    } elseif ($new_password !== $confirm_password) {
                        $error_message = 'كلمة المرور الجديدة وتأكيدها غير متطابقين';
                    } elseif (strlen($new_password) < 8) {
                        $error_message = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
                    } else {
                        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                        $database->update('users', ['password' => $hashed_password], 'id = :id', ['id' => $user['id']]);
                        $success_message = 'تم تغيير كلمة المرور بنجاح';
                    }
                    break;
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء تحديث البيانات';
            logError("Profile update error: " . $e->getMessage());
        }
    }
}

// إحصائيات المحاسب
$accountant_stats = [
    'assigned_requests' => 0,
    'completed_requests' => 0,
    'pending_requests' => 0,
    'total_clients' => 0,
    'total_revenue' => 0
];

try {
    // الطلبات المسندة للمحاسب
    $stats_sql = "SELECT 
                    COUNT(*) as assigned_requests,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_requests,
                    SUM(CASE WHEN status IN ('pending', 'in_progress') THEN 1 ELSE 0 END) as pending_requests
                  FROM service_requests WHERE assigned_to = :user_id";
    
    $request_stats = fetchOne($stats_sql, ['user_id' => $user['id']]);
    if ($request_stats) {
        $accountant_stats = array_merge($accountant_stats, $request_stats);
    }
    
    // عدد العملاء المتفردين
    $client_stats = fetchOne("SELECT COUNT(DISTINCT user_id) as total_clients FROM service_requests WHERE assigned_to = :user_id", 
        ['user_id' => $user['id']]);
    if ($client_stats) {
        $accountant_stats['total_clients'] = $client_stats['total_clients'];
    }
    
    // إجمالي الإيرادات من الطلبات المكتملة
    $revenue_stats = fetchOne("SELECT SUM(total_amount) as total_revenue FROM service_requests WHERE assigned_to = :user_id AND status = 'completed'", 
        ['user_id' => $user['id']]);
    if ($revenue_stats) {
        $accountant_stats['total_revenue'] = $revenue_stats['total_revenue'] ?? 0;
    }
    
} catch (Exception $e) {
    logError("Accountant stats error: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include 'frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($success_message): ?>
            <div class="mb-6 p-4 rounded-lg bg-green-100 text-green-700 border border-green-300">
                <div class="flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="mb-6 p-4 rounded-lg bg-red-100 text-red-700 border border-red-300">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle ml-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-4">
                <a href="<?php echo getUrl(); ?>" class="hover:text-earth-green transition-colors">الرئيسية</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <a href="<?php echo getUrl('dashboard/accountant'); ?>" class="hover:text-earth-green transition-colors">لوحة المحاسب</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <span class="text-earth-green">الملف الشخصي</span>
            </nav>
            
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-earth-green mb-2">الملف الشخصي</h1>
                    <p class="text-gray-600">إدارة بياناتك الشخصية وإعدادات الحساب المهني</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <a href="<?php echo getUrl('dashboard/accountant'); ?>" 
                       class="bg-sand-brown text-white px-6 py-2 rounded-lg hover:bg-earth-green transition-colors">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للوحة التحكم
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Accountant Statistics -->
        <div class="grid md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            
            <!-- Assigned Requests -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">الطلبات المسندة</p>
                        <p class="text-2xl font-bold text-earth-green"><?php echo $accountant_stats['assigned_requests']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-earth-green rounded-lg flex items-center justify-center">
                        <i class="fas fa-tasks text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Completed Requests -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">طلبات مكتملة</p>
                        <p class="text-2xl font-bold text-green-600"><?php echo $accountant_stats['completed_requests']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Pending Requests -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">طلبات معلقة</p>
                        <p class="text-2xl font-bold text-yellow-600"><?php echo $accountant_stats['pending_requests']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-yellow-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Total Clients -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">العملاء</p>
                        <p class="text-2xl font-bold text-blue-600"><?php echo $accountant_stats['total_clients']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-tie text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Total Revenue -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                        <p class="text-2xl font-bold text-purple-600"><?php echo formatCurrency($accountant_stats['total_revenue']); ?></p>
                    </div>
                    <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-chart-line text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Forms -->
        <div class="grid lg:grid-cols-2 gap-8">

            <!-- Personal Information -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-earth-green mb-6">البيانات الشخصية والمهنية</h2>

                <form method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="update_profile">

                    <!-- Full Name -->
                    <div>
                        <label for="full_name" class="block text-sm font-medium text-gray-700 mb-2">
                            الاسم الكامل <span class="text-red-500">*</span>
                        </label>
                        <input type="text"
                               id="full_name"
                               name="full_name"
                               value="<?php echo htmlspecialchars($user['full_name']); ?>"
                               required
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني المهني <span class="text-red-500">*</span>
                        </label>
                        <input type="email"
                               id="email"
                               name="email"
                               value="<?php echo htmlspecialchars($user['email']); ?>"
                               required
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                    </div>

                    <!-- Phone -->
                    <div>
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                            رقم الهاتف المهني
                        </label>
                        <input type="tel"
                               id="phone"
                               name="phone"
                               value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                    </div>

                    <!-- Company Name -->
                    <div>
                        <label for="company_name" class="block text-sm font-medium text-gray-700 mb-2">
                            مكتب المحاسبة / الشركة
                        </label>
                        <input type="text"
                               id="company_name"
                               name="company_name"
                               value="<?php echo htmlspecialchars($user['company_name'] ?? ''); ?>"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                    </div>

                    <div class="flex justify-end">
                        <button type="submit"
                                class="bg-earth-green text-white px-6 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                            <i class="fas fa-save ml-2"></i>
                            حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>

            <!-- Change Password -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h2 class="text-xl font-semibold text-earth-green mb-6">تغيير كلمة المرور</h2>

                <form method="POST" class="space-y-6">
                    <input type="hidden" name="action" value="change_password">

                    <!-- Current Password -->
                    <div>
                        <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">
                            كلمة المرور الحالية <span class="text-red-500">*</span>
                        </label>
                        <input type="password"
                               id="current_password"
                               name="current_password"
                               required
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                    </div>

                    <!-- New Password -->
                    <div>
                        <label for="new_password" class="block text-sm font-medium text-gray-700 mb-2">
                            كلمة المرور الجديدة <span class="text-red-500">*</span>
                        </label>
                        <input type="password"
                               id="new_password"
                               name="new_password"
                               required
                               minlength="8"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                        <p class="text-xs text-gray-500 mt-1">يجب أن تكون 8 أحرف على الأقل</p>
                    </div>

                    <!-- Confirm Password -->
                    <div>
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                            تأكيد كلمة المرور <span class="text-red-500">*</span>
                        </label>
                        <input type="password"
                               id="confirm_password"
                               name="confirm_password"
                               required
                               minlength="8"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                    </div>

                    <div class="flex justify-end">
                        <button type="submit"
                                class="bg-red-600 text-white px-6 py-2 rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-key ml-2"></i>
                            تغيير كلمة المرور
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="assets/js/main.js"></script>

    <script>
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const newPassword = document.getElementById('new_password').value;
            const confirmPassword = this.value;

            if (newPassword !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });

        document.getElementById('new_password').addEventListener('input', function() {
            const confirmPassword = document.getElementById('confirm_password');
            if (confirmPassword.value) {
                confirmPassword.dispatchEvent(new Event('input'));
            }
        });
    </script>
</body>
</html>
