<?php
/**
 * AI Document Analysis
 * تحليل المستندات بالذكاء الاصطناعي
 */

require_once '../config/config.php';
require_once 'ai-helper.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    http_response_code(401);
    echo json_encode(['error' => 'غير مصرح لك بالوصول']);
    exit;
}

$user = getCurrentUser();
if (!in_array($user['role'], ['admin', 'accountant'])) {
    http_response_code(403);
    echo json_encode(['error' => 'غير مصرح لك بتحليل المستندات']);
    exit;
}

$document_id = (int)($_POST['document_id'] ?? 0);

if (!$document_id) {
    http_response_code(400);
    echo json_encode(['error' => 'معرف المستند مطلوب']);
    exit;
}

try {
    // الحصول على معلومات المستند
    $sql = "SELECT d.*, sr.request_title, sr.description, s.service_name_ar 
            FROM documents d 
            LEFT JOIN service_requests sr ON d.request_id = sr.id 
            LEFT JOIN services s ON sr.service_id = s.id 
            WHERE d.id = :id";
    
    $document = fetchOne($sql, ['id' => $document_id]);
    
    if (!$document) {
        http_response_code(404);
        echo json_encode(['error' => 'المستند غير موجود']);
        exit;
    }
    
    // التحقق من وجود الملف
    if (!file_exists($document['file_path'])) {
        http_response_code(404);
        echo json_encode(['error' => 'الملف غير موجود على الخادم']);
        exit;
    }
    
    // التحقق من أن الملف لم يتم تحليله مسبقاً
    if (!empty($document['ai_analysis'])) {
        echo json_encode([
            'success' => true,
            'analysis' => $document['ai_analysis'],
            'message' => 'تم استرجاع التحليل المحفوظ مسبقاً'
        ]);
        exit;
    }
    
    // إعداد البيانات للذكاء الاصطناعي
    $document_info = [
        'filename' => $document['original_filename'],
        'file_type' => $document['file_type'],
        'request_title' => $document['request_title'] ?? '',
        'request_description' => $document['description'] ?? '',
        'service_type' => $document['service_name_ar'] ?? ''
    ];

    // استدعاء الذكاء الاصطناعي لتحليل المستند
    $analysis = AIHelper::analyzeDocument($document_info);
    
    if ($analysis) {
        // حفظ التحليل في قاعدة البيانات
        global $database;
        $database->update('documents', 
            ['ai_analysis' => $analysis, 'is_processed' => 1], 
            'id = :id', 
            ['id' => $document_id]
        );
        
        // تسجيل العملية
        logError("AI Analysis: Document {$document_id} analyzed by user {$user['id']}");
        
        echo json_encode([
            'success' => true,
            'analysis' => $analysis,
            'message' => 'تم تحليل المستند بنجاح'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'فشل في تحليل المستند'
        ]);
    }
    
} catch (Exception $e) {
    logError("AI Analysis error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'حدث خطأ أثناء تحليل المستند']);
}

// تم نقل الدوال إلى ai-helper.php
?>
