<?php
/**
 * AI Service Recommendations Page
 * صفحة توصيات الخدمات الذكية
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'client') {
    redirect(getUrl('login.php'));
}

// الحصول على توصيات الخدمات من الذكاء الاصطناعي
$ai_recommendations = [];
$loading_error = false;

try {
    require_once '../../backend/ai/ai-helper.php';
    $user_data = AIHelper::getUserAnalysisData($user['id']);
    
    if ($user_data) {
        $ai_response = AIHelper::getServiceRecommendations($user_data);
        if ($ai_response) {
            $ai_recommendations = AIHelper::parseServiceRecommendations($ai_response);
        } else {
            $ai_recommendations = AIHelper::getDefaultRecommendations($user_data);
        }
    }
} catch (Exception $e) {
    logError("AI recommendations error: " . $e->getMessage());
    $loading_error = true;
}

// الحصول على الخدمات المتاحة
$available_services = [];
try {
    $available_services = fetchAll("SELECT * FROM services WHERE is_active = 1 ORDER BY name");
} catch (Exception $e) {
    logError("Services fetch error: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التوصيات الذكية - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Header -->
        <div class="mb-8">
            <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-4">
                <a href="<?php echo getUrl(); ?>" class="hover:text-earth-green transition-colors">الرئيسية</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <a href="<?php echo getUrl('dashboard/client'); ?>" class="hover:text-earth-green transition-colors">لوحة التحكم</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <span class="text-earth-green">التوصيات الذكية</span>
            </nav>
            
            <div class="text-center mb-8">
                <div class="w-20 h-20 bg-earth-green rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-robot text-white text-3xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-earth-green mb-2">
                    التوصيات الذكية
                </h1>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    خدمات محاسبية مُوصى بها خصيصاً لك بناءً على تحليل الذكاء الاصطناعي لاحتياجاتك
                </p>
            </div>
        </div>
        
        <?php if ($loading_error): ?>
            <!-- Error State -->
            <div class="bg-white rounded-lg shadow-lg p-8 text-center">
                <i class="fas fa-exclamation-triangle text-4xl text-red-500 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">حدث خطأ في تحميل التوصيات</h3>
                <p class="text-gray-600 mb-6">نعتذر، لم نتمكن من تحميل التوصيات الذكية في الوقت الحالي</p>
                <a href="<?php echo getUrl('services/request.php'); ?>" 
                   class="bg-earth-green text-white px-6 py-3 rounded-lg hover:bg-sand-brown transition-colors">
                    تصفح جميع الخدمات
                </a>
            </div>
        <?php elseif (empty($ai_recommendations)): ?>
            <!-- No Recommendations -->
            <div class="bg-white rounded-lg shadow-lg p-8 text-center">
                <i class="fas fa-lightbulb text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-800 mb-2">لا توجد توصيات متاحة حالياً</h3>
                <p class="text-gray-600 mb-6">قم بطلب بعض الخدمات أولاً لنتمكن من تقديم توصيات مخصصة لك</p>
                <a href="<?php echo getUrl('services/request.php'); ?>" 
                   class="bg-earth-green text-white px-6 py-3 rounded-lg hover:bg-sand-brown transition-colors">
                    اطلب خدمة الآن
                </a>
            </div>
        <?php else: ?>
            <!-- AI Recommendations -->
            <div class="grid lg:grid-cols-3 gap-8 mb-8">
                <?php foreach ($ai_recommendations as $index => $recommendation): ?>
                <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 recommendation-card" 
                     style="animation-delay: <?php echo $index * 0.2; ?>s">
                    
                    <!-- Priority Badge -->
                    <div class="relative">
                        <?php
                        $priority_colors = [
                            'عالية' => 'bg-red-500',
                            'متوسطة' => 'bg-yellow-500', 
                            'منخفضة' => 'bg-green-500'
                        ];
                        $priority_color = $priority_colors[$recommendation['priority']] ?? 'bg-gray-500';
                        ?>
                        <div class="absolute top-4 left-4 z-10">
                            <span class="px-3 py-1 text-xs font-semibold text-white rounded-full <?php echo $priority_color; ?>">
                                أولوية <?php echo htmlspecialchars($recommendation['priority']); ?>
                            </span>
                        </div>
                        
                        <!-- Service Icon -->
                        <div class="bg-gradient-to-r from-earth-green to-sand-brown p-6 text-center">
                            <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-cogs text-white text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white">
                                <?php echo htmlspecialchars($recommendation['service']); ?>
                            </h3>
                        </div>
                    </div>
                    
                    <!-- Content -->
                    <div class="p-6">
                        <div class="mb-4">
                            <h4 class="text-sm font-semibold text-gray-700 mb-2">
                                <i class="fas fa-lightbulb text-yellow-500 ml-2"></i>
                                لماذا نوصي بهذه الخدمة؟
                            </h4>
                            <p class="text-gray-600 text-sm leading-relaxed">
                                <?php echo htmlspecialchars($recommendation['reason']); ?>
                            </p>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="flex flex-col gap-3">
                            <a href="<?php echo getUrl('services/request.php'); ?>" 
                               class="bg-earth-green text-white px-4 py-2 rounded-lg text-center font-semibold hover:bg-sand-brown transition-colors">
                                <i class="fas fa-plus ml-2"></i>
                                اطلب هذه الخدمة
                            </a>
                            <button onclick="showServiceDetails('<?php echo htmlspecialchars($recommendation['service']); ?>')"
                                    class="border border-earth-green text-earth-green px-4 py-2 rounded-lg text-center font-semibold hover:bg-earth-green hover:text-white transition-colors">
                                <i class="fas fa-info-circle ml-2"></i>
                                تفاصيل أكثر
                            </button>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
        
        <!-- All Services Section -->
        <?php if (!empty($available_services)): ?>
        <div class="bg-white rounded-lg shadow-lg p-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-earth-green">
                    <i class="fas fa-list ml-2"></i>
                    جميع الخدمات المتاحة
                </h2>
                <a href="<?php echo getUrl('services/request.php'); ?>" 
                   class="text-earth-green hover:text-sand-brown transition-colors">
                    عرض الكل <i class="fas fa-arrow-left mr-2"></i>
                </a>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php foreach (array_slice($available_services, 0, 6) as $service): ?>
                <div class="border border-gray-200 rounded-lg p-4 hover:border-earth-green transition-colors">
                    <h4 class="font-semibold text-gray-800 mb-2"><?php echo htmlspecialchars($service['name']); ?></h4>
                    <p class="text-sm text-gray-600 mb-3"><?php echo htmlspecialchars(substr($service['description'], 0, 100)); ?>...</p>
                    <div class="flex items-center justify-between">
                        <span class="text-earth-green font-semibold"><?php echo formatCurrency($service['price']); ?></span>
                        <a href="<?php echo getUrl('services/request.php?service=' . $service['id']); ?>" 
                           class="text-sm text-earth-green hover:text-sand-brown transition-colors">
                            اطلب الآن
                        </a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>
    
    <script>
        // Animation for recommendation cards
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.recommendation-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
        
        // Show service details
        function showServiceDetails(serviceName) {
            alert(`تفاصيل خدمة: ${serviceName}\n\nسيتم إضافة المزيد من التفاصيل قريباً...`);
        }
    </script>
</body>
</html>
