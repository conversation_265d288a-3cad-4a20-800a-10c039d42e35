-- Database Setup Script for Mohassebk System
-- سكريبت إعداد قاعدة البيانات لنظام المحاسبك

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS mohassebk_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE mohassebk_db;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    company_name VARCHAR(100),
    role ENUM('client', 'accountant', 'admin') DEFAULT 'client',
    is_active BOOLEAN DEFAULT TRUE,
    email_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Services table
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    service_name_ar VARCHAR(200) NOT NULL,
    service_name_en VARCHAR(200),
    description_ar TEXT NOT NULL,
    description_en TEXT,
    base_price DECIMAL(10,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Service requests table
CREATE TABLE IF NOT EXISTS service_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    service_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    status ENUM('pending', 'in_progress', 'completed', 'cancelled') DEFAULT 'pending',
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    total_amount DECIMAL(10,2) DEFAULT 0.00,
    payment_status ENUM('pending', 'completed', 'failed') DEFAULT 'pending',
    assigned_to INT NULL,
    due_date DATE NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
);

-- Payments table
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    service_request_id INT,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'DZD',
    payment_method VARCHAR(50),
    payment_status ENUM('pending', 'completed', 'failed', 'refunded') DEFAULT 'pending',
    transaction_id VARCHAR(100),
    payment_gateway VARCHAR(50),
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_request_id) REFERENCES service_requests(id) ON DELETE SET NULL
);

-- Documents table
CREATE TABLE IF NOT EXISTS documents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    service_request_id INT,
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    is_processed BOOLEAN DEFAULT FALSE,
    ai_analysis TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (service_request_id) REFERENCES service_requests(id) ON DELETE CASCADE
);

-- System settings table
CREATE TABLE IF NOT EXISTS system_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact messages table
CREATE TABLE IF NOT EXISTS contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    company VARCHAR(100),
    subject VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    status ENUM('new', 'read', 'replied', 'closed') DEFAULT 'new',
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- AI conversations table
CREATE TABLE IF NOT EXISTS ai_conversations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    session_id VARCHAR(100) NOT NULL,
    message TEXT NOT NULL,
    response TEXT NOT NULL,
    model_used VARCHAR(100),
    tokens_used INT DEFAULT 0,
    processing_time DECIMAL(5,3) DEFAULT 0.000,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default admin user
INSERT IGNORE INTO users (username, email, password, full_name, role, is_active, email_verified) 
VALUES ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', TRUE, TRUE);

-- Insert default services
INSERT IGNORE INTO services (service_name_ar, service_name_en, description_ar, description_en, base_price, is_active) VALUES
('حساب الضرائب', 'Tax Calculation', 'حساب دقيق للضرائب المستحقة وفقاً للقوانين الجزائرية', 'Accurate tax calculation according to Algerian laws', 5000.00, TRUE),
('المراجعة والتدقيق المالي', 'Financial Audit', 'مراجعة شاملة للقوائم المالية والحسابات', 'Comprehensive review of financial statements and accounts', 15000.00, TRUE),
('إعداد القوائم المالية', 'Financial Statements', 'إعداد قوائم مالية احترافية ومطابقة للمعايير', 'Professional financial statements preparation', 10000.00, TRUE),
('تسوية الحسابات المالية', 'Account Reconciliation', 'تسوية ومطابقة الحسابات المالية', 'Financial account reconciliation and matching', 3000.00, TRUE),
('إدارة الأرباح والضرائب', 'Profit & Tax Management', 'تخطيط وإدارة الأرباح والضرائب', 'Profit and tax planning and management', 8000.00, TRUE),
('إدارة الديون', 'Debt Management', 'متابعة وإدارة المستحقات والديون', 'Debt and receivables management', 4000.00, TRUE),
('خدمات الاستشارة المالية', 'Financial Consulting', 'استشارات مالية متخصصة', 'Specialized financial consulting services', 12000.00, TRUE);

-- Insert default system settings
INSERT IGNORE INTO system_settings (setting_key, setting_value, setting_type, description, is_public) VALUES
('site_name', 'المحاسبك الرقمي', 'string', 'اسم الموقع', TRUE),
('site_email', '<EMAIL>', 'string', 'البريد الإلكتروني للموقع', TRUE),
('site_phone', '+************', 'string', 'رقم هاتف الموقع', TRUE),
('site_address', 'الجزائر العاصمة، الجزائر', 'string', 'عنوان الموقع', TRUE),
('site_description', 'منصة الخدمات المحاسبية الرقمية', 'string', 'وصف الموقع', TRUE),
('currency', 'DZD', 'string', 'العملة المستخدمة', TRUE),
('timezone', 'Africa/Algiers', 'string', 'المنطقة الزمنية', FALSE),
('max_file_size', '10', 'number', 'الحد الأقصى لحجم الملف بالميجابايت', FALSE),
('allowed_file_types', 'pdf,doc,docx,xls,xlsx,jpg,jpeg,png', 'string', 'أنواع الملفات المسموحة', FALSE),
('ai_enabled', '1', 'boolean', 'تفعيل الذكاء الاصطناعي', FALSE),
('ai_model', 'deepseek/deepseek-chat:free', 'string', 'نموذج الذكاء الاصطناعي', FALSE),
('email_notifications', '1', 'boolean', 'تفعيل إشعارات البريد الإلكتروني', FALSE);
