<?php
/**
 * Database Connection Configuration
 * ملف إعدادات الاتصال بقاعدة البيانات
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'mohassebk_db');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

class Database {
    private $host = DB_HOST;
    private $db_name = DB_NAME;
    private $username = DB_USER;
    private $password = DB_PASS;
    private $charset = DB_CHARSET;
    private $pdo;
    
    /**
     * إنشاء اتصال بقاعدة البيانات
     */
    public function connect() {
        $this->pdo = null;
        
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
            ];
            
            $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $e) {
            echo "خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage();
            die();
        }
        
        return $this->pdo;
    }
    
    /**
     * إغلاق الاتصال
     */
    public function disconnect() {
        $this->pdo = null;
    }
    
    /**
     * تنفيذ استعلام
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch(PDOException $e) {
            throw new Exception("خطأ في تنفيذ الاستعلام: " . $e->getMessage());
        }
    }
    
    /**
     * الحصول على سجل واحد
     */
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * الحصول على جميع السجلات
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * إدراج سجل جديد
     */
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        
        $this->query($sql, $data);
        return $this->pdo->lastInsertId();
    }
    
    /**
     * تحديث سجل
     */
    public function update($table, $data, $where, $whereParams = []) {
        $set = [];
        foreach($data as $key => $value) {
            $set[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $set);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        
        $params = array_merge($data, $whereParams);
        return $this->query($sql, $params);
    }
    
    /**
     * حذف سجل
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql, $params);
    }
    
    /**
     * عدد السجلات
     */
    public function count($table, $where = '1=1', $params = []) {
        $sql = "SELECT COUNT(*) as count FROM {$table} WHERE {$where}";
        $result = $this->fetch($sql, $params);
        return $result['count'];
    }
}

// إنشاء اتصال عام
$database = new Database();
$pdo = $database->connect();

/**
 * دالة مساعدة للحصول على اتصال قاعدة البيانات
 */
function getDB() {
    global $pdo;
    return $pdo;
}

/**
 * دالة مساعدة لتنفيذ استعلام آمن
 */
function executeQuery($sql, $params = []) {
    global $database;
    return $database->query($sql, $params);
}

/**
 * دالة مساعدة للحصول على سجل واحد
 */
function fetchOne($sql, $params = []) {
    global $database;
    return $database->fetch($sql, $params);
}

/**
 * دالة مساعدة للحصول على جميع السجلات
 */
function fetchAll($sql, $params = []) {
    global $database;
    return $database->fetchAll($sql, $params);
}
?>
