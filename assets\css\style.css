/**
 * محاسبك الرقمي - ملف الأنماط الرئيسي
 * Digital Accountant - Main Stylesheet
 */

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* الألوان الأساسية - Muted Modern Palette */
    --muted-green: #CAD2C5;
    --warm-beige: #FFE5B4;
    --earth-green: #52796F;
    --sand-brown: #8D7B68;
    --light-gray: #F0F0F0;
    --white: #FFFFFF;
    --dark-gray: #333333;
    --text-gray: #666666;
    
    /* الخطوط */
    --font-primary: 'Cairo', sans-serif;
    
    /* الظلال */
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
    
    /* الانتقالات */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
}

/* إعدادات الخط والاتجاه */
body {
    font-family: var(--font-primary);
    direction: rtl;
    text-align: right;
    line-height: 1.6;
    color: var(--dark-gray);
    background-color: var(--light-gray);
}

/* العناوين */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

/* الروابط */
a {
    color: var(--earth-green);
    text-decoration: none;
    transition: var(--transition-normal);
}

a:hover {
    color: var(--sand-brown);
}

/* الأزرار */
.btn {
    display: inline-block;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition-normal);
    text-decoration: none;
    font-family: inherit;
}

.btn-primary {
    background-color: var(--earth-green);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--sand-brown);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background-color: var(--warm-beige);
    color: var(--earth-green);
}

.btn-secondary:hover {
    background-color: var(--muted-green);
    transform: translateY(-2px);
}

.btn-outline {
    background-color: transparent;
    border: 2px solid var(--earth-green);
    color: var(--earth-green);
}

.btn-outline:hover {
    background-color: var(--earth-green);
    color: var(--white);
}

/* النماذج */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--dark-gray);
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--muted-green);
    border-radius: 8px;
    font-size: 1rem;
    font-family: inherit;
    transition: var(--transition-normal);
    background-color: var(--white);
}

.form-input:focus {
    outline: none;
    border-color: var(--earth-green);
    box-shadow: 0 0 0 3px rgba(82, 121, 111, 0.1);
}

.form-input.error {
    border-color: #e74c3c;
}

.form-error {
    color: #e74c3c;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* البطاقات */
.card {
    background-color: var(--white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow-light);
    transition: var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-4px);
}

.card-header {
    border-bottom: 1px solid var(--muted-green);
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--earth-green);
}

/* شريط التنقل */
.navbar {
    background-color: var(--white);
    box-shadow: var(--shadow-light);
    position: sticky;
    top: 0;
    z-index: 1000;
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--earth-green);
}

.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-item {
    margin: 0 1rem;
}

.nav-link {
    color: var(--dark-gray);
    font-weight: 500;
    padding: 0.5rem 0;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--earth-green);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--earth-green);
    transition: var(--transition-normal);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* التذييل */
.footer {
    background-color: var(--earth-green);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-section {
    margin-bottom: 2rem;
}

.footer-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--warm-beige);
}

.footer-link {
    color: var(--white);
    opacity: 0.8;
    transition: var(--transition-normal);
}

.footer-link:hover {
    opacity: 1;
    color: var(--warm-beige);
}

/* الرسائل التنبيهية */
.alert {
    padding: 1rem 1.5rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    border-left: 4px solid;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-color: #28a745;
}

.alert-error {
    background-color: #f8d7da;
    color: #721c24;
    border-color: #dc3545;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border-color: #ffc107;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border-color: #17a2b8;
}

/* الجداول */
.table {
    width: 100%;
    border-collapse: collapse;
    background-color: var(--white);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-light);
}

.table th,
.table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid var(--muted-green);
}

.table th {
    background-color: var(--earth-green);
    color: var(--white);
    font-weight: 600;
}

.table tr:hover {
    background-color: var(--light-gray);
}

/* الشارات */
.badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 20px;
    text-align: center;
}

.badge-success {
    background-color: #28a745;
    color: var(--white);
}

.badge-warning {
    background-color: #ffc107;
    color: var(--dark-gray);
}

.badge-danger {
    background-color: #dc3545;
    color: var(--white);
}

.badge-info {
    background-color: #17a2b8;
    color: var(--white);
}

/* الحاويات */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.container-fluid {
    width: 100%;
    padding: 0 1rem;
}

/* الشبكة */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -0.75rem;
}

.col {
    flex: 1;
    padding: 0 0.75rem;
}

/* الأدوات المساعدة */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

/* الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.25rem; }
    
    .container {
        padding: 0 1rem;
    }
    
    .navbar-nav {
        flex-direction: column;
    }
    
    .nav-item {
        margin: 0.5rem 0;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

/* تحسينات الأداء */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* تحسينات إضافية للـ RTL */
.rtl {
    direction: rtl;
    text-align: right;
}

.ltr {
    direction: ltr;
    text-align: left;
}
