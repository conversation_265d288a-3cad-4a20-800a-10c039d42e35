<?php
/**
 * اختبار سريع للذكاء الاصطناعي
 */

// إعدادات API
define('OPENROUTER_API_KEY', '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');
define('OPENROUTER_API_URL', 'https://openrouter.ai/api/v1/chat/completions');
define('AI_MODEL', 'deepseek/deepseek-chat-v3-0324:free');

function testAPI($message) {
    $data = [
        'model' => AI_MODEL,
        'messages' => [
            ['role' => 'user', 'content' => $message]
        ],
        'max_tokens' => 300,
        'temperature' => 0.7
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, OPENROUTER_API_URL);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . OPENROUTER_API_KEY,
        'HTTP-Referer: http://localhost/Mohassebk',
        'X-Title: Mohassebk AI Test'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);
    
    echo "<h3>نتيجة الاختبار:</h3>";
    echo "<p><strong>HTTP Code:</strong> $http_code</p>";
    echo "<p><strong>cURL Error:</strong> " . ($curl_error ?: 'لا يوجد') . "</p>";
    
    if ($response === false || $http_code !== 200) {
        echo "<p style='color: red;'><strong>فشل:</strong> لم يتم الحصول على رد صحيح</p>";
        echo "<p><strong>الرد الخام:</strong> " . htmlspecialchars(substr($response, 0, 500)) . "</p>";
        return false;
    }
    
    $decoded = json_decode($response, true);
    if (isset($decoded['choices'][0]['message']['content'])) {
        $content = trim($decoded['choices'][0]['message']['content']);
        echo "<p style='color: green;'><strong>نجح!</strong> تم الحصول على رد من API</p>";
        echo "<div style='background: #f0f0f0; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<strong>الرد:</strong><br>" . nl2br(htmlspecialchars($content));
        echo "</div>";
        return $content;
    }
    
    echo "<p style='color: orange;'><strong>تحذير:</strong> تم الحصول على رد لكن بدون محتوى</p>";
    echo "<p><strong>الرد الخام:</strong> " . htmlspecialchars(substr($response, 0, 500)) . "</p>";
    return false;
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع للذكاء الاصطناعي</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-box {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 اختبار سريع للذكاء الاصطناعي</h1>
        
        <div class="test-box">
            <h2>معلومات API:</h2>
            <p><strong>API Key:</strong> <?php echo substr(OPENROUTER_API_KEY, 0, 20) . '...'; ?></p>
            <p><strong>Model:</strong> <?php echo AI_MODEL; ?></p>
            <p><strong>URL:</strong> <?php echo OPENROUTER_API_URL; ?></p>
        </div>

        <?php if (isset($_POST['test_message'])): ?>
            <div class="test-box">
                <h2>اختبار الرسالة:</h2>
                <p><strong>الرسالة المرسلة:</strong> <?php echo htmlspecialchars($_POST['test_message']); ?></p>
                
                <?php
                $start_time = microtime(true);
                $result = testAPI($_POST['test_message']);
                $end_time = microtime(true);
                $processing_time = round(($end_time - $start_time) * 1000);
                ?>
                
                <p><strong>وقت المعالجة:</strong> <?php echo $processing_time; ?> مللي ثانية</p>
            </div>
        <?php endif; ?>

        <div class="test-box">
            <h2>اختبار رسالة:</h2>
            <form method="POST">
                <p>
                    <label>الرسالة:</label><br>
                    <textarea name="test_message" rows="3" style="width: 100%; padding: 10px;" required><?php echo isset($_POST['test_message']) ? htmlspecialchars($_POST['test_message']) : 'مرحبا، كيف أحسب ضريبة القيمة المضافة في الجزائر؟'; ?></textarea>
                </p>
                <p>
                    <button type="submit" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">🧪 اختبار API</button>
                </p>
            </form>
        </div>

        <div class="test-box">
            <h2>اختبارات سريعة:</h2>
            <form method="POST" style="display: inline;">
                <input type="hidden" name="test_message" value="مرحبا">
                <button type="submit" style="margin: 5px; padding: 8px 15px; background: #28a745; color: white; border: none; border-radius: 3px;">مرحبا</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="test_message" value="كيف أحسب ضريبة القيمة المضافة؟">
                <button type="submit" style="margin: 5px; padding: 8px 15px; background: #17a2b8; color: white; border: none; border-radius: 3px;">سؤال ضريبي</button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="test_message" value="ما هي أنواع الشركات في الجزائر؟">
                <button type="submit" style="margin: 5px; padding: 8px 15px; background: #ffc107; color: black; border: none; border-radius: 3px;">أنواع الشركات</button>
            </form>
        </div>

        <div class="test-box">
            <h2>ملاحظات:</h2>
            <ul>
                <li>إذا نجح الاختبار هنا، فإن API يعمل بشكل صحيح</li>
                <li>إذا فشل، تحقق من مفتاح API أو الاتصال بالإنترنت</li>
                <li>يمكنك نسخ هذا الكود إلى المساعد الذكي إذا كان يعمل</li>
            </ul>
        </div>
    </div>
</body>
</html>
