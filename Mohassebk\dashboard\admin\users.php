<?php
/**
 * Admin User Management Page
 * صفحة إدارة المستخدمين
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'admin') {
    redirect(getUrl('dashboard/client'));
}

// معالجة الإجراءات
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = sanitize($_POST['action']);
        $user_id = (int)($_POST['user_id'] ?? 0);
        
        try {
            global $database;
            
            switch ($action) {
                case 'toggle_status':
                    $current_status = fetchOne("SELECT is_active FROM users WHERE id = :id", ['id' => $user_id]);
                    $new_status = $current_status['is_active'] ? 0 : 1;
                    $database->update('users', ['is_active' => $new_status], 'id = :id', ['id' => $user_id]);
                    $success_message = $new_status ? 'تم تفعيل المستخدم بنجاح' : 'تم إلغاء تفعيل المستخدم بنجاح';
                    break;
                    
                case 'delete_user':
                    if ($user_id !== $user['id']) { // منع حذف الحساب الحالي
                        $database->delete('users', 'id = :id', ['id' => $user_id]);
                        $success_message = 'تم حذف المستخدم بنجاح';
                    } else {
                        $error_message = 'لا يمكن حذف حسابك الحالي';
                    }
                    break;
                    
                case 'bulk_action':
                    $bulk_action = sanitize($_POST['bulk_action'] ?? '');
                    $selected_users = $_POST['selected_users'] ?? [];
                    
                    if (!empty($selected_users) && !empty($bulk_action)) {
                        $user_ids = array_map('intval', $selected_users);
                        $placeholders = str_repeat('?,', count($user_ids) - 1) . '?';
                        
                        switch ($bulk_action) {
                            case 'activate':
                                $database->query("UPDATE users SET is_active = 1 WHERE id IN ($placeholders)", $user_ids);
                                $success_message = 'تم تفعيل المستخدمين المحددين بنجاح';
                                break;
                            case 'deactivate':
                                $database->query("UPDATE users SET is_active = 0 WHERE id IN ($placeholders) AND id != ?", array_merge($user_ids, [$user['id']]));
                                $success_message = 'تم إلغاء تفعيل المستخدمين المحددين بنجاح';
                                break;
                            case 'delete':
                                $database->query("DELETE FROM users WHERE id IN ($placeholders) AND id != ?", array_merge($user_ids, [$user['id']]));
                                $success_message = 'تم حذف المستخدمين المحددين بنجاح';
                                break;
                        }
                    }
                    break;
            }
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء تنفيذ العملية';
            logError("User management error: " . $e->getMessage());
        }
    }
}

// معاملات البحث والتصفية
$search = sanitize($_GET['search'] ?? '');
$role_filter = sanitize($_GET['role'] ?? '');
$status_filter = sanitize($_GET['status'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 15;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = ['1=1'];
$params = [];

if (!empty($search)) {
    $where_conditions[] = '(full_name LIKE :search OR email LIKE :search OR username LIKE :search OR company_name LIKE :search)';
    $params['search'] = '%' . $search . '%';
}

if (!empty($role_filter)) {
    $where_conditions[] = 'role = :role';
    $params['role'] = $role_filter;
}

if (!empty($status_filter)) {
    $where_conditions[] = 'is_active = :status';
    $params['status'] = $status_filter === 'active' ? 1 : 0;
}

$where_clause = implode(' AND ', $where_conditions);

// الحصول على إجمالي عدد المستخدمين
$total_users = 0;
try {
    $count_sql = "SELECT COUNT(*) as count FROM users WHERE $where_clause";
    $total_users = fetchOne($count_sql, $params)['count'];
} catch (Exception $e) {
    logError("Users count error: " . $e->getMessage());
}

$total_pages = ceil($total_users / $per_page);

// الحصول على المستخدمين
$users = [];
try {
    $sql = "SELECT * FROM users WHERE $where_clause ORDER BY created_at DESC LIMIT :limit OFFSET :offset";
    $stmt = executeQuery($sql, array_merge($params, ['limit' => $per_page, 'offset' => $offset]));
    $users = $stmt->fetchAll();
} catch (Exception $e) {
    logError("Users fetch error: " . $e->getMessage());
}

// إحصائيات المستخدمين
$user_stats = [
    'total' => 0,
    'clients' => 0,
    'accountants' => 0,
    'admins' => 0,
    'active' => 0,
    'inactive' => 0
];

try {
    $stats_sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN role = 'client' THEN 1 ELSE 0 END) as clients,
                    SUM(CASE WHEN role = 'accountant' THEN 1 ELSE 0 END) as accountants,
                    SUM(CASE WHEN role = 'admin' THEN 1 ELSE 0 END) as admins,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active,
                    SUM(CASE WHEN is_active = 0 THEN 1 ELSE 0 END) as inactive
                  FROM users";
    
    $user_stats = fetchOne($stats_sql);
} catch (Exception $e) {
    logError("User stats error: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($success_message): ?>
            <div class="mb-6 p-4 rounded-lg bg-green-100 text-green-700 border border-green-300">
                <div class="flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="mb-6 p-4 rounded-lg bg-red-100 text-red-700 border border-red-300">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle ml-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-4">
                <a href="<?php echo getUrl(); ?>" class="hover:text-earth-green transition-colors">الرئيسية</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <a href="<?php echo getUrl('dashboard/admin'); ?>" class="hover:text-earth-green transition-colors">لوحة الإدارة</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <span class="text-earth-green">إدارة المستخدمين</span>
            </nav>
            
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-earth-green mb-2">إدارة المستخدمين</h1>
                    <p class="text-gray-600">عرض وإدارة جميع مستخدمي النظام</p>
                </div>
                <div class="mt-4 md:mt-0">
                    <a href="<?php echo getUrl('register.php'); ?>" 
                       class="bg-earth-green text-white px-6 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة مستخدم جديد
                    </a>
                </div>
            </div>
        </div>
        
        <!-- User Statistics -->
        <div class="grid md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
            
            <!-- Total Users -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي المستخدمين</p>
                        <p class="text-2xl font-bold text-earth-green"><?php echo $user_stats['total']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-earth-green rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Clients -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">العملاء</p>
                        <p class="text-2xl font-bold text-blue-600"><?php echo $user_stats['clients']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Accountants -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">المحاسبين</p>
                        <p class="text-2xl font-bold text-purple-600"><?php echo $user_stats['accountants']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-calculator text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Admins -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">المديرين</p>
                        <p class="text-2xl font-bold text-red-600"><?php echo $user_stats['admins']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-red-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-shield text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Active Users -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">نشط</p>
                        <p class="text-2xl font-bold text-green-600"><?php echo $user_stats['active']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Inactive Users -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">غير نشط</p>
                        <p class="text-2xl font-bold text-gray-600"><?php echo $user_stats['inactive']; ?></p>
                    </div>
                    <div class="w-12 h-12 bg-gray-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times-circle text-white text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Search and Filter -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <form method="GET" class="grid md:grid-cols-4 gap-4">
                
                <!-- Search -->
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                    <div class="relative">
                        <input type="text" 
                               id="search" 
                               name="search" 
                               value="<?php echo htmlspecialchars($search); ?>"
                               class="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                               placeholder="ابحث بالاسم، البريد، اسم المستخدم...">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>
                
                <!-- Role Filter -->
                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-2">الدور</label>
                    <select id="role" 
                            name="role"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                        <option value="">جميع الأدوار</option>
                        <option value="client" <?php echo $role_filter === 'client' ? 'selected' : ''; ?>>عميل</option>
                        <option value="accountant" <?php echo $role_filter === 'accountant' ? 'selected' : ''; ?>>محاسب</option>
                        <option value="admin" <?php echo $role_filter === 'admin' ? 'selected' : ''; ?>>مدير</option>
                    </select>
                </div>
                
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                    <select id="status" 
                            name="status"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                        <option value="">جميع الحالات</option>
                        <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>نشط</option>
                        <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>غير نشط</option>
                    </select>
                </div>
                
                <!-- Submit Button -->
                <div class="flex items-end">
                    <button type="submit" 
                            class="w-full bg-earth-green text-white px-6 py-2 rounded-lg font-semibold hover:bg-sand-brown transition-all duration-300">
                        <i class="fas fa-filter ml-2"></i>
                        تصفية
                    </button>
                </div>
            </form>
            
            <?php if (!empty($search) || !empty($role_filter) || !empty($status_filter)): ?>
                <div class="mt-4 flex items-center justify-between">
                    <span class="text-sm text-gray-600">
                        عرض <?php echo count($users); ?> من أصل <?php echo $total_users; ?> مستخدم
                    </span>
                    <a href="<?php echo getUrl('dashboard/admin/users.php'); ?>" 
                       class="text-sm text-earth-green hover:text-sand-brown transition-colors">
                        <i class="fas fa-times ml-1"></i>
                        مسح التصفية
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Users List -->
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold text-earth-green">
                        قائمة المستخدمين (<?php echo $total_users; ?>)
                    </h2>

                    <!-- Bulk Actions -->
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <form method="POST" id="bulkActionForm" class="flex items-center space-x-2 space-x-reverse">
                            <input type="hidden" name="action" value="bulk_action">
                            <select name="bulk_action" id="bulkAction"
                                    class="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-earth-green focus:border-earth-green">
                                <option value="">إجراءات متعددة</option>
                                <option value="activate">تفعيل</option>
                                <option value="deactivate">إلغاء تفعيل</option>
                                <option value="delete">حذف</option>
                            </select>
                            <button type="submit"
                                    class="bg-sand-brown text-white px-4 py-2 rounded-lg text-sm hover:bg-earth-green transition-colors"
                                    onclick="return confirmBulkAction()">
                                تطبيق
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="overflow-x-auto">
                <?php if (empty($users)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-users text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600 mb-4">
                            <?php if (!empty($search) || !empty($role_filter) || !empty($status_filter)): ?>
                                لا توجد مستخدمين تطابق معايير البحث
                            <?php else: ?>
                                لا توجد مستخدمين مسجلين حتى الآن
                            <?php endif; ?>
                        </p>
                    </div>
                <?php else: ?>
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right">
                                    <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-earth-green focus:ring-earth-green">
                                </th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المستخدم</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الدور</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الشركة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">تاريخ التسجيل</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($users as $u): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <input type="checkbox" name="selected_users[]" value="<?php echo $u['id']; ?>"
                                               class="user-checkbox rounded border-gray-300 text-earth-green focus:ring-earth-green">
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-earth-green rounded-full flex items-center justify-center ml-3">
                                                <span class="text-white font-semibold">
                                                    <?php echo mb_substr($u['full_name'], 0, 1); ?>
                                                </span>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo htmlspecialchars($u['full_name']); ?>
                                                </div>
                                                <div class="text-sm text-gray-500">
                                                    <?php echo htmlspecialchars($u['email']); ?>
                                                </div>
                                                <?php if ($u['phone']): ?>
                                                    <div class="text-xs text-gray-400">
                                                        <?php echo htmlspecialchars($u['phone']); ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $role_colors = [
                                            'client' => 'bg-blue-100 text-blue-800',
                                            'accountant' => 'bg-purple-100 text-purple-800',
                                            'admin' => 'bg-red-100 text-red-800'
                                        ];

                                        $role_names = [
                                            'client' => 'عميل',
                                            'accountant' => 'محاسب',
                                            'admin' => 'مدير'
                                        ];
                                        ?>
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $role_colors[$u['role']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                            <?php echo $role_names[$u['role']] ?? $u['role']; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($u['company_name'] ?? 'غير محدد'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $u['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                            <?php echo $u['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div><?php echo date('Y-m-d', strtotime($u['created_at'])); ?></div>
                                        <div class="text-xs"><?php echo date('H:i', strtotime($u['created_at'])); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <!-- Toggle Status -->
                                            <form method="POST" class="inline">
                                                <input type="hidden" name="action" value="toggle_status">
                                                <input type="hidden" name="user_id" value="<?php echo $u['id']; ?>">
                                                <button type="submit"
                                                        class="<?php echo $u['is_active'] ? 'text-red-600 hover:text-red-800' : 'text-green-600 hover:text-green-800'; ?> transition-colors"
                                                        title="<?php echo $u['is_active'] ? 'إلغاء تفعيل' : 'تفعيل'; ?>"
                                                        onclick="return confirm('هل أنت متأكد من تغيير حالة هذا المستخدم؟')">
                                                    <i class="fas <?php echo $u['is_active'] ? 'fa-ban' : 'fa-check'; ?>"></i>
                                                </button>
                                            </form>

                                            <!-- Edit User -->
                                            <a href="#"
                                               class="text-blue-600 hover:text-blue-800 transition-colors"
                                               title="تعديل"
                                               onclick="editUser(<?php echo $u['id']; ?>)">
                                                <i class="fas fa-edit"></i>
                                            </a>

                                            <!-- Delete User -->
                                            <?php if ($u['id'] !== $user['id']): ?>
                                                <form method="POST" class="inline">
                                                    <input type="hidden" name="action" value="delete_user">
                                                    <input type="hidden" name="user_id" value="<?php echo $u['id']; ?>">
                                                    <button type="submit"
                                                            class="text-red-600 hover:text-red-800 transition-colors"
                                                            title="حذف"
                                                            onclick="return confirm('هل أنت متأكد من حذف هذا المستخدم؟ هذا الإجراء لا يمكن التراجع عنه.')">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>

    <script>
        // Select All functionality
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.user-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // Confirm bulk action
        function confirmBulkAction() {
            const selectedUsers = document.querySelectorAll('.user-checkbox:checked');
            const bulkAction = document.getElementById('bulkAction').value;

            if (selectedUsers.length === 0) {
                alert('يرجى اختيار مستخدم واحد على الأقل');
                return false;
            }

            if (!bulkAction) {
                alert('يرجى اختيار إجراء');
                return false;
            }

            const actionNames = {
                'activate': 'تفعيل',
                'deactivate': 'إلغاء تفعيل',
                'delete': 'حذف'
            };

            return confirm(`هل أنت متأكد من ${actionNames[bulkAction]} ${selectedUsers.length} مستخدم؟`);
        }

        // Edit user function (placeholder)
        function editUser(userId) {
            alert('سيتم إضافة وظيفة التعديل قريباً');
        }
    </script>
</body>
</html>
