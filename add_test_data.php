<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إضافة البيانات التجريبية - محاسبك الرقمي</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #52796F;
            --secondary-color: #CAD2C5;
            --accent-color: #FFE5B4;
            --success-color: #28a745;
            --error-color: #dc3545;
            --warning-color: #ffc107;
            --text-dark: #333333;
            --text-light: #666666;
            --bg-light: #F0F0F0;
            --white: #FFFFFF;
            --shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: var(--white);
            border-radius: 20px;
            box-shadow: var(--shadow);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .icon {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        h1 {
            color: var(--text-dark);
            margin-bottom: 10px;
            font-size: 2rem;
        }

        .description {
            color: var(--text-light);
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .btn {
            display: inline-block;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--white);
        }

        .btn-primary:hover {
            background: #3d5a50;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: var(--text-dark);
        }

        .btn-secondary:hover {
            background: #b8c4b8;
            transform: translateY(-2px);
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: right;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
            margin: 20px 0;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .data-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: right;
        }

        .data-list h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .data-list ul {
            list-style: none;
            padding: 0;
        }

        .data-list li {
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .data-list li:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">
            <i class="fas fa-database"></i>
        </div>
        
        <h1>إضافة البيانات التجريبية</h1>
        <p class="description">
            هذه الأداة تساعدك على إضافة بيانات تجريبية لاختبار النظام والتطوير
        </p>

        <div class="data-list">
            <h3>البيانات التي سيتم إضافتها:</h3>
            <ul>
                <li>✅ 18 مستخدم (مدير + محاسبين + عملاء)</li>
                <li>✅ 6 خدمات محاسبية متنوعة</li>
                <li>✅ 30 طلب خدمة عشوائي</li>
                <li>✅ 50 إشعار تجريبي</li>
                <li>✅ مدفوعات عشوائية</li>
                <li>✅ 100 محادثة ذكاء اصطناعي</li>
                <li>✅ إعدادات النظام الإضافية</li>
            </ul>
        </div>

        <?php
        $message = '';
        $error = '';
        $success = false;

        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_data'])) {
            try {
                // Check if config file exists
                if (!file_exists(__DIR__ . '/config.php')) {
                    throw new Exception('ملف الإعدادات غير موجود! يرجى تشغيل معالج الإعداد أولاً.');
                }

                // Include database configuration
                require_once __DIR__ . '/backend/config/db.php';
                require_once __DIR__ . '/database/seed_data.php';

                // Create seeder instance and run
                $seeder = new DataSeeder($pdo);
                $seeder->run();

                $success = true;
                $message = 'تم إضافة جميع البيانات التجريبية بنجاح! يمكنك الآن تسجيل الدخول واختبار النظام.';

            } catch (Exception $e) {
                $error = 'خطأ في إضافة البيانات: ' . $e->getMessage();
            }
        }
        ?>

        <?php if ($message): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <?= htmlspecialchars($message) ?>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i>
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>جاري إضافة البيانات التجريبية...</p>
        </div>

        <?php if (!$success): ?>
            <form method="POST" id="seedForm">
                <button type="submit" name="add_data" class="btn btn-primary" onclick="showLoading()">
                    <i class="fas fa-plus"></i>
                    إضافة البيانات التجريبية
                </button>
            </form>
        <?php else: ?>
            <div style="margin-top: 30px;">
                <a href="index.php" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    الذهاب إلى الموقع
                </a>
                <a href="login.php" class="btn btn-secondary">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </a>
            </div>

            <div class="alert alert-success" style="margin-top: 20px;">
                <h4>بيانات تسجيل الدخول التجريبية:</h4>
                <p><strong>المدير:</strong> <EMAIL> / admin123</p>
                <p><strong>محاسب:</strong> <EMAIL> / acc123</p>
                <p><strong>عميل:</strong> <EMAIL> / client123</p>
            </div>
        <?php endif; ?>

        <div style="margin-top: 30px;">
            <a href="setup.php" class="btn btn-secondary">
                <i class="fas fa-cog"></i>
                معالج الإعداد
            </a>
        </div>
    </div>

    <script>
        function showLoading() {
            document.getElementById('loading').style.display = 'block';
            document.getElementById('seedForm').style.display = 'none';
        }
    </script>
</body>
</html>
