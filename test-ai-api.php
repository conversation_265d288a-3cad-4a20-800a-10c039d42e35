<?php
/**
 * اختبار API الذكاء الاصطناعي
 * AI API Test
 */

require_once 'backend/config/config.php';
require_once 'backend/ai/ai-helper.php';

// دالة تسجيل الأخطاء إذا لم تكن موجودة
if (!function_exists('logError')) {
    function logError($message) {
        echo "<div style='color: red; background: #fee; padding: 10px; margin: 10px 0; border: 1px solid #fcc; border-radius: 5px;'>";
        echo "<strong>خطأ:</strong> " . htmlspecialchars($message);
        echo "</div>";
    }
}

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار API الذكاء الاصطناعي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .response-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 اختبار API الذكاء الاصطناعي</h1>
        
        <!-- فحص الإعدادات -->
        <div class="test-section">
            <h2>1. فحص الإعدادات</h2>
            
            <?php
            echo "<h3>الثوابت المطلوبة:</h3>";
            
            $constants = [
                'OPENROUTER_API_KEY' => 'مفتاح OpenRouter API',
                'OPENROUTER_API_URL' => 'رابط OpenRouter API', 
                'AI_MODEL' => 'نموذج الذكاء الاصطناعي'
            ];
            
            $all_good = true;
            
            foreach ($constants as $constant => $description) {
                if (defined($constant)) {
                    $value = constant($constant);
                    if (!empty($value) && $value !== 'your_openrouter_api_key_here') {
                        echo "<div class='status success'>✅ <strong>$description</strong>: " . 
                             (strlen($value) > 30 ? substr($value, 0, 30) . '...' : $value) . "</div>";
                    } else {
                        echo "<div class='status error'>❌ <strong>$description</strong>: غير محدد أو فارغ</div>";
                        $all_good = false;
                    }
                } else {
                    echo "<div class='status error'>❌ <strong>$description</strong>: غير موجود</div>";
                    $all_good = false;
                }
            }
            
            if ($all_good) {
                echo "<div class='status success'>🎉 جميع الإعدادات صحيحة!</div>";
            } else {
                echo "<div class='status warning'>⚠️ يوجد مشاكل في الإعدادات</div>";
            }
            ?>
        </div>

        <!-- فحص الفئات والدوال -->
        <div class="test-section">
            <h2>2. فحص الفئات والدوال</h2>
            
            <?php
            if (class_exists('AIHelper')) {
                echo "<div class='status success'>✅ فئة AIHelper موجودة</div>";
                
                if (method_exists('AIHelper', 'callOpenRouterAPI')) {
                    echo "<div class='status success'>✅ دالة callOpenRouterAPI موجودة</div>";
                } else {
                    echo "<div class='status error'>❌ دالة callOpenRouterAPI غير موجودة</div>";
                }
            } else {
                echo "<div class='status error'>❌ فئة AIHelper غير موجودة</div>";
            }
            ?>
        </div>

        <!-- اختبار API -->
        <div class="test-section">
            <h2>3. اختبار API</h2>
            
            <?php if (isset($_POST['test_api'])): ?>
                <h3>نتيجة الاختبار:</h3>
                <?php
                try {
                    $test_message = "مرحبا، هذا اختبار بسيط. أجب بكلمة واحدة: مرحبا";
                    
                    echo "<div class='info'><strong>الرسالة المرسلة:</strong> $test_message</div>";
                    
                    $start_time = microtime(true);
                    $response = AIHelper::callOpenRouterAPI($test_message, 100, 0.7);
                    $end_time = microtime(true);
                    
                    $processing_time = round(($end_time - $start_time) * 1000);
                    
                    if ($response !== false && !empty($response)) {
                        echo "<div class='status success'>✅ API يعمل بشكل صحيح!</div>";
                        echo "<div class='info'><strong>وقت المعالجة:</strong> {$processing_time} مللي ثانية</div>";
                        echo "<div class='response-box'><strong>الرد:</strong>\n" . htmlspecialchars($response) . "</div>";
                    } else {
                        echo "<div class='status error'>❌ API لا يعمل أو أرجع رد فارغ</div>";
                        echo "<div class='info'>سيتم استخدام الردود المحلية كبديل</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='status error'>❌ خطأ في API: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
            ?>
            
            <form method="POST">
                <button type="submit" name="test_api">🧪 اختبار API الآن</button>
            </form>
            
            <?php else: ?>
                <p>اضغط على الزر أدناه لاختبار الاتصال بـ API:</p>
                <form method="POST">
                    <button type="submit" name="test_api">🧪 اختبار API الآن</button>
                </form>
            <?php endif; ?>
        </div>

        <!-- اختبار محادثة كاملة -->
        <div class="test-section">
            <h2>4. اختبار محادثة محاسبية</h2>
            
            <?php if (isset($_POST['test_accounting'])): ?>
                <h3>نتيجة الاختبار المحاسبي:</h3>
                <?php
                try {
                    $accounting_question = "كيف أحسب ضريبة القيمة المضافة في الجزائر؟";
                    
                    // إنشاء prompt مخصص
                    $prompt = "أنت مساعد محاسبي ذكي متخصص في المحاسبة والضرائب في الجزائر.\n\n";
                    $prompt .= "سؤال العميل: " . $accounting_question . "\n\n";
                    $prompt .= "الرد:";
                    
                    echo "<div class='info'><strong>السؤال:</strong> $accounting_question</div>";
                    
                    $start_time = microtime(true);
                    $response = AIHelper::callOpenRouterAPI($prompt, 300, 0.7);
                    $end_time = microtime(true);
                    
                    $processing_time = round(($end_time - $start_time) * 1000);
                    
                    if ($response !== false && !empty($response)) {
                        echo "<div class='status success'>✅ الاختبار المحاسبي نجح!</div>";
                        echo "<div class='info'><strong>وقت المعالجة:</strong> {$processing_time} مللي ثانية</div>";
                        echo "<div class='response-box'><strong>الرد المحاسبي:</strong>\n" . htmlspecialchars($response) . "</div>";
                    } else {
                        echo "<div class='status error'>❌ فشل الاختبار المحاسبي</div>";
                    }
                    
                } catch (Exception $e) {
                    echo "<div class='status error'>❌ خطأ في الاختبار المحاسبي: " . htmlspecialchars($e->getMessage()) . "</div>";
                }
            ?>
            
            <form method="POST">
                <button type="submit" name="test_accounting">📊 اختبار السؤال المحاسبي</button>
            </form>
            
            <?php else: ?>
                <p>اختبار سؤال محاسبي حقيقي:</p>
                <form method="POST">
                    <button type="submit" name="test_accounting">📊 اختبار السؤال المحاسبي</button>
                </form>
            <?php endif; ?>
        </div>

        <!-- الخلاصة -->
        <div class="test-section">
            <h2>5. الخلاصة والتوصيات</h2>
            
            <?php
            $api_configured = defined('OPENROUTER_API_KEY') && !empty(OPENROUTER_API_KEY) && OPENROUTER_API_KEY !== 'your_openrouter_api_key_here';
            $class_exists = class_exists('AIHelper');
            
            if ($api_configured && $class_exists) {
                echo "<div class='status success'>🎉 <strong>النظام جاهز!</strong> يمكن استخدام المساعد الذكي مع API حقيقي.</div>";
                echo "<p><strong>الخطوة التالية:</strong> اذهب إلى <a href='ai-assistant.php' target='_blank'>المساعد الذكي</a> وجرب طرح أسئلة محاسبية.</p>";
            } else {
                echo "<div class='status warning'>⚠️ <strong>النظام يعمل في الوضع المحلي.</strong></div>";
                echo "<p>سيتم استخدام الردود المحلية المبرمجة مسبقاً بدلاً من API.</p>";
                
                if (!$api_configured) {
                    echo "<p><strong>لتفعيل API:</strong> تأكد من إعداد OPENROUTER_API_KEY في ملف config.php</p>";
                }
            }
            ?>
            
            <div style="margin-top: 20px; padding: 15px; background: #e7f3ff; border: 1px solid #b3d9ff; border-radius: 5px;">
                <h4>📝 معلومات مفيدة:</h4>
                <ul>
                    <li><strong>النموذج المستخدم:</strong> <?php echo defined('AI_MODEL') ? AI_MODEL : 'غير محدد'; ?></li>
                    <li><strong>رابط API:</strong> <?php echo defined('OPENROUTER_API_URL') ? OPENROUTER_API_URL : 'غير محدد'; ?></li>
                    <li><strong>حالة API:</strong> <?php echo $api_configured ? 'مُعد بشكل صحيح' : 'غير مُعد'; ?></li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
