<?php
/**
 * Mock Payment Checkout
 * صفحة الدفع التجريبية
 */

require_once '../backend/config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'client') {
    redirect(getUrl('login.php'));
}

// الحصول على معرف الطلب
$request_id = (int)($_GET['request_id'] ?? 0);

if (!$request_id) {
    showMessage('معرف الطلب مطلوب', 'error');
    redirect(getUrl('dashboard/client'));
}

// الحصول على تفاصيل الطلب
$request = null;
try {
    $sql = "SELECT sr.*, s.service_name_ar, u.full_name as client_name 
            FROM service_requests sr 
            LEFT JOIN services s ON sr.service_id = s.id 
            LEFT JOIN users u ON sr.user_id = u.id 
            WHERE sr.id = :id AND sr.user_id = :user_id";
    
    $request = fetchOne($sql, ['id' => $request_id, 'user_id' => $user['id']]);
} catch (Exception $e) {
    logError("Payment checkout error: " . $e->getMessage());
}

if (!$request) {
    showMessage('الطلب غير موجود أو غير مصرح لك بالوصول إليه', 'error');
    redirect(getUrl('dashboard/client'));
}

// التحقق من حالة الطلب
if ($request['payment_status'] === 'paid') {
    showMessage('تم دفع هذا الطلب مسبقاً', 'info');
    redirect(getUrl('dashboard/client/request-details.php?id=' . $request_id));
}

// معالجة عملية الدفع
$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['process_payment'])) {
    $payment_method = sanitize($_POST['payment_method'] ?? '');
    $card_number = sanitize($_POST['card_number'] ?? '');
    $card_holder = sanitize($_POST['card_holder'] ?? '');
    $expiry_month = sanitize($_POST['expiry_month'] ?? '');
    $expiry_year = sanitize($_POST['expiry_year'] ?? '');
    $cvv = sanitize($_POST['cvv'] ?? '');
    
    // التحقق من البيانات
    if (empty($payment_method)) {
        $error_message = 'يرجى اختيار طريقة الدفع';
    } elseif ($payment_method === 'golden_card') {
        if (empty($card_number) || empty($card_holder) || empty($expiry_month) || empty($expiry_year) || empty($cvv)) {
            $error_message = 'يرجى ملء جميع بيانات البطاقة الذهبية';
        } elseif (!preg_match('/^\d{16}$/', str_replace(' ', '', $card_number))) {
            $error_message = 'رقم البطاقة الذهبية غير صحيح';
        } elseif (!preg_match('/^\d{3,4}$/', $cvv)) {
            $error_message = 'رمز الأمان غير صحيح';
        }
    }
    
    if (!$error_message) {
        try {
            // محاكاة عملية الدفع
            $payment_result = processPayment($request, $payment_method, [
                'card_number' => $card_number,
                'card_holder' => $card_holder,
                'expiry_month' => $expiry_month,
                'expiry_year' => $expiry_year,
                'cvv' => $cvv
            ]);
            
            if ($payment_result['success']) {
                showMessage('تم الدفع بنجاح! شكراً لك', 'success');
                redirect(getUrl('payment/success.php?request_id=' . $request_id));
            } else {
                $error_message = $payment_result['message'];
            }
            
        } catch (Exception $e) {
            $error_message = 'حدث خطأ أثناء معالجة الدفع. يرجى المحاولة مرة أخرى';
            logError("Payment processing error: " . $e->getMessage());
        }
    }
}

/**
 * معالجة الدفع التجريبي
 */
function processPayment($request, $payment_method, $payment_data) {
    global $database;
    
    // محاكاة تأخير الشبكة
    sleep(2);
    
    // محاكاة نسبة نجاح 95%
    $success_rate = 95;
    $random = rand(1, 100);
    
    if ($random > $success_rate) {
        return [
            'success' => false,
            'message' => 'فشل في معالجة الدفع. يرجى التحقق من بيانات البطاقة والمحاولة مرة أخرى'
        ];
    }
    
    // إنشاء معرف معاملة وهمي
    $transaction_id = 'TXN_' . time() . '_' . rand(1000, 9999);
    
    try {
        // حفظ بيانات الدفع
        $payment_data_db = [
            'request_id' => $request['id'],
            'user_id' => $request['user_id'],
            'amount' => $request['total_amount'],
            'payment_method' => $payment_method,
            'transaction_id' => $transaction_id,
            'payment_status' => 'completed',
            'notes' => 'دفع تجريبي - ' . ($payment_method === 'golden_card' ? 'البطاقة الذهبية' : $payment_method)
        ];
        
        $payment_id = $database->insert('payments', $payment_data_db);
        
        // تحديث حالة الطلب
        $database->update('service_requests', 
            ['payment_status' => 'paid'], 
            'id = :id', 
            ['id' => $request['id']]
        );
        
        // إنشاء إشعار للإدارة
        $notification_data = [
            'user_id' => 1, // المدير
            'title' => 'دفع جديد',
            'message' => "تم استلام دفع بقيمة {$request['total_amount']} ريال للطلب رقم {$request['id']}",
            'type' => 'success',
            'related_request_id' => $request['id']
        ];
        $database->insert('notifications', $notification_data);
        
        return [
            'success' => true,
            'transaction_id' => $transaction_id,
            'payment_id' => $payment_id
        ];
        
    } catch (Exception $e) {
        logError("Payment database error: " . $e->getMessage());
        return [
            'success' => false,
            'message' => 'خطأ في حفظ بيانات الدفع'
        ];
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الدفع - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl w-full space-y-8">
            
            <!-- Header -->
            <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                    <i class="fas fa-credit-card text-white text-2xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-earth-green mb-2">
                    إتمام عملية الدفع
                </h1>
                <p class="text-gray-600">
                    دفع آمن ومحمي بالبطاقة الذهبية - نظام تجريبي
                </p>

                <!-- Golden Card Welcome Message -->
                <!-- <div class="bg-gradient-to-r from-yellow-50 to-yellow-100 border border-yellow-200 rounded-lg p-4 mt-4 max-w-md mx-auto">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-star text-yellow-500 ml-2"></i>
                        <span class="text-yellow-800 font-semibold">مرحباً بك في نادي البطاقة الذهبية</span>
                        <i class="fas fa-star text-yellow-500 mr-2"></i>
                    </div>
                    <p class="text-yellow-700 text-sm">
                        استمتع بخدمة دفع حصرية ومميزة مع مزايا إضافية
                    </p>
                </div> -->
            </div>
            
            <div class="grid lg:grid-cols-3 gap-8">
                
                <!-- Payment Form -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-lg shadow-lg p-8">
                        
                        <!-- Error Message -->
                        <?php if ($error_message): ?>
                            <div class="mb-6 p-4 rounded-lg bg-red-100 text-red-700 border border-red-300">
                                <div class="flex items-center">
                                    <i class="fas fa-exclamation-circle ml-2"></i>
                                    <?php echo htmlspecialchars($error_message); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" id="paymentForm" class="space-y-6">
                            <input type="hidden" name="process_payment" value="1">
                            
                            <!-- Payment Method -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-4">
                                    طريقة الدفع <span class="text-red-500">*</span>
                                </label>
                                
                                <div class="space-y-3">
                                    <label class="flex items-center p-4 border-2 border-yellow-400 bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg cursor-pointer hover:from-yellow-100 hover:to-yellow-200 transition-all duration-300">
                                        <input type="radio" name="payment_method" value="golden_card" class="ml-3" checked>
                                        <div class="flex items-center">
                                            <i class="fas fa-credit-card text-yellow-600 text-xl ml-3"></i>
                                            <div>
                                                <div class="font-medium text-yellow-800">البطاقة الذهبية</div>
                                                <div class="text-sm text-yellow-600">بطاقة دفع حصرية ومميزة</div>
                                            </div>
                                        </div>
                                        <div class="mr-auto">
                                            <i class="fas fa-star text-yellow-500"></i>
                                        </div>
                                    </label>
                                </div>
                            </div>
                            
                            <!-- Golden Card Details -->
                            <div id="creditCardDetails" class="space-y-4">
                                <h3 class="text-lg font-semibold text-yellow-600 flex items-center">
                                    <i class="fas fa-star text-yellow-500 ml-2"></i>
                                    بيانات البطاقة الذهبية
                                </h3>
                                
                                <div>
                                    <label for="card_number" class="block text-sm font-medium text-yellow-700 mb-2">
                                        <i class="fas fa-credit-card text-yellow-600 ml-1"></i>
                                        رقم البطاقة الذهبية <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text"
                                           id="card_number"
                                           name="card_number"
                                           placeholder="1234 5678 9012 3456"
                                           maxlength="19"
                                           class="w-full px-4 py-3 border-2 border-yellow-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 transition-colors bg-yellow-50"
                                           required>
                                </div>
                                
                                <div>
                                    <label for="card_holder" class="block text-sm font-medium text-yellow-700 mb-2">
                                        <i class="fas fa-user text-yellow-600 ml-1"></i>
                                        اسم حامل البطاقة الذهبية <span class="text-red-500">*</span>
                                    </label>
                                    <input type="text"
                                           id="card_holder"
                                           name="card_holder"
                                           value="<?php echo htmlspecialchars($user['full_name']); ?>"
                                           class="w-full px-4 py-3 border-2 border-yellow-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 transition-colors bg-yellow-50"
                                           required>
                                </div>
                                
                                <div class="grid grid-cols-3 gap-4">
                                    <div>
                                        <label for="expiry_month" class="block text-sm font-medium text-gray-700 mb-2">
                                            الشهر <span class="text-red-500">*</span>
                                        </label>
                                        <select id="expiry_month" 
                                                name="expiry_month"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                                            <option value="">الشهر</option>
                                            <?php for ($i = 1; $i <= 12; $i++): ?>
                                                <option value="<?php echo sprintf('%02d', $i); ?>"><?php echo sprintf('%02d', $i); ?></option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label for="expiry_year" class="block text-sm font-medium text-gray-700 mb-2">
                                            السنة <span class="text-red-500">*</span>
                                        </label>
                                        <select id="expiry_year" 
                                                name="expiry_year"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                                            <option value="">السنة</option>
                                            <?php for ($i = date('Y'); $i <= date('Y') + 10; $i++): ?>
                                                <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                            <?php endfor; ?>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label for="cvv" class="block text-sm font-medium text-gray-700 mb-2">
                                            CVV <span class="text-red-500">*</span>
                                        </label>
                                        <input type="text" 
                                               id="cvv" 
                                               name="cvv" 
                                               placeholder="123"
                                               maxlength="4"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="pt-6">
                                <button type="submit"
                                        id="payButton"
                                        class="w-full bg-gradient-to-r from-yellow-500 to-yellow-600 text-white py-4 px-6 rounded-lg font-semibold text-lg hover:from-yellow-600 hover:to-yellow-700 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 shadow-lg">
                                    <i class="fas fa-star ml-2"></i>
                                    <span id="payButtonText">دفع بالبطاقة الذهبية <?php echo formatCurrency($request['total_amount'] * 1.19); ?></span>
                                    <div id="payButtonSpinner" class="hidden">
                                        <i class="fas fa-spinner fa-spin ml-2"></i>
                                        جاري المعالجة بالبطاقة الذهبية...
                                    </div>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="space-y-6">

                    <!-- Order Details -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-earth-green mb-4">
                            <i class="fas fa-receipt ml-2"></i>
                            ملخص الطلب
                        </h3>

                        <div class="space-y-4">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h4 class="font-medium text-gray-900">
                                        <?php echo htmlspecialchars($request['request_title']); ?>
                                    </h4>
                                    <p class="text-sm text-gray-600">
                                        <?php echo htmlspecialchars($request['service_name_ar']); ?>
                                    </p>
                                    <p class="text-xs text-gray-500 mt-1">
                                        طلب رقم: #<?php echo $request['id']; ?>
                                    </p>
                                </div>
                                <div class="text-right">
                                    <p class="font-semibold text-earth-green">
                                        <?php echo formatCurrency($request['total_amount']); ?>
                                    </p>
                                </div>
                            </div>

                            <div class="border-t border-gray-200 pt-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm text-gray-600">المبلغ الفرعي:</span>
                                    <span class="text-sm"><?php echo formatCurrency($request['total_amount']); ?></span>
                                </div>
                                <div class="flex justify-between items-center mt-2">
                                    <span class="text-sm text-gray-600">ضريبة القيمة المضافة (19%):</span>
                                    <span class="text-sm"><?php echo formatCurrency($request['total_amount'] * 0.19); ?></span>
                                </div>
                                <div class="flex justify-between items-center mt-3 pt-3 border-t border-gray-200">
                                    <span class="font-semibold">المجموع الكلي:</span>
                                    <span class="font-semibold text-lg text-earth-green">
                                        <?php echo formatCurrency($request['total_amount'] * 1.19); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Security Info -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex items-start space-x-3 space-x-reverse">
                            <i class="fas fa-shield-alt text-green-600 mt-1"></i>
                            <div class="text-sm">
                                <h4 class="font-semibold text-green-800 mb-1">دفع آمن ومحمي</h4>
                                <p class="text-green-700">
                                    جميع المعاملات محمية بتشفير SSL 256-bit.
                                    لا نحتفظ ببيانات بطاقتك الائتمانية.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Test Mode Notice -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div class="flex items-start space-x-3 space-x-reverse">
                            <i class="fas fa-exclamation-triangle text-yellow-600 mt-1"></i>
                            <div class="text-sm">
                                <h4 class="font-semibold text-yellow-800 mb-1">وضع تجريبي</h4>
                                <p class="text-yellow-700">
                                    هذا نظام دفع تجريبي. لن يتم خصم أي مبلغ فعلي.
                                    استخدم أي أرقام للاختبار.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Support -->
                    <!-- <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-earth-green mb-4">
                            <i class="fas fa-headset ml-2"></i>
                            تحتاج مساعدة؟
                        </h3>

                        <div class="space-y-3 text-sm">
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <i class="fas fa-phone text-earth-green"></i>
                                <span><?php echo SITE_PHONE; ?></span>
                            </div>
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <i class="fas fa-envelope text-earth-green"></i>
                                <span><?php echo SITE_EMAIL; ?></span>
                            </div>
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <i class="fas fa-clock text-earth-green"></i>
                                <span>متاح 24/7</span>
                            </div>
                        </div>
                    </div> -->
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>

    <script>
        // Format card number input
        document.getElementById('card_number').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\s/g, '').replace(/[^0-9]/gi, '');
            let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
            e.target.value = formattedValue;
        });

        // CVV input validation
        document.getElementById('cvv').addEventListener('input', function(e) {
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
        });

        // Payment method change handler
        document.querySelectorAll('input[name="payment_method"]').forEach(function(radio) {
            radio.addEventListener('change', function() {
                const creditCardDetails = document.getElementById('creditCardDetails');
                if (this.value === 'golden_card') {
                    creditCardDetails.style.display = 'block';
                } else {
                    creditCardDetails.style.display = 'none';
                }
            });
        });

        // Show card details by default since golden card is the only option
        document.getElementById('creditCardDetails').style.display = 'block';

        // Form submission handler
        document.getElementById('paymentForm').addEventListener('submit', function(e) {
            const payButton = document.getElementById('payButton');
            const payButtonText = document.getElementById('payButtonText');
            const payButtonSpinner = document.getElementById('payButtonSpinner');

            // Disable button and show spinner
            payButton.disabled = true;
            payButtonText.style.display = 'none';
            payButtonSpinner.classList.remove('hidden');

            // Re-enable button after 10 seconds (in case of error)
            setTimeout(function() {
                payButton.disabled = false;
                payButtonText.style.display = 'inline';
                payButtonSpinner.classList.add('hidden');
            }, 10000);
        });

        // Auto-fill test data for demo
        function fillTestData() {
            document.getElementById('card_number').value = '4111 1111 1111 1111';
            document.getElementById('expiry_month').value = '12';
            document.getElementById('expiry_year').value = '2025';
            document.getElementById('cvv').value = '123';
        }

        // Add test data button for demo
        if (window.location.hostname === 'localhost') {
            const testButton = document.createElement('button');
            testButton.type = 'button';
            testButton.className = 'text-sm text-blue-600 hover:text-blue-800 transition-colors mt-2';
            testButton.innerHTML = '<i class="fas fa-magic ml-1"></i> ملء بيانات تجريبية';
            testButton.onclick = fillTestData;

            const cardNumberField = document.getElementById('card_number');
            cardNumberField.parentNode.appendChild(testButton);
        }
    </script>
</body>
</html>
