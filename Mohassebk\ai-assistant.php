<?php
/**
 * AI Assistant Page - Simple Version
 * صفحة المساعد الذكي - نسخة بسيطة
 */

require_once 'backend/config/config.php';
require_once 'backend/ai/ai-helper.php';

// إنشاء جدول المحادثات إذا لم يكن موجوداً
try {
    $create_table_sql = "CREATE TABLE IF NOT EXISTS `ai_conversations` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `user_id` int(11) DEFAULT NULL,
      `session_id` varchar(255) NOT NULL,
      `message` text NOT NULL,
      `response` text NOT NULL,
      `model_used` varchar(100) DEFAULT NULL,
      `tokens_used` int(11) DEFAULT 0,
      `processing_time` int(11) DEFAULT 0,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `user_id` (`user_id`),
      KEY `session_id` (`session_id`),
      <PERSON><PERSON><PERSON> `created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    global $database;
    $database->query($create_table_sql);
} catch (Exception $e) {
    // تجاهل الخطأ إذا كان الجدول موجود بالفعل
}

// معالجة الرسائل
$response = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['message'])) {
    $message = trim($_POST['message']);

    if (!empty($message)) {
        try {
            $start_time = microtime(true);

            // إنشاء prompt مخصص للمحاسبة الجزائرية
            $prompt = createAccountingPrompt($message);

            // استدعاء AI API
            $ai_response = AIHelper::callOpenRouterAPI($prompt, 800, 0.7);

            if ($ai_response !== false) {
                $response = $ai_response;

                // حساب وقت المعالجة
                $processing_time = round((microtime(true) - $start_time) * 1000); // milliseconds

                // حفظ المحادثة (اختياري)
                $session_id = session_id();
                try {
                    AIHelper::saveConversation(null, $session_id, $message, $response, AI_MODEL, 0, $processing_time);
                } catch (Exception $e) {
                    // تجاهل أخطاء حفظ المحادثة
                    logError("Save conversation error: " . $e->getMessage());
                }

            } else {
                // في حالة فشل AI، استخدم الردود الجاهزة كـ fallback
                $response = getFallbackResponse($message);
            }

        } catch (Exception $e) {
            $error_message = 'عذراً، حدث خطأ أثناء التواصل مع المساعد الذكي. يرجى المحاولة مرة أخرى.';
            logError("AI Assistant error: " . $e->getMessage());

            // استخدام الردود الجاهزة كـ fallback
            $response = getFallbackResponse($message);
        }
    } else {
        $error_message = 'يرجى كتابة رسالة قبل الإرسال.';
    }
}

/**
 * إنشاء prompt مخصص للمحاسبة الجزائرية
 */
function createAccountingPrompt($user_message) {
    $prompt = "أنت مساعد محاسبي ذكي متخصص في المحاسبة والضرائب في الجزائر. لديك خبرة واسعة في:\n\n";

    $prompt .= "المجالات المحاسبية:\n";
    $prompt .= "- القوانين المحاسبية الجزائرية\n";
    $prompt .= "- النظام المحاسبي المالي (SCF)\n";
    $prompt .= "- ضريبة القيمة المضافة (TVA) 19%\n";
    $prompt .= "- ضريبة الدخل الإجمالي (IRG)\n";
    $prompt .= "- ضريبة أرباح الشركات (IBS)\n";
    $prompt .= "- الرسم على النشاط المهني (TAP)\n";
    $prompt .= "- أنواع الشركات: SARL, SPA, EURL, المؤسسة الفردية\n";
    $prompt .= "- إعداد القوائم المالية والميزانيات\n";
    $prompt .= "- التصريحات الضريبية والجمركية\n";
    $prompt .= "- المراجعة والتدقيق المحاسبي\n\n";

    $prompt .= "تعليمات الرد:\n";
    $prompt .= "1. اجعل إجابتك باللغة العربية\n";
    $prompt .= "2. كن دقيقاً ومهنياً\n";
    $prompt .= "3. اذكر المراجع القانونية عند الضرورة\n";
    $prompt .= "4. قدم أمثلة عملية عند الإمكان\n";
    $prompt .= "5. اجعل الرد مفهوماً وواضحاً\n";
    $prompt .= "6. لا تتجاوز 500 كلمة\n\n";

    $prompt .= "سؤال العميل: " . $user_message . "\n\n";
    $prompt .= "الرد:";

    return $prompt;
}

/**
 * الردود الجاهزة كـ fallback
 */
function getFallbackResponse($message) {
    $responses = [
        'مرحبا' => 'مرحباً بك في المساعد المحاسبي الذكي! أنا هنا لمساعدتك في جميع الأسئلة المحاسبية والضريبية المتعلقة بالجزائر. كيف يمكنني مساعدتك اليوم؟',
        'كيف حالك' => 'أنا بخير وجاهز لمساعدتك! كيف يمكنني مساعدتك في أمورك المحاسبية والضريبية؟',
        'ما هي خدماتكم' => 'نحن نقدم خدمات محاسبية شاملة تشمل: إعداد القوائم المالية، المراجعة والتدقيق، الاستشارات الضريبية، إعداد التصريحات الضريبية، إدارة الحسابات، وتأسيس الشركات.',
        'كيف أحسب ضريبة القيمة المضافة' => 'ضريبة القيمة المضافة في الجزائر هي 19%. طريقة الحساب:\n- المبلغ الخاضع للضريبة × 0.19 = قيمة الضريبة\n- المبلغ الإجمالي = المبلغ الأساسي + الضريبة\n\nمثال: إذا كان المبلغ 1000 د.ج\nالضريبة = 1000 × 0.19 = 190 د.ج\nالمبلغ الإجمالي = 1000 + 190 = 1190 د.ج',
        'ما هي أنواع الشركات' => 'الأنواع الرئيسية للشركات في الجزائر:\n\n1. SARL - الشركة ذات المسؤولية المحدودة\n2. SPA - شركة المساهمة\n3. EURL - الشركة ذات الشخص الواحد\n4. المؤسسة الفردية\n5. SNC - شركة التضامن\n6. SCS - الشركة البسيطة بالأسهم\n\nكل نوع له متطلبات رأس مال وإجراءات تأسيس مختلفة.',
        'كيف أسجل شركة' => 'خطوات تسجيل شركة في الجزائر:\n\n1. إعداد القانون الأساسي عند الموثق\n2. إيداع رأس المال في البنك\n3. التسجيل في السجل التجاري\n4. النشر في الجريدة الرسمية\n5. الحصول على البطاقة الجبائية\n6. التسجيل في الضمان الاجتماعي\n7. فتح حساب بنكي للشركة\n\nالمدة الإجمالية: 15-30 يوم عمل',
        'ما هي الضرائب المطلوبة' => 'الضرائب الرئيسية في الجزائر:\n\n1. IRG - ضريبة الدخل الإجمالي\n2. IBS - ضريبة أرباح الشركات\n3. TVA - ضريبة القيمة المضافة (19%)\n4. TAP - الرسم على النشاط المهني\n5. VF - الرسم العقاري\n6. ضرائب أخرى حسب النشاط\n\nيجب تقديم التصريحات شهرياً أو سنوياً حسب النوع.',
    ];

    // البحث عن رد مناسب
    foreach ($responses as $keyword => $reply) {
        if (stripos($message, $keyword) !== false) {
            return $reply;
        }
    }

    // رد افتراضي
    return 'شكراً لسؤالك المهم! كمساعد محاسبي متخصص في القوانين الجزائرية، أنصحك بالتواصل مع أحد محاسبينا المعتمدين للحصول على استشارة مفصلة ودقيقة حول موضوعك. يمكنك طلب خدمة استشارة من خلال الموقع أو الاتصال بنا مباشرة.';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المساعد المحاسبي الذكي - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include 'frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-earth-green rounded-full mb-4">
                <i class="fas fa-robot text-white text-3xl"></i>
            </div>
            <h1 class="text-4xl font-bold text-earth-green mb-4">المساعد المحاسبي الذكي</h1>
            <p class="text-gray-600 text-lg max-w-2xl mx-auto mb-4">
                احصل على إجابات فورية لأسئلتك المحاسبية والضريبية من الذكاء الاصطناعي المتخصص في القوانين الجزائرية.
            </p>
            <div class="flex items-center justify-center space-x-4 space-x-reverse text-sm text-gray-500">
                <div class="flex items-center">
                    <i class="fas fa-robot text-earth-green ml-1"></i>
                    <span>مدعوم بالذكاء الاصطناعي</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-clock text-earth-green ml-1"></i>
                    <span>متاح 24/7</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-earth-green ml-1"></i>
                    <span>معلومات محدثة</span>
                </div>
            </div>
        </div>
        
        <!-- Chat Container -->
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg">
                
                <!-- Chat Messages -->
                <div class="p-6 h-96 overflow-y-auto border-b border-gray-200">
                    
                    <!-- Welcome Message -->
                    <div class="flex items-start space-x-3 space-x-reverse mb-6">
                        <div class="w-10 h-10 bg-earth-green rounded-full flex items-center justify-center">
                            <i class="fas fa-robot text-white"></i>
                        </div>
                        <div class="bg-gray-100 rounded-lg p-4 max-w-md">
                            <p class="text-gray-800">مرحباً! أنا المساعد المحاسبي الذكي المدعوم بالذكاء الاصطناعي 🤖</p>
                            <p class="text-gray-600 text-sm mt-2">
                                متخصص في المحاسبة والضرائب الجزائرية • أجيب على أسئلتك فوراً • متاح 24/7
                            </p>
                            <p class="text-gray-800 mt-3">كيف يمكنني مساعدتك اليوم؟</p>
                        </div>
                    </div>
                    
                    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['message'])): ?>
                        <!-- User Message -->
                        <div class="flex items-start space-x-3 space-x-reverse mb-6 justify-end">
                            <div class="bg-earth-green text-white rounded-lg p-4 max-w-md">
                                <p><?php echo nl2br(htmlspecialchars($_POST['message'])); ?></p>
                            </div>
                            <div class="w-10 h-10 bg-sand-brown rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                        </div>
                        
                        <!-- AI Response -->
                        <?php if ($response): ?>
                            <div class="flex items-start space-x-3 space-x-reverse mb-6">
                                <div class="w-10 h-10 bg-earth-green rounded-full flex items-center justify-center">
                                    <i class="fas fa-robot text-white"></i>
                                </div>
                                <div class="bg-gray-100 rounded-lg p-4 max-w-md">
                                    <p class="text-gray-800 whitespace-pre-line"><?php echo htmlspecialchars($response); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Error Message -->
                        <?php if ($error_message): ?>
                            <div class="flex items-start space-x-3 space-x-reverse mb-6">
                                <div class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-exclamation-triangle text-white"></i>
                                </div>
                                <div class="bg-red-100 border border-red-300 rounded-lg p-4 max-w-md">
                                    <p class="text-red-700"><?php echo htmlspecialchars($error_message); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                
                <!-- Chat Input -->
                <div class="p-6">
                    <form method="POST" class="flex items-end space-x-4 space-x-reverse">
                        <div class="flex-1">
                            <textarea 
                                name="message" 
                                id="messageInput"
                                placeholder="اكتب سؤالك هنا..."
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green resize-none"
                                rows="3"
                                required></textarea>
                        </div>
                        <button
                            type="submit"
                            id="submitBtn"
                            class="bg-earth-green text-white px-6 py-3 rounded-lg hover:bg-sand-brown transition-colors flex items-center">
                            <span id="submitText">
                                <i class="fas fa-paper-plane ml-2"></i>
                                إرسال
                            </span>
                            <span id="loadingText" class="hidden">
                                <i class="fas fa-spinner fa-spin ml-2"></i>
                                جاري المعالجة...
                            </span>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Quick Questions -->
            <div class="mt-8">
                <h3 class="text-xl font-semibold text-earth-green mb-4 text-center">أسئلة شائعة</h3>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">

                    <button onclick="askQuestion('كيف أحسب ضريبة القيمة المضافة في الجزائر؟')"
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-calculator text-earth-green ml-2"></i>
                        كيف أحسب ضريبة القيمة المضافة؟
                    </button>

                    <button onclick="askQuestion('ما هي أنواع الشركات في الجزائر؟')"
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-building text-earth-green ml-2"></i>
                        ما هي أنواع الشركات؟
                    </button>

                    <button onclick="askQuestion('كيف أسجل شركة جديدة؟')"
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-file-alt text-earth-green ml-2"></i>
                        كيف أسجل شركة جديدة؟
                    </button>

                    <button onclick="askQuestion('ما هي الضرائب المطلوبة؟')"
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-receipt text-earth-green ml-2"></i>
                        ما هي الضرائب المطلوبة؟
                    </button>

                    <button onclick="askQuestion('كيف أعد القوائم المالية؟')"
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-chart-line text-earth-green ml-2"></i>
                        كيف أعد القوائم المالية؟
                    </button>

                    <button onclick="askQuestion('كيف أختار محاسب مناسب؟')"
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-user-tie text-earth-green ml-2"></i>
                        كيف أختار محاسب مناسب؟
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'frontend/components/footer.php'; ?>
    
    <!-- Scripts -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // Ask predefined question
        function askQuestion(question) {
            document.getElementById('messageInput').value = question;
            document.getElementById('messageInput').focus();
        }

        // Auto-resize textarea
        document.getElementById('messageInput').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // Auto-scroll to bottom
        function scrollToBottom() {
            const chatContainer = document.querySelector('.overflow-y-auto');
            if (chatContainer) {
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
        }

        // Handle form submission with loading state
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const loadingText = document.getElementById('loadingText');
            const messageInput = document.getElementById('messageInput');

            // Show loading state
            submitText.classList.add('hidden');
            loadingText.classList.remove('hidden');
            submitBtn.disabled = true;
            messageInput.disabled = true;

            // The form will submit normally, but we show loading state
        });

        // Scroll to bottom on page load
        window.addEventListener('load', function() {
            scrollToBottom();
        });

        // Add some visual feedback for AI responses
        <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($response)): ?>
        // Highlight the AI response briefly
        setTimeout(function() {
            const aiResponses = document.querySelectorAll('.bg-gray-100');
            if (aiResponses.length > 1) {
                const lastResponse = aiResponses[aiResponses.length - 1];
                lastResponse.style.backgroundColor = '#e8f5e8';
                setTimeout(function() {
                    lastResponse.style.backgroundColor = '';
                }, 2000);
            }
        }, 500);
        <?php endif; ?>
    </script>
</body>
</html>
