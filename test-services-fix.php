<?php
/**
 * اختبار إصلاح مشكلة الخدمات
 * Test Services Fix
 */

require_once 'backend/config/config.php';

echo "<h1>اختبار إصلاح مشكلة الخدمات</h1>";

try {
    // الاتصال بقاعدة البيانات
    $pdo = getDBConnection();
    
    echo "<h2>1. فحص هيكل جدول الخدمات</h2>";
    
    // الحصول على أعمدة جدول الخدمات
    $columns = $pdo->query("DESCRIBE services")->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr><th>اسم العمود</th><th>النوع</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    
    $expected_columns = ['service_name_ar', 'service_name', 'description_ar', 'description', 'base_price'];
    $found_columns = [];
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . $column['Field'] . "</td>";
        echo "<td>" . $column['Type'] . "</td>";
        echo "<td>" . $column['Null'] . "</td>";
        echo "<td>" . $column['Key'] . "</td>";
        echo "<td>" . $column['Default'] . "</td>";
        echo "</tr>";
        
        $found_columns[] = $column['Field'];
    }
    echo "</table>";
    
    echo "<h3>فحص الأعمدة المطلوبة:</h3>";
    foreach ($expected_columns as $col) {
        if (in_array($col, $found_columns)) {
            echo "✅ العمود <strong>$col</strong> موجود<br>";
        } else {
            echo "❌ العمود <strong>$col</strong> غير موجود<br>";
        }
    }
    
    echo "<h2>2. اختبار جلب الخدمات</h2>";
    
    // اختبار الاستعلام المستخدم في admin/services.php
    $sql = "SELECT s.*, 
                   COUNT(sr.id) as request_count,
                   AVG(sr.total_amount) as avg_amount
            FROM services s 
            LEFT JOIN service_requests sr ON s.id = sr.service_id 
            GROUP BY s.id 
            ORDER BY s.created_at DESC 
            LIMIT 5";
    
    $services = $pdo->query($sql)->fetchAll();
    
    if (empty($services)) {
        echo "⚠️ لا توجد خدمات في قاعدة البيانات<br>";
        echo "<p>يمكنك إضافة بيانات تجريبية من خلال: <a href='add_test_data.php'>إضافة البيانات التجريبية</a></p>";
    } else {
        echo "✅ تم جلب " . count($services) . " خدمة بنجاح<br>";
        
        echo "<h3>عينة من الخدمات:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>الاسم العربي</th><th>الاسم الإنجليزي</th><th>الوصف العربي</th><th>السعر</th></tr>";
        
        foreach (array_slice($services, 0, 3) as $service) {
            echo "<tr>";
            echo "<td>" . $service['id'] . "</td>";
            echo "<td>" . ($service['service_name_ar'] ?? 'غير محدد') . "</td>";
            echo "<td>" . ($service['service_name'] ?? 'غير محدد') . "</td>";
            echo "<td>" . mb_substr($service['description_ar'] ?? 'غير محدد', 0, 50) . "...</td>";
            echo "<td>" . ($service['base_price'] ?? 0) . " دج</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<h3>اختبار الوصول للأعمدة:</h3>";
        $test_service = $services[0];
        
        // اختبار الأعمدة التي كانت تسبب مشاكل
        $tests = [
            'service_name_ar' => $test_service['service_name_ar'] ?? null,
            'service_name' => $test_service['service_name'] ?? null,
            'description_ar' => $test_service['description_ar'] ?? null,
            'description' => $test_service['description'] ?? null,
            'base_price' => $test_service['base_price'] ?? null
        ];
        
        foreach ($tests as $column => $value) {
            if ($value !== null) {
                echo "✅ العمود <strong>$column</strong>: " . (is_string($value) ? mb_substr($value, 0, 30) . "..." : $value) . "<br>";
            } else {
                echo "❌ العمود <strong>$column</strong>: غير موجود أو فارغ<br>";
            }
        }
    }
    
    echo "<h2>3. اختبار البحث</h2>";
    
    // اختبار استعلام البحث المحدث
    $search_sql = "SELECT * FROM services WHERE (service_name_ar LIKE :search OR service_name LIKE :search OR description_ar LIKE :search) LIMIT 3";
    $search_stmt = $pdo->prepare($search_sql);
    $search_stmt->execute(['search' => '%محاسب%']);
    $search_results = $search_stmt->fetchAll();
    
    echo "✅ اختبار البحث: تم العثور على " . count($search_results) . " نتيجة<br>";
    
    echo "<h2>4. اختبار إدراج خدمة جديدة</h2>";
    
    // اختبار إدراج خدمة تجريبية
    $test_service_data = [
        'service_name_ar' => 'خدمة اختبار',
        'service_name' => 'Test Service',
        'description_ar' => 'وصف تجريبي للخدمة',
        'description' => 'Test service description',
        'base_price' => 1000.00,
        'is_active' => 1,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $insert_sql = "INSERT INTO services (service_name_ar, service_name, description_ar, description, base_price, is_active, created_at) 
                   VALUES (:service_name_ar, :service_name, :description_ar, :description, :base_price, :is_active, :created_at)";
    
    try {
        $insert_stmt = $pdo->prepare($insert_sql);
        $insert_result = $insert_stmt->execute($test_service_data);
        
        if ($insert_result) {
            echo "✅ تم إدراج خدمة تجريبية بنجاح<br>";
            
            // حذف الخدمة التجريبية
            $test_id = $pdo->lastInsertId();
            $pdo->prepare("DELETE FROM services WHERE id = ?")->execute([$test_id]);
            echo "✅ تم حذف الخدمة التجريبية<br>";
        } else {
            echo "❌ فشل في إدراج الخدمة التجريبية<br>";
        }
    } catch (Exception $e) {
        echo "❌ خطأ في إدراج الخدمة: " . $e->getMessage() . "<br>";
    }
    
} catch (Exception $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br>";
}

echo "<hr>";
echo "<h2>الخلاصة</h2>";
echo "<p>إذا كانت جميع الاختبارات ✅، فقد تم إصلاح مشكلة <code>service_name_en</code> بنجاح.</p>";
echo "<p>يمكنك الآن الذهاب إلى <a href='dashboard/admin/services.php'>صفحة إدارة الخدمات</a> للتأكد من عدم ظهور الخطأ.</p>";

echo "<br><div style='background: #f0f0f0; padding: 15px; border-radius: 5px;'>";
echo "<h3>الإصلاحات التي تمت:</h3>";
echo "<ul>";
echo "<li>✅ تغيير <code>service_name_en</code> إلى <code>service_name</code></li>";
echo "<li>✅ تغيير <code>description_en</code> إلى <code>description</code></li>";
echo "<li>✅ إصلاح استعلامات البحث</li>";
echo "<li>✅ إصلاح JavaScript للتعديل</li>";
echo "<li>✅ إصلاح ملف البيانات التجريبية</li>";
echo "<li>✅ إضافة حماية من الأخطاء باستخدام <code>?? null</code></li>";
echo "</ul>";
echo "</div>";
?>
