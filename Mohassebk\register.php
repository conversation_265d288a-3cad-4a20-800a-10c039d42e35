<?php
/**
 * Registration Page
 * صفحة التسجيل
 */

require_once 'backend/config/config.php';

// إعادة توجيه المستخدم المسجل دخوله
if (isLoggedIn()) {
    $user = getCurrentUser();
    if ($user['role'] === 'admin' || $user['role'] === 'accountant') {
        redirect(getUrl('dashboard/admin'));
    } else {
        redirect(getUrl('dashboard/client'));
    }
}

// معالجة التسجيل
$error_message = '';
$success_message = '';
$form_data = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $form_data = [
        'full_name' => sanitize($_POST['full_name'] ?? ''),
        'email' => sanitize($_POST['email'] ?? ''),
        'phone' => sanitize($_POST['phone'] ?? ''),
        'company_name' => sanitize($_POST['company_name'] ?? ''),
        'tax_number' => sanitize($_POST['tax_number'] ?? ''),
        'username' => sanitize($_POST['username'] ?? ''),
        'password' => $_POST['password'] ?? '',
        'confirm_password' => $_POST['confirm_password'] ?? ''
    ];
    
    // التحقق من البيانات
    if (empty($form_data['full_name']) || empty($form_data['email']) || 
        empty($form_data['username']) || empty($form_data['password'])) {
        $error_message = 'يرجى ملء جميع الحقول المطلوبة';
    } elseif (!isValidEmail($form_data['email'])) {
        $error_message = 'يرجى إدخال بريد إلكتروني صحيح';
    } elseif (strlen($form_data['password']) < 8) {
        $error_message = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    } elseif ($form_data['password'] !== $form_data['confirm_password']) {
        $error_message = 'كلمات المرور غير متطابقة';
    } else {
        // التحقق من عدم وجود المستخدم مسبقاً
        $check_sql = "SELECT id FROM users WHERE email = :email OR username = :username";
        $existing_user = fetchOne($check_sql, [
            'email' => $form_data['email'],
            'username' => $form_data['username']
        ]);
        
        if ($existing_user) {
            $error_message = 'البريد الإلكتروني أو اسم المستخدم مستخدم مسبقاً';
        } else {
            // إنشاء المستخدم الجديد
            try {
                global $database;
                $user_data = [
                    'username' => $form_data['username'],
                    'email' => $form_data['email'],
                    'password' => hashPassword($form_data['password']),
                    'full_name' => $form_data['full_name'],
                    'phone' => $form_data['phone'],
                    'company_name' => $form_data['company_name'],
                    'tax_number' => $form_data['tax_number'],
                    'role' => 'client',
                    'is_active' => 1,
                    'email_verified' => 0
                ];
                
                $user_id = $database->insert('users', $user_data);
                
                if ($user_id) {
                    $success_message = 'تم إنشاء حسابك بنجاح! يمكنك الآن تسجيل الدخول';
                    $form_data = []; // مسح البيانات بعد النجاح
                } else {
                    $error_message = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى';
                }
            } catch (Exception $e) {
                $error_message = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى';
                logError("Registration error: " . $e->getMessage());
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray min-h-screen">
    
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-2xl w-full space-y-8">
            
            <!-- Header -->
            <div class="text-center">
                <a href="<?php echo getUrl(); ?>" class="inline-flex items-center space-x-3 space-x-reverse">
                    <div class="w-12 h-12 bg-earth-green rounded-lg flex items-center justify-center">
                        <i class="fas fa-calculator text-white text-2xl"></i>
                    </div>
                    <span class="text-3xl font-bold text-earth-green"><?php echo SITE_NAME; ?></span>
                </a>
                <h2 class="mt-6 text-3xl font-bold text-gray-900">
                    إنشاء حساب جديد
                </h2>
                <p class="mt-2 text-sm text-gray-600">
                    لديك حساب بالفعل؟ 
                    <a href="<?php echo getUrl('login.php'); ?>" class="font-medium text-earth-green hover:text-sand-brown transition-colors">
                        تسجيل الدخول
                    </a>
                </p>
            </div>
            
            <!-- Registration Form -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                
                <!-- Success Message -->
                <?php if ($success_message): ?>
                    <div class="mb-6 p-4 rounded-lg bg-green-100 text-green-700 border border-green-300">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle ml-2"></i>
                            <?php echo htmlspecialchars($success_message); ?>
                        </div>
                        <div class="mt-3">
                            <a href="<?php echo getUrl('login.php'); ?>" 
                               class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors inline-block">
                                تسجيل الدخول الآن
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
                
                <!-- Error Message -->
                <?php if ($error_message): ?>
                    <div class="mb-6 p-4 rounded-lg bg-red-100 text-red-700 border border-red-300">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle ml-2"></i>
                            <?php echo htmlspecialchars($error_message); ?>
                        </div>
                    </div>
                <?php endif; ?>
                
                <?php if (!$success_message): ?>
                <form method="POST" data-validate class="space-y-6">
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        
                        <!-- Full Name -->
                        <div>
                            <label for="full_name" class="block text-sm font-medium text-gray-700 mb-2">
                                الاسم الكامل <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="full_name" 
                                   name="full_name" 
                                   required
                                   value="<?php echo htmlspecialchars($form_data['full_name'] ?? ''); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                                   placeholder="أدخل اسمك الكامل">
                        </div>
                        
                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                البريد الإلكتروني <span class="text-red-500">*</span>
                            </label>
                            <input type="email" 
                                   id="email" 
                                   name="email" 
                                   required
                                   value="<?php echo htmlspecialchars($form_data['email'] ?? ''); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                                   placeholder="أدخل بريدك الإلكتروني">
                        </div>
                        
                        <!-- Phone -->
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                رقم الهاتف
                            </label>
                            <input type="tel" 
                                   id="phone" 
                                   name="phone"
                                   value="<?php echo htmlspecialchars($form_data['phone'] ?? ''); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                                   placeholder="أدخل رقم هاتفك">
                        </div>
                        
                        <!-- Username -->
                        <div>
                            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                                اسم المستخدم <span class="text-red-500">*</span>
                            </label>
                            <input type="text" 
                                   id="username" 
                                   name="username" 
                                   required
                                   value="<?php echo htmlspecialchars($form_data['username'] ?? ''); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                                   placeholder="اختر اسم مستخدم">
                        </div>
                        
                        <!-- Company Name -->
                        <div>
                            <label for="company_name" class="block text-sm font-medium text-gray-700 mb-2">
                                اسم الشركة
                            </label>
                            <input type="text" 
                                   id="company_name" 
                                   name="company_name"
                                   value="<?php echo htmlspecialchars($form_data['company_name'] ?? ''); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                                   placeholder="أدخل اسم شركتك (اختياري)">
                        </div>
                        
                        <!-- Tax Number -->
                        <div>
                            <label for="tax_number" class="block text-sm font-medium text-gray-700 mb-2">
                                الرقم الضريبي
                            </label>
                            <input type="text" 
                                   id="tax_number" 
                                   name="tax_number"
                                   value="<?php echo htmlspecialchars($form_data['tax_number'] ?? ''); ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                                   placeholder="أدخل الرقم الضريبي (اختياري)">
                        </div>
                        
                        <!-- Password -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                كلمة المرور <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="password" 
                                       id="password" 
                                       name="password" 
                                       required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                                       placeholder="أدخل كلمة المرور (8 أحرف على الأقل)">
                                <button type="button" 
                                        id="toggle-password"
                                        class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                    <i class="fas fa-eye text-gray-400 hover:text-gray-600 transition-colors"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Confirm Password -->
                        <div>
                            <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                                تأكيد كلمة المرور <span class="text-red-500">*</span>
                            </label>
                            <div class="relative">
                                <input type="password" 
                                       id="confirm_password" 
                                       name="confirm_password" 
                                       required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                                       placeholder="أعد إدخال كلمة المرور">
                                <button type="button" 
                                        id="toggle-confirm-password"
                                        class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                    <i class="fas fa-eye text-gray-400 hover:text-gray-600 transition-colors"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Terms and Conditions -->
                    <div class="flex items-start">
                        <input type="checkbox" 
                               id="terms" 
                               name="terms"
                               required
                               class="h-4 w-4 text-earth-green focus:ring-earth-green border-gray-300 rounded mt-1">
                        <label for="terms" class="mr-2 block text-sm text-gray-700">
                            أوافق على 
                            <a href="<?php echo getUrl('terms.php'); ?>" class="text-earth-green hover:text-sand-brown transition-colors" target="_blank">
                                شروط الاستخدام
                            </a>
                            و
                            <a href="<?php echo getUrl('privacy.php'); ?>" class="text-earth-green hover:text-sand-brown transition-colors" target="_blank">
                                سياسة الخصوصية
                            </a>
                        </label>
                    </div>
                    
                    <!-- Submit Button -->
                    <button type="submit" 
                            class="w-full bg-earth-green text-white py-3 px-4 rounded-lg font-semibold hover:bg-sand-brown transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-earth-green focus:ring-offset-2">
                        <i class="fas fa-user-plus ml-2"></i>
                        إنشاء الحساب
                    </button>
                    
                </form>
                <?php endif; ?>
            </div>
            
            <!-- Back to Home -->
            <div class="text-center">
                <a href="<?php echo getUrl(); ?>" 
                   class="text-sm text-gray-600 hover:text-earth-green transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // Toggle password visibility
        document.getElementById('toggle-password').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'fas fa-eye-slash text-gray-400 hover:text-gray-600 transition-colors';
            } else {
                passwordInput.type = 'password';
                icon.className = 'fas fa-eye text-gray-400 hover:text-gray-600 transition-colors';
            }
        });
        
        document.getElementById('toggle-confirm-password').addEventListener('click', function() {
            const passwordInput = document.getElementById('confirm_password');
            const icon = this.querySelector('i');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.className = 'fas fa-eye-slash text-gray-400 hover:text-gray-600 transition-colors';
            } else {
                passwordInput.type = 'password';
                icon.className = 'fas fa-eye text-gray-400 hover:text-gray-600 transition-colors';
            }
        });
    </script>
</body>
</html>
