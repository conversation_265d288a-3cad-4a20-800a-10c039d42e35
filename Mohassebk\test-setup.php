<?php
/**
 * اختبار إعداد النظام
 * Setup Test File
 */

echo "<h1>اختبار إعداد النظام</h1>";

// التحقق من PHP
echo "<h2>1. فحص PHP</h2>";
echo "إصدار PHP: " . PHP_VERSION . "<br>";
echo "هل PHP 7.4+? " . (version_compare(PHP_VERSION, '7.4.0', '>=') ? "✅ نعم" : "❌ لا") . "<br>";

// التحقق من الإضافات المطلوبة
echo "<h2>2. فحص الإضافات المطلوبة</h2>";
$required_extensions = ['pdo_mysql', 'json', 'curl', 'mbstring'];
foreach ($required_extensions as $ext) {
    echo "إضافة {$ext}: " . (extension_loaded($ext) ? "✅ متوفرة" : "❌ غير متوفرة") . "<br>";
}

// التحقق من صلاحيات الكتابة
echo "<h2>3. فحص صلاحيات الكتابة</h2>";
echo "المجلد الحالي: " . (is_writable(__DIR__) ? "✅ قابل للكتابة" : "❌ غير قابل للكتابة") . "<br>";

// التحقق من ملفات النظام
echo "<h2>4. فحص ملفات النظام</h2>";
$required_files = [
    'setup.php',
    'database/mohassebk_db.sql',
    'database/seed_data.php',
    'backend/config/db.php'
];

foreach ($required_files as $file) {
    echo "ملف {$file}: " . (file_exists($file) ? "✅ موجود" : "❌ غير موجود") . "<br>";
}

// اختبار الاتصال بقاعدة البيانات
echo "<h2>5. اختبار الاتصال بقاعدة البيانات</h2>";
try {
    $pdo = new PDO("mysql:host=localhost", "root", "");
    echo "✅ الاتصال بقاعدة البيانات ناجح<br>";
    
    // التحقق من وجود قاعدة البيانات
    $stmt = $pdo->query("SHOW DATABASES LIKE 'mohassebk_db'");
    if ($stmt->rowCount() > 0) {
        echo "✅ قاعدة البيانات mohassebk_db موجودة<br>";
        
        // التحقق من الجداول
        $pdo->exec("USE mohassebk_db");
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        echo "عدد الجداول: " . count($tables) . "<br>";
        
        if (in_array('users', $tables)) {
            echo "✅ جدول المستخدمين موجود<br>";
            
            // التحقق من المستخدم المدير
            $admin = $pdo->query("SELECT * FROM users WHERE username = 'admin'")->fetch();
            if ($admin) {
                echo "✅ حساب المدير موجود<br>";
                echo "البريد الإلكتروني: " . $admin['email'] . "<br>";
            } else {
                echo "❌ حساب المدير غير موجود<br>";
            }
        }
    } else {
        echo "❌ قاعدة البيانات mohassebk_db غير موجودة<br>";
    }
} catch (PDOException $e) {
    echo "❌ خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage() . "<br>";
}

// التحقق من ملف الإعدادات
echo "<h2>6. فحص ملف الإعدادات</h2>";
if (file_exists('config.php')) {
    echo "✅ ملف config.php موجود<br>";
    
    // قراءة محتوى الملف
    $config_content = file_get_contents('config.php');
    if (strpos($config_content, 'DB_HOST') !== false) {
        echo "✅ إعدادات قاعدة البيانات موجودة<br>";
    }
    if (strpos($config_content, 'SITE_NAME') !== false) {
        echo "✅ إعدادات الموقع موجودة<br>";
    }
} else {
    echo "❌ ملف config.php غير موجود<br>";
}

// اختبار الجلسات
echo "<h2>7. اختبار الجلسات</h2>";
session_start();
$_SESSION['test'] = 'working';
echo "الجلسات: " . (isset($_SESSION['test']) ? "✅ تعمل" : "❌ لا تعمل") . "<br>";

echo "<hr>";
echo "<h2>الخلاصة</h2>";
echo "<p>إذا كانت جميع الفحوصات ✅، فالنظام جاهز للعمل.</p>";
echo "<p>إذا كان هناك أي ❌، يرجى إصلاح المشاكل أولاً.</p>";

echo "<br><a href='setup.php' style='background: #52796F; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الذهاب إلى معالج الإعداد</a>";
?>
