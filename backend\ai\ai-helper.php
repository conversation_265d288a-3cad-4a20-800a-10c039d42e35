<?php
/**
 * AI Helper Functions
 * دوال مساعدة للذكاء الاصطناعي
 */

require_once __DIR__ . '/../config/config.php';

class AIHelper {
    
    /**
     * استدعاء OpenRouter API
     */
    public static function callOpenRouterAPI($prompt, $max_tokens = 500, $temperature = 0.7) {
        // التحقق من وجود الثوابت المطلوبة
        if (!defined('OPENROUTER_API_KEY') || !defined('OPENROUTER_API_URL') || !defined('AI_MODEL')) {
            logError("AI Helper: Missing required constants");
            return false;
        }

        $api_key = OPENROUTER_API_KEY;
        $api_url = OPENROUTER_API_URL;
        $model = AI_MODEL;

        // التحقق من صحة API Key
        if (empty($api_key) || $api_key === 'your_openrouter_api_key_here') {
            logError("AI Helper: Invalid or missing API key");
            return false;
        }
        
        $data = [
            'model' => $model,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'max_tokens' => $max_tokens,
            'temperature' => $temperature
        ];
        
        $headers = [
            'Authorization: Bearer ' . $api_key,
            'Content-Type: application/json',
            'HTTP-Referer: ' . SITE_URL,
            'X-Title: ' . SITE_NAME
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $api_url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            logError("OpenRouter API cURL error: " . $error);
            return false;
        }
        
        if ($http_code !== 200) {
            logError("OpenRouter API HTTP error: " . $http_code . " - " . $response);
            return false;
        }
        
        $result = json_decode($response, true);
        
        if (!$result || !isset($result['choices'][0]['message']['content'])) {
            logError("OpenRouter API invalid response: " . $response);
            return false;
        }
        
        return trim($result['choices'][0]['message']['content']);
    }
    
    /**
     * تحليل المستندات
     */
    public static function analyzeDocument($document_info) {
        $prompt = "أنت محاسب خبير متخصص في تحليل المستندات المحاسبية. قم بتحليل المستند التالي وقدم ملخصاً مفيداً:\n\n";
        
        $prompt .= "معلومات المستند:\n";
        $prompt .= "- اسم الملف: {$document_info['filename']}\n";
        $prompt .= "- نوع الملف: {$document_info['file_type']}\n";
        
        if (!empty($document_info['service_type'])) {
            $prompt .= "- نوع الخدمة: {$document_info['service_type']}\n";
        }
        
        if (!empty($document_info['request_title'])) {
            $prompt .= "- عنوان الطلب: {$document_info['request_title']}\n";
        }
        
        $prompt .= "\nيرجى تقديم تحليل مختصر ومفيد يتضمن:\n";
        $prompt .= "1. نوع المستند المحتمل\n";
        $prompt .= "2. الغرض من المستند\n";
        $prompt .= "3. أي ملاحظات مهمة للمحاسب\n";
        $prompt .= "4. اقتراحات للخطوات التالية\n\n";
        $prompt .= "اجعل الرد باللغة العربية ومختصراً (لا يزيد عن 200 كلمة).";
        
        return self::callOpenRouterAPI($prompt, 500, 0.7);
    }
    
    /**
     * توصيات الخدمات
     */
    public static function getServiceRecommendations($user_data) {
        $user = $user_data['user'];
        $stats = $user_data['stats'];
        
        $prompt = "أنت مستشار محاسبي خبير. قم بتحليل بيانات العميل التالي واقترح 3-5 خدمات محاسبية مناسبة له:\n\n";
        
        $prompt .= "معلومات العميل:\n";
        $prompt .= "- الاسم: {$user['full_name']}\n";
        $prompt .= "- نوع الشركة: " . ($user['company_name'] ?? 'فردي') . "\n";
        $prompt .= "- الرقم الضريبي: " . ($user['tax_number'] ? 'موجود' : 'غير موجود') . "\n";
        
        $prompt .= "\nإحصائيات الاستخدام:\n";
        $prompt .= "- إجمالي الطلبات: {$stats['total_requests']}\n";
        $prompt .= "- الطلبات المكتملة: {$stats['completed_requests']}\n";
        $prompt .= "- إجمالي المبلغ المنفق: {$stats['total_spent']} دينار\n";
        
        $prompt .= "\nالخدمات المتاحة:\n";
        $prompt .= "1. إعداد الدفاتر المحاسبية\n";
        $prompt .= "2. إعداد التصريحات الضريبية\n";
        $prompt .= "3. مراجعة الحسابات\n";
        $prompt .= "4. إعداد الميزانية العمومية\n";
        $prompt .= "5. استشارات ضريبية\n";
        $prompt .= "6. إعداد كشوف المرتبات\n";
        
        $prompt .= "\nيرجى تقديم توصيات بالتنسيق التالي:\n";
        $prompt .= "RECOMMENDATION: [اسم الخدمة]\n";
        $prompt .= "REASON: [سبب التوصية]\n";
        $prompt .= "PRIORITY: [عالية/متوسطة/منخفضة]\n";
        $prompt .= "---\n";
        
        $prompt .= "\nاجعل التوصيات باللغة العربية ومناسبة لحالة العميل.";
        
        return self::callOpenRouterAPI($prompt, 800, 0.7);
    }
    
    /**
     * تحليل استجابة توصيات الخدمات
     */
    public static function parseServiceRecommendations($ai_response) {
        $recommendations = [];
        $sections = explode('---', $ai_response);
        
        foreach ($sections as $section) {
            $lines = explode("\n", trim($section));
            $recommendation = [];
            
            foreach ($lines as $line) {
                if (strpos($line, 'RECOMMENDATION:') === 0) {
                    $recommendation['service'] = trim(str_replace('RECOMMENDATION:', '', $line));
                } elseif (strpos($line, 'REASON:') === 0) {
                    $recommendation['reason'] = trim(str_replace('REASON:', '', $line));
                } elseif (strpos($line, 'PRIORITY:') === 0) {
                    $recommendation['priority'] = trim(str_replace('PRIORITY:', '', $line));
                }
            }
            
            if (!empty($recommendation['service']) && !empty($recommendation['reason'])) {
                $recommendations[] = $recommendation;
            }
        }
        
        return $recommendations;
    }
    
    /**
     * توصيات افتراضية
     */
    public static function getDefaultRecommendations($user_data) {
        $user = $user_data['user'];
        $stats = $user_data['stats'];
        
        $recommendations = [];
        
        if ($stats['total_requests'] == 0) {
            // مستخدم جديد
            $recommendations[] = [
                'service' => 'إعداد الدفاتر المحاسبية',
                'reason' => 'خدمة أساسية لتنظيم الحسابات المالية',
                'priority' => 'عالية'
            ];
            
            $recommendations[] = [
                'service' => 'إعداد التصريحات الضريبية',
                'reason' => 'ضرورية للامتثال للقوانين الضريبية',
                'priority' => 'عالية'
            ];
        } else {
            // مستخدم لديه طلبات سابقة
            $recommendations[] = [
                'service' => 'مراجعة الحسابات',
                'reason' => 'مراجعة دورية للتأكد من دقة الحسابات',
                'priority' => 'عالية'
            ];
            
            $recommendations[] = [
                'service' => 'استشارات ضريبية',
                'reason' => 'تحسين الكفاءة الضريبية وتوفير المال',
                'priority' => 'متوسطة'
            ];
        }
        
        if (!empty($user['tax_number'])) {
            $recommendations[] = [
                'service' => 'إعداد الميزانية العمومية',
                'reason' => 'ضرورية للشركات المسجلة ضريبياً',
                'priority' => 'عالية'
            ];
        }
        
        return $recommendations;
    }
    
    /**
     * الحصول على بيانات المستخدم للتحليل
     */
    public static function getUserAnalysisData($user_id) {
        try {
            // الحصول على معلومات المستخدم
            $user = fetchOne("SELECT * FROM users WHERE id = :id", ['id' => $user_id]);
            
            // الحصول على إحصائيات
            $stats = [
                'total_requests' => 0,
                'completed_requests' => 0,
                'total_spent' => 0
            ];
            
            $stats['total_requests'] = fetchOne(
                "SELECT COUNT(*) as count FROM service_requests WHERE user_id = :user_id",
                ['user_id' => $user_id]
            )['count'];
            
            $stats['completed_requests'] = fetchOne(
                "SELECT COUNT(*) as count FROM service_requests WHERE user_id = :user_id AND status = 'completed'",
                ['user_id' => $user_id]
            )['count'];
            
            $total_spent = fetchOne(
                "SELECT SUM(total_amount) as total FROM service_requests WHERE user_id = :user_id AND status = 'completed'",
                ['user_id' => $user_id]
            );
            $stats['total_spent'] = $total_spent['total'] ?? 0;
            
            return [
                'user' => $user,
                'stats' => $stats
            ];
            
        } catch (Exception $e) {
            logError("User analysis data error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * حفظ محادثة الذكاء الاصطناعي
     */
    public static function saveConversation($user_id, $session_id, $message, $response, $model_used = null, $tokens_used = 0, $processing_time = 0) {
        try {
            $sql = "INSERT INTO ai_conversations (user_id, session_id, message, response, model_used, tokens_used, processing_time) 
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            global $database;
            return $database->query($sql, [
                $user_id,
                $session_id,
                $message,
                $response,
                $model_used ?? AI_MODEL,
                $tokens_used,
                $processing_time
            ]);
            
        } catch (Exception $e) {
            logError("Save AI conversation error: " . $e->getMessage());
            return false;
        }
    }
}
?>
