<?php
/**
 * Client Requests List
 * قائمة طلبات العميل
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'client') {
    redirect(getUrl('login.php'));
}

// معاملات البحث والتصفية
$search = sanitize($_GET['search'] ?? '');
$status_filter = sanitize($_GET['status'] ?? '');
$page = max(1, (int)($_GET['page'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// بناء استعلام البحث
$where_conditions = ['sr.user_id = :user_id'];
$params = ['user_id' => $user['id']];

if (!empty($search)) {
    $where_conditions[] = '(sr.request_title LIKE :search OR sr.description LIKE :search OR s.service_name_ar LIKE :search)';
    $params['search'] = '%' . $search . '%';
}

if (!empty($status_filter)) {
    $where_conditions[] = 'sr.status = :status';
    $params['status'] = $status_filter;
}

$where_clause = implode(' AND ', $where_conditions);

// الحصول على إجمالي عدد الطلبات
$total_requests = 0;
try {
    $count_sql = "SELECT COUNT(*) as count FROM service_requests sr 
                  LEFT JOIN services s ON sr.service_id = s.id 
                  WHERE $where_clause";
    $total_requests = fetchOne($count_sql, $params)['count'];
} catch (Exception $e) {
    logError("Requests count error: " . $e->getMessage());
}

$total_pages = ceil($total_requests / $per_page);

// الحصول على الطلبات
$requests = [];
try {
    $sql = "SELECT sr.*, s.service_name_ar, u.full_name as accountant_name
            FROM service_requests sr 
            LEFT JOIN services s ON sr.service_id = s.id 
            LEFT JOIN users u ON sr.assigned_accountant_id = u.id
            WHERE $where_clause 
            ORDER BY sr.created_at DESC 
            LIMIT :limit OFFSET :offset";
    
    $stmt = executeQuery($sql, array_merge($params, ['limit' => $per_page, 'offset' => $offset]));
    $requests = $stmt->fetchAll();
} catch (Exception $e) {
    logError("Requests fetch error: " . $e->getMessage());
}

// الحصول على رسالة الفلاش
$flash_message = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>طلباتي - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($flash_message): ?>
            <div class="mb-6 p-4 rounded-lg <?php echo $flash_message['type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                <div class="flex items-center">
                    <i class="fas <?php echo $flash_message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($flash_message['message']); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-4">
                <a href="<?php echo getUrl(); ?>" class="hover:text-earth-green transition-colors">الرئيسية</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <a href="<?php echo getUrl('dashboard/client'); ?>" class="hover:text-earth-green transition-colors">لوحة التحكم</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <span class="text-earth-green">طلباتي</span>
            </nav>
            
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-earth-green mb-2">
                        طلباتي المحاسبية
                    </h1>
                    <p class="text-gray-600">
                        متابعة وإدارة جميع طلباتك المحاسبية
                    </p>
                </div>
                <a href="<?php echo getUrl('services/request.php'); ?>" 
                   class="bg-earth-green text-white px-6 py-3 rounded-lg font-semibold hover:bg-sand-brown transition-all duration-300">
                    <i class="fas fa-plus ml-2"></i>
                    طلب خدمة جديدة
                </a>
            </div>
        </div>
        
        <!-- Search and Filter -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <form method="GET" class="grid md:grid-cols-4 gap-4">
                
                <!-- Search -->
                <div class="md:col-span-2">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-2">
                        البحث
                    </label>
                    <div class="relative">
                        <input type="text" 
                               id="search" 
                               name="search" 
                               value="<?php echo htmlspecialchars($search); ?>"
                               class="w-full px-4 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors"
                               placeholder="ابحث في العنوان أو الوصف أو نوع الخدمة...">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                    </div>
                </div>
                
                <!-- Status Filter -->
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                        الحالة
                    </label>
                    <select id="status" 
                            name="status"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green transition-colors">
                        <option value="">جميع الحالات</option>
                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>معلق</option>
                        <option value="in_progress" <?php echo $status_filter === 'in_progress' ? 'selected' : ''; ?>>قيد التنفيذ</option>
                        <option value="under_review" <?php echo $status_filter === 'under_review' ? 'selected' : ''; ?>>قيد المراجعة</option>
                        <option value="completed" <?php echo $status_filter === 'completed' ? 'selected' : ''; ?>>مكتمل</option>
                        <option value="cancelled" <?php echo $status_filter === 'cancelled' ? 'selected' : ''; ?>>ملغي</option>
                    </select>
                </div>
                
                <!-- Submit -->
                <div class="flex items-end">
                    <button type="submit" 
                            class="w-full bg-earth-green text-white px-4 py-2 rounded-lg font-semibold hover:bg-sand-brown transition-all duration-300">
                        <i class="fas fa-filter ml-2"></i>
                        تصفية
                    </button>
                </div>
            </form>
        </div>

        <!-- Requests List -->
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold text-earth-green">
                        قائمة الطلبات (<?php echo $total_requests; ?>)
                    </h2>
                    <?php if (!empty($search) || !empty($status_filter)): ?>
                        <a href="<?php echo getUrl('dashboard/client/requests.php'); ?>"
                           class="text-sm text-gray-600 hover:text-earth-green transition-colors">
                            <i class="fas fa-times ml-1"></i>
                            مسح التصفية
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <div class="overflow-x-auto">
                <?php if (empty($requests)): ?>
                    <div class="text-center py-12">
                        <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600 mb-4">
                            <?php if (!empty($search) || !empty($status_filter)): ?>
                                لا توجد طلبات تطابق معايير البحث
                            <?php else: ?>
                                لا توجد طلبات حتى الآن
                            <?php endif; ?>
                        </p>
                        <a href="<?php echo getUrl('services/request.php'); ?>"
                           class="bg-earth-green text-white px-6 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                            اطلب خدمة الآن
                        </a>
                    </div>
                <?php else: ?>
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الطلب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الخدمة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المحاسب</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($requests as $request): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($request['request_title']); ?>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            #<?php echo $request['id']; ?>
                                        </div>
                                        <?php
                                        $priority_classes = [
                                            'low' => 'bg-gray-100 text-gray-800',
                                            'medium' => 'bg-blue-100 text-blue-800',
                                            'high' => 'bg-orange-100 text-orange-800',
                                            'urgent' => 'bg-red-100 text-red-800'
                                        ];

                                        $priority_text = [
                                            'low' => 'منخفضة',
                                            'medium' => 'متوسطة',
                                            'high' => 'عالية',
                                            'urgent' => 'عاجلة'
                                        ];
                                        ?>
                                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-1 <?php echo $priority_classes[$request['priority']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                            <?php echo $priority_text[$request['priority']] ?? $request['priority']; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($request['service_name_ar'] ?? 'خدمة محاسبية'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $status_classes = [
                                            'pending' => 'bg-yellow-100 text-yellow-800',
                                            'in_progress' => 'bg-blue-100 text-blue-800',
                                            'under_review' => 'bg-purple-100 text-purple-800',
                                            'completed' => 'bg-green-100 text-green-800',
                                            'cancelled' => 'bg-red-100 text-red-800'
                                        ];

                                        $status_text = [
                                            'pending' => 'معلق',
                                            'in_progress' => 'قيد التنفيذ',
                                            'under_review' => 'قيد المراجعة',
                                            'completed' => 'مكتمل',
                                            'cancelled' => 'ملغي'
                                        ];
                                        ?>
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $status_classes[$request['status']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                            <?php echo $status_text[$request['status']] ?? $request['status']; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php if ($request['accountant_name']): ?>
                                            <div class="flex items-center">
                                                <div class="w-6 h-6 bg-earth-green rounded-full flex items-center justify-center ml-2">
                                                    <i class="fas fa-user text-white text-xs"></i>
                                                </div>
                                                <?php echo htmlspecialchars($request['accountant_name']); ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-gray-500">لم يتم التعيين</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo formatCurrency($request['total_amount']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <div><?php echo date('Y-m-d', strtotime($request['created_at'])); ?></div>
                                        <div class="text-xs"><?php echo date('H:i', strtotime($request['created_at'])); ?></div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <a href="<?php echo getUrl('dashboard/client/request-details.php?id=' . $request['id']); ?>"
                                               class="text-earth-green hover:text-sand-brown transition-colors"
                                               title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if ($request['status'] === 'pending'): ?>
                                                <a href="<?php echo getUrl('dashboard/client/edit-request.php?id=' . $request['id']); ?>"
                                                   class="text-blue-600 hover:text-blue-800 transition-colors"
                                                   title="تعديل">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            <?php endif; ?>
                                            <a href="<?php echo getUrl('dashboard/client/documents.php?request_id=' . $request['id']); ?>"
                                               class="text-purple-600 hover:text-purple-800 transition-colors"
                                               title="المستندات">
                                                <i class="fas fa-folder"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>
</body>
</html>
