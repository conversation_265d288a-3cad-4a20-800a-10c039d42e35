<?php
/**
 * Admin Dashboard
 * لوحة تحكم الإدارة
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || !in_array($user['role'], ['admin', 'accountant'])) {
    redirect(getUrl('login.php'));
}

// إحصائيات عامة
$stats = [
    'total_users' => 0,
    'total_requests' => 0,
    'pending_requests' => 0,
    'completed_requests' => 0,
    'total_revenue' => 0,
    'new_users_today' => 0
];

try {
    // إجمالي المستخدمين
    $stats['total_users'] = fetchOne(
        "SELECT COUNT(*) as count FROM users WHERE role = 'client'"
    )['count'];
    
    // إجمالي الطلبات
    $stats['total_requests'] = fetchOne(
        "SELECT COUNT(*) as count FROM service_requests"
    )['count'];
    
    // الطلبات المعلقة
    $stats['pending_requests'] = fetchOne(
        "SELECT COUNT(*) as count FROM service_requests WHERE status IN ('pending', 'in_progress')"
    )['count'];
    
    // الطلبات المكتملة
    $stats['completed_requests'] = fetchOne(
        "SELECT COUNT(*) as count FROM service_requests WHERE status = 'completed'"
    )['count'];
    
    // إجمالي الإيرادات
    $revenue_result = fetchOne(
        "SELECT SUM(total_amount) as total FROM service_requests WHERE status = 'completed'"
    );
    $stats['total_revenue'] = $revenue_result['total'] ?? 0;
    
    // المستخدمين الجدد اليوم
    $stats['new_users_today'] = fetchOne(
        "SELECT COUNT(*) as count FROM users WHERE DATE(created_at) = CURDATE()"
    )['count'];
    
} catch (Exception $e) {
    logError("Admin dashboard stats error: " . $e->getMessage());
}

// أحدث الطلبات
$recent_requests = [];
try {
    $recent_requests = fetchAll(
        "SELECT sr.*, s.service_name_ar, u.full_name as client_name 
         FROM service_requests sr 
         LEFT JOIN services s ON sr.service_id = s.id 
         LEFT JOIN users u ON sr.user_id = u.id 
         ORDER BY sr.created_at DESC 
         LIMIT 10"
    );
} catch (Exception $e) {
    logError("Recent requests error: " . $e->getMessage());
}

// الحصول على رسالة الفلاش
$flash_message = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة تحكم الإدارة - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($flash_message): ?>
            <div class="mb-6 p-4 rounded-lg <?php echo $flash_message['type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                <div class="flex items-center">
                    <i class="fas <?php echo $flash_message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($flash_message['message']); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-earth-green mb-2">
                لوحة تحكم <?php echo $user['role'] === 'admin' ? 'الإدارة' : 'المحاسب'; ?>
            </h1>
            <p class="text-gray-600">
                مرحباً، <?php echo htmlspecialchars($user['full_name']); ?> - إدارة ومتابعة جميع العمليات
            </p>
        </div>
        
        <!-- Statistics Cards -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6 mb-8">
            
            <!-- Total Users -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي العملاء</p>
                        <p class="text-2xl font-bold text-earth-green"><?php echo $stats['total_users']; ?></p>
                    </div>
                    <div class="w-10 h-10 bg-earth-green rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-white"></i>
                    </div>
                </div>
            </div>
            
            <!-- Total Requests -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
                        <p class="text-2xl font-bold text-blue-600"><?php echo $stats['total_requests']; ?></p>
                    </div>
                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-file-alt text-white"></i>
                    </div>
                </div>
            </div>
            
            <!-- Pending Requests -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">طلبات معلقة</p>
                        <p class="text-2xl font-bold text-yellow-600"><?php echo $stats['pending_requests']; ?></p>
                    </div>
                    <div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-white"></i>
                    </div>
                </div>
            </div>
            
            <!-- Completed Requests -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">طلبات مكتملة</p>
                        <p class="text-2xl font-bold text-green-600"><?php echo $stats['completed_requests']; ?></p>
                    </div>
                    <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-white"></i>
                    </div>
                </div>
            </div>
            
            <!-- Total Revenue -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                        <p class="text-2xl font-bold text-sand-brown"><?php echo number_format($stats['total_revenue'], 0); ?></p>
                        <p class="text-xs text-gray-500">ر.س</p>
                    </div>
                    <div class="w-10 h-10 bg-sand-brown rounded-lg flex items-center justify-center">
                        <i class="fas fa-coins text-white"></i>
                    </div>
                </div>
            </div>
            
            <!-- New Users Today -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">عملاء جدد اليوم</p>
                        <p class="text-2xl font-bold text-purple-600"><?php echo $stats['new_users_today']; ?></p>
                    </div>
                    <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-user-plus text-white"></i>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <a href="<?php echo getUrl('dashboard/admin/settings.php'); ?>"
               class="bg-earth-green text-white p-4 rounded-lg hover:bg-sand-brown transition-all duration-300 text-center">
                <i class="fas fa-cogs text-2xl mb-2"></i>
                <p class="font-semibold">إعدادات النظام</p>
            </a>
            
            <a href="<?php echo getUrl('dashboard/admin/users.php'); ?>" 
               class="bg-warm-beige text-earth-green p-4 rounded-lg hover:bg-opacity-80 transition-all duration-300 text-center">
                <i class="fas fa-users text-2xl mb-2"></i>
                <p class="font-semibold">إدارة المستخدمين</p>
            </a>
            
            <a href="<?php echo getUrl('dashboard/admin/services.php'); ?>" 
               class="bg-muted-green text-earth-green p-4 rounded-lg hover:bg-opacity-80 transition-all duration-300 text-center">
                <i class="fas fa-cogs text-2xl mb-2"></i>
                <p class="font-semibold">إدارة الخدمات</p>
            </a>
            
            <a href="<?php echo getUrl('dashboard/admin/reports.php'); ?>" 
               class="bg-sand-brown text-white p-4 rounded-lg hover:bg-opacity-80 transition-all duration-300 text-center">
                <i class="fas fa-chart-bar text-2xl mb-2"></i>
                <p class="font-semibold">التقارير</p>
            </a>
        </div>
        
        <!-- Recent Requests -->
        <div class="bg-white rounded-lg shadow-lg">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-semibold text-earth-green">أحدث الطلبات</h2>
                    <a href="<?php echo getUrl('dashboard/admin/requests.php'); ?>" 
                       class="text-earth-green hover:text-sand-brown transition-colors">
                        عرض الكل <i class="fas fa-arrow-left mr-2"></i>
                    </a>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <?php if (empty($recent_requests)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
                        <p class="text-gray-600">لا توجد طلبات حتى الآن</p>
                    </div>
                <?php else: ?>
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">العميل</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الخدمة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">التاريخ</th>
                                <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">إجراءات</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($recent_requests as $request): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($request['client_name'] ?? 'غير محدد'); ?>
                                        </div>
                                        <div class="text-sm text-gray-500">
                                            <?php echo htmlspecialchars($request['request_title']); ?>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($request['service_name_ar'] ?? 'خدمة محاسبية'); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $status_classes = [
                                            'pending' => 'bg-yellow-100 text-yellow-800',
                                            'in_progress' => 'bg-blue-100 text-blue-800',
                                            'under_review' => 'bg-purple-100 text-purple-800',
                                            'completed' => 'bg-green-100 text-green-800',
                                            'cancelled' => 'bg-red-100 text-red-800'
                                        ];
                                        
                                        $status_text = [
                                            'pending' => 'معلق',
                                            'in_progress' => 'قيد التنفيذ',
                                            'under_review' => 'قيد المراجعة',
                                            'completed' => 'مكتمل',
                                            'cancelled' => 'ملغي'
                                        ];
                                        ?>
                                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo $status_classes[$request['status']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                            <?php echo $status_text[$request['status']] ?? $request['status']; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo number_format($request['total_amount'], 2); ?> ر.س
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo date('Y-m-d', strtotime($request['created_at'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <a href="<?php echo getUrl('dashboard/admin/request-details.php?id=' . $request['id']); ?>" 
                                           class="text-earth-green hover:text-sand-brown transition-colors ml-3">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?php echo getUrl('dashboard/admin/edit-request.php?id=' . $request['id']); ?>" 
                                           class="text-blue-600 hover:text-blue-900 transition-colors">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>
    
    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>
</body>
</html>
