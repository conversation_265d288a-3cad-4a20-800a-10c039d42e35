<?php
/**
 * Database Setup Script
 * سكريبت إعداد قاعدة البيانات
 */

require_once '../config/config.php';

try {
    // Read the SQL setup file
    $sqlFile = __DIR__ . '/setup.sql';
    
    if (!file_exists($sqlFile)) {
        throw new Exception('ملف إعداد قاعدة البيانات غير موجود');
    }
    
    $sql = file_get_contents($sqlFile);
    
    if ($sql === false) {
        throw new Exception('فشل في قراءة ملف إعداد قاعدة البيانات');
    }
    
    // Split SQL into individual statements
    $statements = array_filter(
        array_map('trim', explode(';', $sql)),
        function($stmt) {
            return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
        }
    );
    
    echo "<h2>إعداد قاعدة البيانات</h2>\n";
    echo "<div style='font-family: Arial, sans-serif; direction: rtl; text-align: right;'>\n";
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        try {
            // Skip comments and empty statements
            if (empty(trim($statement)) || preg_match('/^\s*--/', $statement)) {
                continue;
            }
            
            $pdo->exec($statement);
            
            // Extract table name or operation for display
            if (preg_match('/CREATE TABLE IF NOT EXISTS\s+(\w+)/i', $statement, $matches)) {
                echo "<p style='color: green;'>✓ تم إنشاء جدول: {$matches[1]}</p>\n";
            } elseif (preg_match('/INSERT IGNORE INTO\s+(\w+)/i', $statement, $matches)) {
                echo "<p style='color: blue;'>✓ تم إدراج البيانات في جدول: {$matches[1]}</p>\n";
            } elseif (preg_match('/CREATE DATABASE/i', $statement)) {
                echo "<p style='color: green;'>✓ تم إنشاء قاعدة البيانات</p>\n";
            } else {
                echo "<p style='color: green;'>✓ تم تنفيذ الاستعلام بنجاح</p>\n";
            }
            
            $successCount++;
            
        } catch (PDOException $e) {
            echo "<p style='color: red;'>✗ خطأ في تنفيذ الاستعلام: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            $errorCount++;
        }
    }
    
    echo "<hr>\n";
    echo "<h3>ملخص العملية:</h3>\n";
    echo "<p><strong>العمليات الناجحة:</strong> $successCount</p>\n";
    echo "<p><strong>الأخطاء:</strong> $errorCount</p>\n";
    
    if ($errorCount === 0) {
        echo "<p style='color: green; font-weight: bold;'>✓ تم إعداد قاعدة البيانات بنجاح!</p>\n";
        
        // Test database connection and tables
        echo "<h3>اختبار الاتصال والجداول:</h3>\n";
        
        $tables = ['users', 'services', 'service_requests', 'payments', 'documents', 'system_settings', 'contact_messages', 'ai_conversations'];
        
        foreach ($tables as $table) {
            try {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                $result = $stmt->fetch();
                echo "<p style='color: green;'>✓ جدول $table: {$result['count']} سجل</p>\n";
            } catch (PDOException $e) {
                echo "<p style='color: red;'>✗ خطأ في جدول $table: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            }
        }
        
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠ تم إعداد قاعدة البيانات مع بعض الأخطاء</p>\n";
    }
    
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div style='color: red; font-family: Arial, sans-serif; direction: rtl; text-align: right;'>\n";
    echo "<h2>خطأ في إعداد قاعدة البيانات</h2>\n";
    echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}

// Add navigation links
echo "<hr>\n";
echo "<div style='text-align: center; margin: 20px;'>\n";
echo "<a href='../../index.php' style='background: #52796F; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>العودة للرئيسية</a>\n";
echo "<a href='../../login.php' style='background: #8D7B68; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>تسجيل الدخول</a>\n";
echo "</div>\n";
?>
