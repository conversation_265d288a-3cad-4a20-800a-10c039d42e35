<?php
/**
 * ملف إعدادات محاسبك الرقمي
 * Digital Accountant Configuration File
 * Generated on: 2025-07-23 13:57:47
 */

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'mohassebk');
define('DB_USER', 'root');
define('DB_PASS', '');

// Site Configuration
define('SITE_NAME', 'المحاسبك الرقمي');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '+************');
define('SITE_URL', 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']));

// Security
define('SECRET_KEY', '06844d2903062561fbb0692387ab36c0741e3ed1872c3226d149d1de6536f487');

// Timezone
date_default_timezone_set('Africa/Algiers');

// Error Reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Connection Function
function getDBConnection() {
    try {
        $pdo = new PDO('mysql:host=' . DB_HOST . ';dbname=' . DB_NAME . ';charset=utf8mb4', DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return $pdo;
    } catch (PDOException $e) {
        die('Database connection failed: ' . $e->getMessage());
    }
}
?>