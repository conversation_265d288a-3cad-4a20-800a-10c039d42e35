# قاعدة البيانات والبيانات التجريبية
# Database and Test Data

## 📁 ملفات قاعدة البيانات / Database Files

### `mohassebk_db.sql`
ملف إنشاء قاعدة البيانات الأساسية مع الجداول والبيانات الأولية.
- إنشاء جميع الجداول المطلوبة
- إدراج البيانات الأساسية (الخدمات، الإعدادات)
- إنشاء حساب المدير الافتراضي

### `seed_data.php`
ملف إضافة البيانات التجريبية العشوائية للاختبار والتطوير.

## 🚀 طرق إضافة البيانات التجريبية

### 1. من خلال معالج الإعداد (setup.php)
```
http://localhost/Mohassebk/setup.php
```
- اتبع خطوات الإعداد
- في الخطوة 5، اختر "نعم، أضف البيانات التجريبية"

### 2. من خلال الملف المنفصل (add_test_data.php)
```
http://localhost/Mohassebk/add_test_data.php
```
- واجهة مستقلة لإضافة البيانات التجريبية
- يمكن استخدامها بعد إتمام الإعداد

### 3. من خلال سطر الأوامر
```bash
cd /path/to/Mohassebk
php database/seed_data.php
```

## 📊 البيانات التجريبية المضافة

### 👥 المستخدمين (18 مستخدم)
- **1 مدير**: <EMAIL> / admin123
- **2 محاسب**: 
  - <EMAIL> / acc123
  - <EMAIL> / acc123
- **15 عميل**: <EMAIL> إلى <EMAIL> / client123

### 🛠️ الخدمات (6 خدمات)
- إعداد الدفاتر المحاسبية (15,000 دج)
- إعداد التصريحات الضريبية (8,000 دج)
- مراجعة الحسابات (25,000 دج)
- إعداد الميزانية العمومية (12,000 دج)
- استشارات ضريبية (5,000 دج)
- إعداد كشوف المرتبات (3,000 دج)

### 📋 طلبات الخدمات (30 طلب)
- طلبات عشوائية بحالات مختلفة
- مُوزعة على العملاء والمحاسبين
- أولويات متنوعة (منخفضة، متوسطة، عالية، عاجلة)

### 🔔 الإشعارات (50 إشعار)
- إشعارات ترحيبية
- تحديثات حالة الطلبات
- تذكيرات الدفع
- إشعارات إكمال الخدمات

### 💳 المدفوعات
- مدفوعات عشوائية لـ 50% من الطلبات
- طرق دفع متنوعة (بطاقة ائتمان، تحويل بنكي، PayPal، نقداً)
- حالات مختلفة (معلقة، مكتملة، فاشلة)

### 🤖 محادثات الذكاء الاصطناعي (100 محادثة)
- أسئلة محاسبية شائعة
- استفسارات ضريبية
- أسئلة حول المستندات المطلوبة
- استعلامات عن المواعيد

### ⚙️ إعدادات النظام الإضافية
- وضع الصيانة
- الحد الأقصى لحجم الملف
- أنواع الملفات المسموحة
- تفعيل الذكاء الاصطناعي
- إشعارات البريد الإلكتروني

## 🔧 استخدام البيانات التجريبية

### للمطورين
- اختبار الواجهات والوظائف
- تطوير ميزات جديدة
- اختبار الأداء مع بيانات كثيرة

### للعرض التوضيحي
- عرض النظام للعملاء
- تدريب المستخدمين
- اختبار سيناريوهات مختلفة

## ⚠️ تحذيرات مهمة

1. **لا تستخدم في الإنتاج**: البيانات التجريبية للاختبار فقط
2. **كلمات مرور ضعيفة**: غيّر كلمات المرور في البيئة الحقيقية
3. **بيانات وهمية**: جميع البيانات الشخصية والمالية وهمية
4. **حذف البيانات**: يمكن حذف البيانات التجريبية بأمان

## 🗑️ حذف البيانات التجريبية

لحذف جميع البيانات التجريبية:

```sql
-- حذف البيانات مع الحفاظ على هيكل الجداول
TRUNCATE TABLE ai_conversations;
TRUNCATE TABLE notifications;
TRUNCATE TABLE payments;
TRUNCATE TABLE documents;
TRUNCATE TABLE service_requests;
DELETE FROM users WHERE username != 'admin';
DELETE FROM system_settings WHERE setting_key NOT IN ('site_name', 'site_email', 'site_phone', 'site_address', 'site_description', 'currency', 'timezone');
```

## 📞 الدعم

إذا واجهت مشاكل في إضافة البيانات التجريبية:
1. تأكد من إتمام إعداد قاعدة البيانات
2. تحقق من صلاحيات قاعدة البيانات
3. راجع ملفات السجل للأخطاء
4. تأكد من وجود ملف config.php

---

**ملاحظة**: هذه البيانات مصممة خصيصاً لنظام محاسبك الرقمي وتتوافق مع هيكل قاعدة البيانات المحددة.
