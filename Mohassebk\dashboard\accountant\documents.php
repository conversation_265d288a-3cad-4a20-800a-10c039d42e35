<?php
/**
 * Accountant Documents Management
 * إدارة مستندات المحاسب
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'accountant') {
    redirect(getUrl('login.php'));
}

// معالجة تحديث حالة المستند
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_document'])) {
    $document_id = (int)$_POST['document_id'];
    $is_processed = (int)$_POST['is_processed'];
    $ai_analysis = sanitize($_POST['ai_analysis'] ?? '');
    
    // التحقق من أن المستند ينتمي لطلب معين للمحاسب الحالي
    $document = fetchOne(
        "SELECT d.*, sr.assigned_accountant_id 
         FROM documents d 
         INNER JOIN service_requests sr ON d.request_id = sr.id 
         WHERE d.id = :id AND sr.assigned_accountant_id = :accountant_id",
        ['id' => $document_id, 'accountant_id' => $user['id']]
    );
    
    if ($document) {
        try {
            $sql = "UPDATE documents SET is_processed = :is_processed, ai_analysis = :ai_analysis WHERE id = :id";
            executeQuery($sql, [
                'is_processed' => $is_processed,
                'ai_analysis' => $ai_analysis,
                'id' => $document_id
            ]);
            
            showMessage('تم تحديث حالة المستند بنجاح', 'success');
            redirect(getUrl('dashboard/accountant/documents.php'));
        } catch (Exception $e) {
            showMessage('حدث خطأ أثناء تحديث المستند', 'error');
            logError("Update document error: " . $e->getMessage());
        }
    } else {
        showMessage('المستند غير موجود أو غير مصرح لك بالوصول إليه', 'error');
    }
}

// فلترة المستندات
$request_id = isset($_GET['request_id']) ? (int)$_GET['request_id'] : 0;
$processed_filter = $_GET['processed'] ?? 'all';
$file_type_filter = $_GET['file_type'] ?? 'all';
$search = sanitize($_GET['search'] ?? '');

// بناء استعلام المستندات
$where_conditions = ["sr.assigned_accountant_id = :accountant_id"];
$params = ['accountant_id' => $user['id']];

if ($request_id > 0) {
    $where_conditions[] = "d.request_id = :request_id";
    $params['request_id'] = $request_id;
}

if ($processed_filter !== 'all') {
    $where_conditions[] = "d.is_processed = :is_processed";
    $params['is_processed'] = $processed_filter === 'processed' ? 1 : 0;
}

if ($file_type_filter !== 'all') {
    $where_conditions[] = "d.file_type = :file_type";
    $params['file_type'] = $file_type_filter;
}

if (!empty($search)) {
    $where_conditions[] = "(d.original_filename LIKE :search OR sr.request_title LIKE :search OR u.full_name LIKE :search)";
    $params['search'] = "%$search%";
}

$where_clause = implode(' AND ', $where_conditions);

// الحصول على المستندات
$documents = [];
try {
    $sql = "SELECT d.*, sr.request_title, sr.status as request_status, u.full_name as client_name
            FROM documents d 
            INNER JOIN service_requests sr ON d.request_id = sr.id 
            INNER JOIN users u ON sr.user_id = u.id 
            WHERE $where_clause 
            ORDER BY d.upload_date DESC";
    
    $documents = fetchAll($sql, $params);
} catch (Exception $e) {
    logError("Fetch documents error: " . $e->getMessage());
}

// إحصائيات المستندات
$stats = [
    'total' => 0,
    'processed' => 0,
    'unprocessed' => 0,
    'pdf' => 0,
    'image' => 0,
    'office' => 0
];

try {
    // إجمالي المستندات
    $stats['total'] = fetchOne(
        "SELECT COUNT(*) as count FROM documents d 
         INNER JOIN service_requests sr ON d.request_id = sr.id 
         WHERE sr.assigned_accountant_id = :accountant_id",
        ['accountant_id' => $user['id']]
    )['count'];
    
    // المستندات المعالجة
    $stats['processed'] = fetchOne(
        "SELECT COUNT(*) as count FROM documents d 
         INNER JOIN service_requests sr ON d.request_id = sr.id 
         WHERE sr.assigned_accountant_id = :accountant_id AND d.is_processed = 1",
        ['accountant_id' => $user['id']]
    )['count'];
    
    // المستندات غير المعالجة
    $stats['unprocessed'] = $stats['total'] - $stats['processed'];
    
    // إحصائيات أنواع الملفات
    $file_types = fetchAll(
        "SELECT d.file_type, COUNT(*) as count FROM documents d 
         INNER JOIN service_requests sr ON d.request_id = sr.id 
         WHERE sr.assigned_accountant_id = :accountant_id 
         GROUP BY d.file_type",
        ['accountant_id' => $user['id']]
    );
    
    foreach ($file_types as $type) {
        if (in_array($type['file_type'], ['pdf'])) {
            $stats['pdf'] += $type['count'];
        } elseif (in_array($type['file_type'], ['jpg', 'jpeg', 'png', 'gif'])) {
            $stats['image'] += $type['count'];
        } elseif (in_array($type['file_type'], ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'])) {
            $stats['office'] += $type['count'];
        }
    }
    
} catch (Exception $e) {
    logError("Documents stats error: " . $e->getMessage());
}

// الحصول على قائمة الطلبات للفلترة
$requests_list = [];
try {
    $requests_list = fetchAll(
        "SELECT id, request_title FROM service_requests 
         WHERE assigned_accountant_id = :accountant_id 
         ORDER BY created_at DESC",
        ['accountant_id' => $user['id']]
    );
} catch (Exception $e) {
    logError("Fetch requests list error: " . $e->getMessage());
}

// الحصول على رسالة الفلاش
$flash_message = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستندات - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($flash_message): ?>
            <div class="mb-6 p-4 rounded-lg <?php echo $flash_message['type'] === 'success' ? 'bg-green-100 text-green-700 border border-green-300' : 'bg-red-100 text-red-700 border border-red-300'; ?>">
                <div class="flex items-center">
                    <i class="fas <?php echo $flash_message['type'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> ml-2"></i>
                    <?php echo htmlspecialchars($flash_message['message']); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-earth-green mb-2">إدارة المستندات</h1>
                    <p class="text-gray-600">عرض وإدارة المستندات المرفوعة من العملاء</p>
                </div>
                <a href="<?php echo getUrl('dashboard/accountant'); ?>" 
                   class="bg-earth-green text-white px-4 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                    <i class="fas fa-arrow-right ml-2"></i>
                    العودة للوحة التحكم
                </a>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="grid md:grid-cols-2 lg:grid-cols-6 gap-4 mb-8">
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <div class="text-2xl font-bold text-earth-green"><?php echo $stats['total']; ?></div>
                <div class="text-sm text-gray-600">إجمالي المستندات</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <div class="text-2xl font-bold text-green-600"><?php echo $stats['processed']; ?></div>
                <div class="text-sm text-gray-600">معالجة</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <div class="text-2xl font-bold text-yellow-600"><?php echo $stats['unprocessed']; ?></div>
                <div class="text-sm text-gray-600">غير معالجة</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <div class="text-2xl font-bold text-red-600"><?php echo $stats['pdf']; ?></div>
                <div class="text-sm text-gray-600">PDF</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <div class="text-2xl font-bold text-blue-600"><?php echo $stats['image']; ?></div>
                <div class="text-sm text-gray-600">صور</div>
            </div>
            <div class="bg-white rounded-lg shadow p-4 text-center">
                <div class="text-2xl font-bold text-purple-600"><?php echo $stats['office']; ?></div>
                <div class="text-sm text-gray-600">مكتب</div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <form method="GET" class="grid md:grid-cols-2 lg:grid-cols-5 gap-4">

                <!-- Request Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">فلترة حسب الطلب</label>
                    <select name="request_id" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green">
                        <option value="0">جميع الطلبات</option>
                        <?php foreach ($requests_list as $req): ?>
                            <option value="<?php echo $req['id']; ?>" <?php echo $request_id == $req['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars(substr($req['request_title'], 0, 30)) . (strlen($req['request_title']) > 30 ? '...' : ''); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <!-- Processed Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">حالة المعالجة</label>
                    <select name="processed" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green">
                        <option value="all" <?php echo $processed_filter === 'all' ? 'selected' : ''; ?>>جميع المستندات</option>
                        <option value="processed" <?php echo $processed_filter === 'processed' ? 'selected' : ''; ?>>معالجة</option>
                        <option value="unprocessed" <?php echo $processed_filter === 'unprocessed' ? 'selected' : ''; ?>>غير معالجة</option>
                    </select>
                </div>

                <!-- File Type Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">نوع الملف</label>
                    <select name="file_type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green">
                        <option value="all" <?php echo $file_type_filter === 'all' ? 'selected' : ''; ?>>جميع الأنواع</option>
                        <option value="pdf" <?php echo $file_type_filter === 'pdf' ? 'selected' : ''; ?>>PDF</option>
                        <option value="jpg" <?php echo $file_type_filter === 'jpg' ? 'selected' : ''; ?>>صور JPG</option>
                        <option value="png" <?php echo $file_type_filter === 'png' ? 'selected' : ''; ?>>صور PNG</option>
                        <option value="doc" <?php echo $file_type_filter === 'doc' ? 'selected' : ''; ?>>مستندات Word</option>
                        <option value="xls" <?php echo $file_type_filter === 'xls' ? 'selected' : ''; ?>>جداول Excel</option>
                    </select>
                </div>

                <!-- Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">البحث</label>
                    <input type="text"
                           name="search"
                           value="<?php echo htmlspecialchars($search); ?>"
                           placeholder="البحث في اسم الملف..."
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green">
                </div>

                <!-- Submit -->
                <div class="flex items-end">
                    <button type="submit"
                            class="w-full bg-earth-green text-white px-4 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                        <i class="fas fa-search ml-2"></i>
                        بحث
                    </button>
                </div>

            </form>
        </div>

        <!-- Documents Grid -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-semibold text-earth-green">
                    المستندات (<?php echo count($documents); ?>)
                </h2>
            </div>

            <?php if (empty($documents)): ?>
                <div class="text-center py-12">
                    <i class="fas fa-folder-open text-6xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد مستندات</h3>
                    <p class="text-gray-500">لا توجد مستندات تطابق معايير البحث المحددة</p>
                </div>
            <?php else: ?>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6">
                    <?php foreach ($documents as $document): ?>
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-lg transition-shadow">

                            <!-- File Icon -->
                            <div class="text-center mb-4">
                                <?php
                                $file_icon = 'fa-file';
                                $icon_color = 'text-gray-500';

                                switch (strtolower($document['file_type'])) {
                                    case 'pdf':
                                        $file_icon = 'fa-file-pdf';
                                        $icon_color = 'text-red-500';
                                        break;
                                    case 'jpg':
                                    case 'jpeg':
                                    case 'png':
                                    case 'gif':
                                        $file_icon = 'fa-file-image';
                                        $icon_color = 'text-blue-500';
                                        break;
                                    case 'doc':
                                    case 'docx':
                                        $file_icon = 'fa-file-word';
                                        $icon_color = 'text-blue-600';
                                        break;
                                    case 'xls':
                                    case 'xlsx':
                                        $file_icon = 'fa-file-excel';
                                        $icon_color = 'text-green-600';
                                        break;
                                    case 'ppt':
                                    case 'pptx':
                                        $file_icon = 'fa-file-powerpoint';
                                        $icon_color = 'text-orange-600';
                                        break;
                                }
                                ?>
                                <i class="fas <?php echo $file_icon; ?> text-4xl <?php echo $icon_color; ?>"></i>
                            </div>

                            <!-- File Info -->
                            <div class="text-center mb-4">
                                <h3 class="font-semibold text-gray-900 mb-2 text-sm">
                                    <?php echo htmlspecialchars(substr($document['original_filename'], 0, 25)) . (strlen($document['original_filename']) > 25 ? '...' : ''); ?>
                                </h3>
                                <p class="text-xs text-gray-600 mb-1">
                                    الطلب: <?php echo htmlspecialchars(substr($document['request_title'], 0, 20)) . (strlen($document['request_title']) > 20 ? '...' : ''); ?>
                                </p>
                                <p class="text-xs text-gray-600 mb-1">
                                    العميل: <?php echo htmlspecialchars($document['client_name']); ?>
                                </p>
                                <p class="text-xs text-gray-500">
                                    <?php echo number_format($document['file_size'] / 1024, 1); ?> KB
                                </p>
                            </div>

                            <!-- Status -->
                            <div class="text-center mb-4">
                                <?php if ($document['is_processed']): ?>
                                    <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">
                                        <i class="fas fa-check ml-1"></i>
                                        معالج
                                    </span>
                                <?php else: ?>
                                    <span class="px-2 py-1 bg-yellow-100 text-yellow-800 text-xs rounded-full">
                                        <i class="fas fa-clock ml-1"></i>
                                        غير معالج
                                    </span>
                                <?php endif; ?>
                            </div>

                            <!-- Actions -->
                            <div class="flex justify-center space-x-2 space-x-reverse">
                                <!-- View -->
                                <a href="<?php echo getUrl('backend/view-document.php?id=' . $document['id']); ?>"
                                   target="_blank"
                                   class="bg-earth-green text-white px-3 py-1 rounded text-xs hover:bg-sand-brown transition-colors"
                                   title="عرض الملف">
                                    <i class="fas fa-eye"></i>
                                </a>

                                <!-- Download -->
                                <a href="<?php echo getUrl('backend/download-document.php?id=' . $document['id']); ?>"
                                   class="bg-blue-500 text-white px-3 py-1 rounded text-xs hover:bg-blue-600 transition-colors"
                                   title="تحميل الملف">
                                    <i class="fas fa-download"></i>
                                </a>

                                <!-- AI Analysis -->
                                <button onclick="openAnalysisModal(<?php echo $document['id']; ?>, '<?php echo htmlspecialchars($document['original_filename'], ENT_QUOTES); ?>', <?php echo $document['is_processed']; ?>, '<?php echo htmlspecialchars($document['ai_analysis'] ?? '', ENT_QUOTES); ?>')"
                                        class="bg-purple-500 text-white px-3 py-1 rounded text-xs hover:bg-purple-600 transition-colors"
                                        title="تحليل بالذكاء الاصطناعي">
                                    <i class="fas fa-robot"></i>
                                </button>

                                <!-- Update Status -->
                                <button onclick="toggleProcessed(<?php echo $document['id']; ?>, <?php echo $document['is_processed'] ? 0 : 1; ?>)"
                                        class="bg-<?php echo $document['is_processed'] ? 'yellow' : 'green'; ?>-500 text-white px-3 py-1 rounded text-xs hover:bg-<?php echo $document['is_processed'] ? 'yellow' : 'green'; ?>-600 transition-colors"
                                        title="<?php echo $document['is_processed'] ? 'إلغاء المعالجة' : 'تعيين كمعالج'; ?>">
                                    <i class="fas fa-<?php echo $document['is_processed'] ? 'undo' : 'check'; ?>"></i>
                                </button>
                            </div>

                            <!-- Upload Date -->
                            <div class="text-center mt-3 text-xs text-gray-500">
                                <i class="fas fa-calendar ml-1"></i>
                                <?php echo formatArabicDate($document['upload_date']); ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- AI Analysis Modal -->
    <div id="analysisModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">تحليل المستند بالذكاء الاصطناعي</h3>
                        <button onclick="closeAnalysisModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="mb-4">
                        <p class="text-sm text-gray-600">اسم الملف: <span id="modalFileName" class="font-semibold"></span></p>
                    </div>

                    <form method="POST" id="analysisForm">
                        <input type="hidden" name="update_document" value="1">
                        <input type="hidden" name="document_id" id="modalDocumentId">
                        <input type="hidden" name="is_processed" id="modalIsProcessed">

                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">تحليل الذكاء الاصطناعي</label>
                            <textarea name="ai_analysis"
                                      id="modalAiAnalysis"
                                      rows="8"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green"
                                      placeholder="أضف تحليل الذكاء الاصطناعي للمستند..."></textarea>
                        </div>

                        <div class="flex justify-between">
                            <div class="flex space-x-3 space-x-reverse">
                                <button type="button"
                                        onclick="closeAnalysisModal()"
                                        class="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors">
                                    إلغاء
                                </button>
                                <button type="submit"
                                        class="px-4 py-2 bg-earth-green text-white rounded-lg hover:bg-sand-brown transition-colors">
                                    حفظ التحليل
                                </button>
                            </div>
                            <button type="button"
                                    onclick="generateAIAnalysis()"
                                    class="px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors">
                                <i class="fas fa-robot ml-2"></i>
                                تحليل تلقائي
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>

    <script>
        function openAnalysisModal(documentId, fileName, isProcessed, aiAnalysis) {
            document.getElementById('modalDocumentId').value = documentId;
            document.getElementById('modalFileName').textContent = fileName;
            document.getElementById('modalIsProcessed').value = 1; // Mark as processed when saving analysis
            document.getElementById('modalAiAnalysis').value = aiAnalysis;
            document.getElementById('analysisModal').classList.remove('hidden');
        }

        function closeAnalysisModal() {
            document.getElementById('analysisModal').classList.add('hidden');
        }

        function toggleProcessed(documentId, newStatus) {
            // Create a form to submit the status change
            const form = document.createElement('form');
            form.method = 'POST';
            form.style.display = 'none';

            const updateInput = document.createElement('input');
            updateInput.type = 'hidden';
            updateInput.name = 'update_document';
            updateInput.value = '1';

            const idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'document_id';
            idInput.value = documentId;

            const statusInput = document.createElement('input');
            statusInput.type = 'hidden';
            statusInput.name = 'is_processed';
            statusInput.value = newStatus;

            const analysisInput = document.createElement('input');
            analysisInput.type = 'hidden';
            analysisInput.name = 'ai_analysis';
            analysisInput.value = '';

            form.appendChild(updateInput);
            form.appendChild(idInput);
            form.appendChild(statusInput);
            form.appendChild(analysisInput);

            document.body.appendChild(form);
            form.submit();
        }

        function generateAIAnalysis() {
            const documentId = document.getElementById('modalDocumentId').value;
            const analysisTextarea = document.getElementById('modalAiAnalysis');

            // Show loading state
            analysisTextarea.value = 'جاري تحليل المستند بالذكاء الاصطناعي...';
            analysisTextarea.disabled = true;

            // Make AJAX request to AI analysis endpoint
            fetch('<?php echo getUrl("backend/ai-analyze-document.php"); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    document_id: documentId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    analysisTextarea.value = data.analysis;
                } else {
                    analysisTextarea.value = 'حدث خطأ أثناء تحليل المستند: ' + (data.error || 'خطأ غير معروف');
                }
            })
            .catch(error => {
                analysisTextarea.value = 'حدث خطأ في الاتصال بخدمة الذكاء الاصطناعي';
                console.error('Error:', error);
            })
            .finally(() => {
                analysisTextarea.disabled = false;
            });
        }

        // Close modal when clicking outside
        document.getElementById('analysisModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeAnalysisModal();
            }
        });
    </script>
</body>
</html>
