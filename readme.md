# نظام المحاسبك - Mohassebk System

## نظام إدارة الخدمات المحاسبية الذكي
### Intelligent Accounting Services Management System

---

## 📋 نظرة عامة / Overview

**المحاسبك** هو نظام شامل لإدارة الخدمات المحاسبية يجمع بين التقنيات الحديثة والذكاء الاصطناعي لتقديم تجربة محاسبية متطورة.

**Mohassebk** is a comprehensive accounting services management system that combines modern technologies with artificial intelligence to provide an advanced accounting experience.

مكونات المشروع الأساسية:

1\. واجهة المستخدم (العملاء):

•	تسجيل الدخول/إنشاء حساب

•	طلب الخدمات المحاسبية    
حساب الضرائب 
المراجعة والتدقيق المالي 
اعداد القوائم المالية 
تسوية الحسابات المالية 
ادارة الارباح والضرائب 
ادارة الديون 
حدمات الاستشارة المالية

•	تحميل المستندات الضرورية (بامكان المستخدم رفع المستندات بدون تشفير)

•	تتبع حالة الطلب

•	إشعارات بالحالة

•	ملف شخصي للمستخدم

2\. واجهة المحاسب (الإدارة):

•	تسجيل دخول خاص

•	إدارة ومعالجة الطلبات

•	تحديث الحالات

•	التواصل مع المستخدم

3\. نظام التسجيل وتسجيل الدخول:

•	مع صلاحيات تفصيلية لكل دور

4\. دمج مبدئي للذكاء الاصطناعي:

•	اقتراحات ذكية

•	تحليل مبدئي للملفات المحاسبية

5\. محاكاة بوابة دفع إلكتروني:

•	تجربة دفع وهمي (بدون ربط حقيقي)

لوحة تحكم بالمتغيرات و ادارة التطبيق 


✅ تعليمات واضحة للحصول على نتيجة جيدة 

أريد بناء مشروع ويب احترافي ومرتب باستخدام تقنيات حديثة، بكود نظيف ومنظم، وتصميم واجهات جميلة وعصرية، مع فصل الملفات بشكل احترافي وتوحيد أسلوب التصميم عبر جميع الصفحات.



تصميم الواجهة (Frontend):

1\.	✅ استخدم UI/UX حديث ومتجاوب (Responsive) لكل الشاشات (حاسوب، لوحي، هاتف). عبر استخدام tailwind

2\.	✅ اختر خط عربي جميل واحترافي (مثل Cairo, Amiri, Tajawal).

3\.	✅ استخدم CSS خارجي موحد لتوحيد الألوان والخطوط والمسافات.

4\.	✅ أنشئ ملف style.css أو استخدم SCSS/SASS عند الحاجة.

5\.	✅ ضع شريط التنقل (Navbar) في ملف منفصل (مثلاً navbar.php أو navbar.html) وقم بإدراجه في كل الصفحات.

6\.	✅ استخدم تصميم متناسق لجميع الصفحات (header، footer، ألوان، buttons، forms).

7\.	✅ أنشئ ملف footer.html منفصل لسهولة التكرار.

8\.	•  الألوان المستخدمة من Muted Modern Palette مثل:

9\.	رمادي مخضر (#CAD2C5)

10\.	بيج دافئ (#FFE5B4)

11\.	أخضر ترابي (#52796F)

12\.	بني رملي (#8D7B68)

13\.	أبيض مائل للرمادي (#F0F0F0)

14\.	•  دعم كامل للـ RTL (من اليمين لليسار).

15\.	

التعامل مع البيانات (Backend):

1\.	✅ فصل الأكواد الخلفية في مجلد backend/ بشكل منظم حسب الوظائف (auth، api، db).

2\.	✅ إنشاء ملف اتصال موحد بقاعدة البيانات db.php وتضمينه عند الحاجة.

3\.	✅ تنظيم عمليات المصادقة والتسجيل داخل مجلد auth/.

4\.	✅ فصل كل خدمة أو وظيفة في API بملف مستقل (modular coding).

مميزات إضافية مطلوبة:

1\.	✅ دعم اللغة العربية RTL بشكل كامل.

2\.	✅ وضع صور وهمية وعناوين وهمية قابلة للاستبدال.

3\.	✅ ضمان سرعة تحميل الموقع (كود خفيف، ملفات منظمة).

4\.	✅ توحيد تنسيقات الأزرار، المدخلات، والبطاقات عبر الموقع.

5\.	✅ إنشاء ملف config.php أو .env لحفظ المتغيرات العامة.

الأنيميشن (Animations):

•	استخدام scrolling animation عند دخول العناصر باستخدام مكتبة مثل AOS أو CSS Scroll Effects.

•	استخدام text animation في العناوين والشعارات (باستخدام CSS أو GSAP).

•	الحركات تكون ناعمة وغير مزعجة (ease-in, fade, scale, slide...).

قاعدى بيانات mysql

الموقع للدراسة فقط لا تهتم بالحمالية كثيرا 



i dont hava database for now make a one, i use xammp

make a folder to upload the documents witout chiffremnt 

    // إعدادات OpenRouter.ai API
    $api_key = '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';
    $api_url = 'https://openrouter.ai/api/v1/chat/completions';
    $model = 'deepseek/deepseek-chat:free'; // نموذج DeepSeek V3 المجاني - أداء عالي بدون تكلفة