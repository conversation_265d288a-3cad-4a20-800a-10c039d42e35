<?php
/**
 * Client Request Details Page
 * صفحة تفاصيل الطلب للعميل
 */

require_once '../../backend/config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'client') {
    redirect(getUrl('dashboard/client'));
}

// الحصول على معرف الطلب
$request_id = (int)($_GET['id'] ?? 0);

if (!$request_id) {
    redirect(getUrl('dashboard/client/requests.php'));
}

// الحصول على تفاصيل الطلب
$request = null;
try {
    $sql = "SELECT sr.*, s.service_name_ar, s.service_name_en, s.description_ar,
                   u.full_name as accountant_name, u.email as accountant_email
            FROM service_requests sr
            LEFT JOIN services s ON sr.service_id = s.id
            LEFT JOIN users u ON sr.assigned_accountant_id = u.id
            WHERE sr.id = :id AND sr.user_id = :user_id";

    $request = fetchOne($sql, ['id' => $request_id, 'user_id' => $user['id']]);

    if (!$request) {
        redirect(getUrl('dashboard/client/requests.php'));
    }
} catch (Exception $e) {
    logError("Request details error: " . $e->getMessage());
    redirect(getUrl('dashboard/client/requests.php'));
}

// الحصول على المستندات المرتبطة بالطلب
$documents = [];
try {
    $documents_sql = "SELECT * FROM documents WHERE request_id = :request_id ORDER BY created_at DESC";
    $documents = fetchAll($documents_sql, ['request_id' => $request_id]);
} catch (Exception $e) {
    logError("Documents fetch error: " . $e->getMessage());
}

// الحصول على المدفوعات المرتبطة بالطلب
$payments = [];
try {
    $payments_sql = "SELECT * FROM payments WHERE request_id = :request_id ORDER BY created_at DESC";
    $payments = fetchAll($payments_sql, ['request_id' => $request_id]);
} catch (Exception $e) {
    logError("Payments fetch error: " . $e->getMessage());
}

// معالجة رفع المستندات
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = sanitize($_POST['action']);
    
    if ($action === 'upload_document' && isset($_FILES['document'])) {
        try {
            $file = $_FILES['document'];
            
            // التحقق من الملف
            if ($file['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('خطأ في رفع الملف');
            }
            
            // التحقق من نوع الملف
            $allowed_types = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png'];
            $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
            
            if (!in_array($file_extension, $allowed_types)) {
                throw new Exception('نوع الملف غير مدعوم');
            }
            
            // التحقق من حجم الملف (10MB)
            if ($file['size'] > 10 * 1024 * 1024) {
                throw new Exception('حجم الملف كبير جداً (الحد الأقصى 10MB)');
            }
            
            // إنشاء مجلد الرفع إذا لم يكن موجوداً
            $upload_dir = '../../uploads/documents/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            // إنشاء اسم ملف فريد
            $filename = uniqid() . '_' . time() . '.' . $file_extension;
            $file_path = $upload_dir . $filename;
            
            // رفع الملف
            if (move_uploaded_file($file['tmp_name'], $file_path)) {
                // حفظ معلومات الملف في قاعدة البيانات
                global $database;
                $document_data = [
                    'user_id' => $user['id'],
                    'request_id' => $request_id,
                    'original_filename' => $file['name'],
                    'stored_filename' => $filename,
                    'file_path' => 'uploads/documents/' . $filename,
                    'file_size' => $file['size'],
                    'file_type' => $file_extension,
                    'mime_type' => $file['type'],
                    'upload_date' => date('Y-m-d H:i:s')
                ];
                
                $database->insert('documents', $document_data);
                $success_message = 'تم رفع المستند بنجاح';
                
                // إعادة تحميل المستندات
                $documents = fetchAll($documents_sql, ['request_id' => $request_id]);
            } else {
                throw new Exception('فشل في رفع الملف');
            }
            
        } catch (Exception $e) {
            $error_message = $e->getMessage();
            logError("Document upload error: " . $e->getMessage());
        }
    }
}

// تحديد حالة الطلب
$status_colors = [
    'pending' => 'bg-yellow-100 text-yellow-800',
    'in_progress' => 'bg-blue-100 text-blue-800',
    'completed' => 'bg-green-100 text-green-800',
    'cancelled' => 'bg-red-100 text-red-800'
];

$status_names = [
    'pending' => 'في الانتظار',
    'in_progress' => 'قيد التنفيذ',
    'completed' => 'مكتمل',
    'cancelled' => 'ملغي'
];

$priority_colors = [
    'low' => 'bg-gray-100 text-gray-800',
    'medium' => 'bg-yellow-100 text-yellow-800',
    'high' => 'bg-red-100 text-red-800'
];

$priority_names = [
    'low' => 'منخفضة',
    'medium' => 'متوسطة',
    'high' => 'عالية'
];
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تفاصيل الطلب #<?php echo $request['id']; ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include '../../frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Flash Messages -->
        <?php if ($success_message): ?>
            <div class="mb-6 p-4 rounded-lg bg-green-100 text-green-700 border border-green-300">
                <div class="flex items-center">
                    <i class="fas fa-check-circle ml-2"></i>
                    <?php echo htmlspecialchars($success_message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="mb-6 p-4 rounded-lg bg-red-100 text-red-700 border border-red-300">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle ml-2"></i>
                    <?php echo htmlspecialchars($error_message); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- Header -->
        <div class="mb-8">
            <nav class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600 mb-4">
                <a href="<?php echo getUrl(); ?>" class="hover:text-earth-green transition-colors">الرئيسية</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <a href="<?php echo getUrl('dashboard/client'); ?>" class="hover:text-earth-green transition-colors">حسابي</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <a href="<?php echo getUrl('dashboard/client/requests.php'); ?>" class="hover:text-earth-green transition-colors">طلباتي</a>
                <i class="fas fa-chevron-left text-xs"></i>
                <span class="text-earth-green">تفاصيل الطلب #<?php echo $request['id']; ?></span>
            </nav>
            
            <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-earth-green mb-2">تفاصيل الطلب #<?php echo $request['id']; ?></h1>
                    <p class="text-gray-600"><?php echo htmlspecialchars($request['request_title'] ?? $request['title'] ?? 'طلب خدمة'); ?></p>
                </div>
                <div class="mt-4 md:mt-0 flex items-center space-x-4 space-x-reverse">
                    <span class="px-3 py-1 text-sm font-semibold rounded-full <?php echo $status_colors[$request['status']] ?? 'bg-gray-100 text-gray-800'; ?>">
                        <?php echo $status_names[$request['status']] ?? $request['status']; ?>
                    </span>
                    <a href="<?php echo getUrl('dashboard/client/requests.php'); ?>" 
                       class="bg-sand-brown text-white px-6 py-2 rounded-lg hover:bg-earth-green transition-colors">
                        <i class="fas fa-arrow-right ml-2"></i>
                        العودة للطلبات
                    </a>
                </div>
            </div>
        </div>

        <!-- Request Details -->
        <div class="grid lg:grid-cols-3 gap-8 mb-8">

            <!-- Main Details -->
            <div class="lg:col-span-2 space-y-6">

                <!-- Request Information -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-earth-green mb-6">معلومات الطلب</h2>

                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">عنوان الطلب</label>
                            <p class="text-gray-900 bg-gray-50 px-4 py-2 rounded-lg">
                                <?php echo htmlspecialchars($request['request_title'] ?? $request['title'] ?? 'طلب خدمة'); ?>
                            </p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">نوع الخدمة</label>
                            <p class="text-gray-900 bg-gray-50 px-4 py-2 rounded-lg">
                                <?php echo htmlspecialchars($request['service_name_ar']); ?>
                            </p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الحالة</label>
                            <span class="px-3 py-1 text-sm font-semibold rounded-full <?php echo $status_colors[$request['status']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                <?php echo $status_names[$request['status']] ?? $request['status']; ?>
                            </span>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الأولوية</label>
                            <span class="px-3 py-1 text-sm font-semibold rounded-full <?php echo $priority_colors[$request['priority']] ?? 'bg-gray-100 text-gray-800'; ?>">
                                <?php echo $priority_names[$request['priority']] ?? $request['priority']; ?>
                            </span>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">المبلغ الإجمالي</label>
                            <p class="text-gray-900 bg-gray-50 px-4 py-2 rounded-lg font-semibold">
                                <?php echo formatCurrency($request['total_amount']); ?>
                            </p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ الإنشاء</label>
                            <p class="text-gray-900 bg-gray-50 px-4 py-2 rounded-lg">
                                <?php echo date('Y-m-d H:i', strtotime($request['created_at'])); ?>
                            </p>
                        </div>

                        <?php if ($request['estimated_completion']): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تاريخ الإنجاز المتوقع</label>
                            <p class="text-gray-900 bg-gray-50 px-4 py-2 rounded-lg">
                                <?php echo date('Y-m-d', strtotime($request['estimated_completion'])); ?>
                            </p>
                        </div>
                        <?php endif; ?>

                        <?php if ($request['assigned_accountant_id'] && $request['accountant_name']): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">المحاسب المسؤول</label>
                            <p class="text-gray-900 bg-gray-50 px-4 py-2 rounded-lg">
                                <i class="fas fa-user-tie ml-2 text-purple-600"></i>
                                <?php echo htmlspecialchars($request['accountant_name']); ?>
                            </p>
                        </div>
                        <?php endif; ?>
                    </div>

                    <?php if ($request['description']): ?>
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">وصف الطلب</label>
                        <div class="text-gray-900 bg-gray-50 px-4 py-3 rounded-lg">
                            <?php echo nl2br(htmlspecialchars($request['description'])); ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Documents Section -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-semibold text-earth-green">المستندات المرفقة</h2>
                        <button onclick="openUploadModal()"
                                class="bg-earth-green text-white px-4 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                            <i class="fas fa-upload ml-2"></i>
                            رفع مستند
                        </button>
                    </div>

                    <?php if (empty($documents)): ?>
                        <div class="text-center py-8">
                            <i class="fas fa-folder-open text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600 mb-4">لا توجد مستندات مرفقة</p>
                            <button onclick="openUploadModal()"
                                    class="bg-earth-green text-white px-6 py-2 rounded-lg hover:bg-sand-brown transition-colors">
                                رفع أول مستند
                            </button>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($documents as $document): ?>
                                <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center ml-3">
                                            <?php
                                            $icon = 'fa-file';
                                            switch ($document['file_type']) {
                                                case 'pdf': $icon = 'fa-file-pdf'; break;
                                                case 'doc':
                                                case 'docx': $icon = 'fa-file-word'; break;
                                                case 'xls':
                                                case 'xlsx': $icon = 'fa-file-excel'; break;
                                                case 'jpg':
                                                case 'jpeg':
                                                case 'png': $icon = 'fa-file-image'; break;
                                            }
                                            ?>
                                            <i class="fas <?php echo $icon; ?> text-blue-600"></i>
                                        </div>
                                        <div>
                                            <p class="font-medium text-gray-900">
                                                <?php echo htmlspecialchars($document['original_filename']); ?>
                                            </p>
                                            <p class="text-sm text-gray-500">
                                                <?php echo number_format($document['file_size'] / 1024, 1); ?> KB •
                                                <?php echo date('Y-m-d H:i', strtotime($document['upload_date'])); ?>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="flex items-center space-x-2 space-x-reverse">
                                        <a href="../../<?php echo $document['file_path']; ?>"
                                           target="_blank"
                                           class="text-blue-600 hover:text-blue-800 transition-colors"
                                           title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="../../<?php echo $document['file_path']; ?>"
                                           download
                                           class="text-green-600 hover:text-green-800 transition-colors"
                                           title="تحميل">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">

                <!-- Payment Status -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-earth-green mb-4">حالة الدفع</h3>

                    <?php if (empty($payments)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-credit-card text-3xl text-gray-400 mb-3"></i>
                            <p class="text-gray-600 text-sm">لا توجد مدفوعات</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-3">
                            <?php foreach ($payments as $payment): ?>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div>
                                        <p class="font-medium text-gray-900">
                                            <?php echo formatCurrency($payment['amount']); ?>
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            <?php echo date('Y-m-d', strtotime($payment['created_at'])); ?>
                                        </p>
                                    </div>
                                    <span class="px-2 py-1 text-xs font-semibold rounded-full
                                        <?php echo $payment['payment_status'] === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                        <?php echo $payment['payment_status'] === 'completed' ? 'مدفوع' : 'معلق'; ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-earth-green mb-4">إجراءات سريعة</h3>

                    <div class="space-y-3">
                        <button onclick="openUploadModal()"
                                class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-upload ml-2"></i>
                            رفع مستند
                        </button>

                        <?php if ($request['accountant_email']): ?>
                        <a href="mailto:<?php echo $request['accountant_email']; ?>"
                           class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors block text-center">
                            <i class="fas fa-envelope ml-2"></i>
                            مراسلة المحاسب
                        </a>
                        <?php endif; ?>

                        <a href="<?php echo getUrl('ai-assistant.php'); ?>"
                           class="w-full bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors block text-center">
                            <i class="fas fa-robot ml-2"></i>
                            المساعد الذكي
                        </a>
                    </div>
                </div>

                <!-- Request Timeline -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-earth-green mb-4">الجدول الزمني</h3>

                    <div class="space-y-4">
                        <div class="flex items-start">
                            <div class="w-3 h-3 bg-blue-500 rounded-full mt-2 ml-3"></div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">تم إنشاء الطلب</p>
                                <p class="text-xs text-gray-500">
                                    <?php echo date('Y-m-d H:i', strtotime($request['created_at'])); ?>
                                </p>
                            </div>
                        </div>

                        <?php if ($request['assigned_accountant_id']): ?>
                        <div class="flex items-start">
                            <div class="w-3 h-3 bg-purple-500 rounded-full mt-2 ml-3"></div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">تم تعيين محاسب</p>
                                <p class="text-xs text-gray-500">
                                    <?php echo htmlspecialchars($request['accountant_name']); ?>
                                </p>
                            </div>
                        </div>
                        <?php endif; ?>

                        <?php if ($request['status'] === 'completed'): ?>
                        <div class="flex items-start">
                            <div class="w-3 h-3 bg-green-500 rounded-full mt-2 ml-3"></div>
                            <div>
                                <p class="text-sm font-medium text-gray-900">تم إكمال الطلب</p>
                                <p class="text-xs text-gray-500">
                                    <?php echo $request['actual_completion'] ? date('Y-m-d H:i', strtotime($request['actual_completion'])) : date('Y-m-d H:i', strtotime($request['updated_at'])); ?>
                                </p>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Modal -->
    <div id="uploadModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-lg font-semibold text-earth-green">رفع مستند جديد</h3>
                        <button onclick="closeUploadModal()" class="text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <form method="POST" enctype="multipart/form-data" class="space-y-4">
                        <input type="hidden" name="action" value="upload_document">

                        <div>
                            <label for="document" class="block text-sm font-medium text-gray-700 mb-2">
                                اختر المستند <span class="text-red-500">*</span>
                            </label>
                            <input type="file"
                                   id="document"
                                   name="document"
                                   required
                                   accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green">
                            <p class="text-xs text-gray-500 mt-1">
                                الأنواع المدعومة: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG (حد أقصى 10MB)
                            </p>
                        </div>

                        <div class="flex items-center justify-end space-x-4 space-x-reverse pt-4">
                            <button type="button"
                                    onclick="closeUploadModal()"
                                    class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors">
                                إلغاء
                            </button>
                            <button type="submit"
                                    class="px-4 py-2 bg-earth-green text-white rounded-lg hover:bg-sand-brown transition-colors">
                                <i class="fas fa-upload ml-2"></i>
                                رفع المستند
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include '../../frontend/components/footer.php'; ?>

    <!-- Scripts -->
    <script src="../../assets/js/main.js"></script>

    <script>
        function openUploadModal() {
            document.getElementById('uploadModal').classList.remove('hidden');
        }

        function closeUploadModal() {
            document.getElementById('uploadModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('uploadModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeUploadModal();
            }
        });
    </script>
</body>
</html>
