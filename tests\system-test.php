<?php
/**
 * System Test Script
 * سكريبت اختبار النظام
 */

require_once '../backend/config/config.php';

// تعطيل عرض الأخطاء للاختبار
error_reporting(E_ALL);
ini_set('display_errors', 1);

class SystemTest {
    private $results = [];
    private $passed = 0;
    private $failed = 0;
    
    public function __construct() {
        echo "<h1>اختبار نظام المحاسبك</h1>\n";
        echo "<style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .pass { color: green; }
            .fail { color: red; }
            .info { color: blue; }
            .section { margin: 20px 0; padding: 10px; border: 1px solid #ccc; }
        </style>\n";
    }
    
    public function runAllTests() {
        $this->testDatabaseConnection();
        $this->testConfigurationFiles();
        $this->testDirectoryStructure();
        $this->testDatabaseTables();
        $this->testUserAuthentication();
        $this->testFileUpload();
        $this->testAIIntegration();
        $this->testPaymentSystem();
        $this->displayResults();
    }
    
    private function testDatabaseConnection() {
        echo "<div class='section'><h2>اختبار الاتصال بقاعدة البيانات</h2>\n";
        
        try {
            global $database;
            $result = $database->query("SELECT 1");
            $this->pass("الاتصال بقاعدة البيانات");
        } catch (Exception $e) {
            $this->fail("الاتصال بقاعدة البيانات: " . $e->getMessage());
        }
        
        echo "</div>\n";
    }
    
    private function testConfigurationFiles() {
        echo "<div class='section'><h2>اختبار ملفات التكوين</h2>\n";
        
        // اختبار ملف التكوين الرئيسي
        if (defined('DB_HOST') && defined('DB_NAME') && defined('DB_USER')) {
            $this->pass("ملف التكوين الرئيسي");
        } else {
            $this->fail("ملف التكوين الرئيسي - متغيرات قاعدة البيانات غير محددة");
        }
        
        // اختبار متغيرات الموقع
        if (defined('SITE_NAME') && defined('SITE_URL')) {
            $this->pass("متغيرات الموقع");
        } else {
            $this->fail("متغيرات الموقع غير محددة");
        }
        
        // اختبار متغيرات الذكاء الاصطناعي
        if (defined('OPENROUTER_API_KEY') && defined('AI_MODEL')) {
            $this->pass("متغيرات الذكاء الاصطناعي");
        } else {
            $this->fail("متغيرات الذكاء الاصطناعي غير محددة");
        }
        
        echo "</div>\n";
    }
    
    private function testDirectoryStructure() {
        echo "<div class='section'><h2>اختبار هيكل المجلدات</h2>\n";
        
        $required_dirs = [
            'backend',
            'frontend',
            'assets',
            'uploads',
            'dashboard',
            'payment',
            'database'
        ];
        
        foreach ($required_dirs as $dir) {
            if (is_dir("../$dir")) {
                $this->pass("مجلد $dir موجود");
            } else {
                $this->fail("مجلد $dir غير موجود");
            }
        }
        
        // اختبار صلاحيات الكتابة
        $writable_dirs = ['uploads', 'logs'];
        foreach ($writable_dirs as $dir) {
            if (is_writable("../$dir")) {
                $this->pass("صلاحيات الكتابة في مجلد $dir");
            } else {
                $this->fail("لا توجد صلاحيات كتابة في مجلد $dir");
            }
        }
        
        echo "</div>\n";
    }
    
    private function testDatabaseTables() {
        echo "<div class='section'><h2>اختبار جداول قاعدة البيانات</h2>\n";
        
        $required_tables = [
            'users',
            'services',
            'service_requests',
            'documents',
            'payments',
            'notifications'
        ];
        
        try {
            global $database;
            foreach ($required_tables as $table) {
                $result = $database->query("SHOW TABLES LIKE '$table'");
                if ($result->rowCount() > 0) {
                    $this->pass("جدول $table موجود");
                } else {
                    $this->fail("جدول $table غير موجود");
                }
            }
        } catch (Exception $e) {
            $this->fail("خطأ في اختبار الجداول: " . $e->getMessage());
        }
        
        echo "</div>\n";
    }
    
    private function testUserAuthentication() {
        echo "<div class='section'><h2>اختبار نظام المصادقة</h2>\n";
        
        try {
            // اختبار تشفير كلمة المرور
            $password = 'test123';
            $hashed = hashPassword($password);
            if (verifyPassword($password, $hashed)) {
                $this->pass("تشفير والتحقق من كلمة المرور");
            } else {
                $this->fail("تشفير والتحقق من كلمة المرور");
            }
            
            // اختبار وجود المستخدم الإداري
            $admin = fetchOne("SELECT * FROM users WHERE role = 'admin' LIMIT 1");
            if ($admin) {
                $this->pass("المستخدم الإداري موجود");
            } else {
                $this->fail("المستخدم الإداري غير موجود");
            }
            
        } catch (Exception $e) {
            $this->fail("خطأ في اختبار المصادقة: " . $e->getMessage());
        }
        
        echo "</div>\n";
    }
    
    private function testFileUpload() {
        echo "<div class='section'><h2>اختبار رفع الملفات</h2>\n";
        
        // اختبار مجلد الرفع
        $upload_dir = '../uploads';
        if (is_dir($upload_dir) && is_writable($upload_dir)) {
            $this->pass("مجلد الرفع جاهز");
        } else {
            $this->fail("مجلد الرفع غير جاهز أو لا يمكن الكتابة فيه");
        }
        
        // اختبار الحد الأقصى لحجم الملف
        $max_size = ini_get('upload_max_filesize');
        if ($max_size) {
            $this->pass("الحد الأقصى لحجم الملف: $max_size");
        } else {
            $this->fail("لم يتم تحديد الحد الأقصى لحجم الملف");
        }
        
        echo "</div>\n";
    }
    
    private function testAIIntegration() {
        echo "<div class='section'><h2>اختبار تكامل الذكاء الاصطناعي</h2>\n";
        
        // اختبار متغيرات API
        if (defined('OPENROUTER_API_KEY') && !empty(OPENROUTER_API_KEY)) {
            $this->pass("مفتاح API للذكاء الاصطناعي محدد");
        } else {
            $this->fail("مفتاح API للذكاء الاصطناعي غير محدد");
        }
        
        if (defined('OPENROUTER_API_URL') && !empty(OPENROUTER_API_URL)) {
            $this->pass("رابط API للذكاء الاصطناعي محدد");
        } else {
            $this->fail("رابط API للذكاء الاصطناعي غير محدد");
        }
        
        // اختبار cURL
        if (function_exists('curl_init')) {
            $this->pass("مكتبة cURL متاحة");
        } else {
            $this->fail("مكتبة cURL غير متاحة");
        }
        
        echo "</div>\n";
    }
    
    private function testPaymentSystem() {
        echo "<div class='section'><h2>اختبار نظام الدفع</h2>\n";
        
        // اختبار وجود ملفات الدفع
        $payment_files = [
            '../payment/checkout.php',
            '../payment/success.php'
        ];
        
        foreach ($payment_files as $file) {
            if (file_exists($file)) {
                $this->pass("ملف الدفع " . basename($file) . " موجود");
            } else {
                $this->fail("ملف الدفع " . basename($file) . " غير موجود");
            }
        }
        
        // اختبار جدول المدفوعات
        try {
            global $database;
            $result = $database->query("DESCRIBE payments");
            if ($result->rowCount() > 0) {
                $this->pass("جدول المدفوعات جاهز");
            } else {
                $this->fail("جدول المدفوعات غير جاهز");
            }
        } catch (Exception $e) {
            $this->fail("خطأ في اختبار جدول المدفوعات: " . $e->getMessage());
        }
        
        echo "</div>\n";
    }
    
    private function pass($test) {
        echo "<p class='pass'>✓ $test</p>\n";
        $this->passed++;
    }
    
    private function fail($test) {
        echo "<p class='fail'>✗ $test</p>\n";
        $this->failed++;
    }
    
    private function displayResults() {
        echo "<div class='section'><h2>نتائج الاختبار</h2>\n";
        echo "<p class='info'>إجمالي الاختبارات: " . ($this->passed + $this->failed) . "</p>\n";
        echo "<p class='pass'>نجح: $this->passed</p>\n";
        echo "<p class='fail'>فشل: $this->failed</p>\n";
        
        if ($this->failed == 0) {
            echo "<h3 class='pass'>🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام</h3>\n";
        } else {
            echo "<h3 class='fail'>⚠️ يوجد $this->failed اختبار فاشل. يرجى إصلاح المشاكل قبل النشر</h3>\n";
        }
        
        echo "</div>\n";
    }
}

// تشغيل الاختبارات
$test = new SystemTest();
$test->runAllTests();
?>
