<?php
/**
 * Test Database Connection
 * اختبار الاتصال بقاعدة البيانات
 */

require_once 'backend/config/config.php';

echo "<h1>اختبار الاتصال بقاعدة البيانات</h1>";

try {
    // اختبار الاتصال
    $pdo = getDBConnection();
    echo "<p style='color: green;'>✅ تم الاتصال بقاعدة البيانات بنجاح</p>";
    
    // اختبار جدول service_requests
    $requests = fetchAll("SELECT COUNT(*) as count FROM service_requests");
    echo "<p>عدد الطلبات في قاعدة البيانات: " . $requests[0]['count'] . "</p>";
    
    // اختبار جدول users
    $users = fetchAll("SELECT COUNT(*) as count FROM users");
    echo "<p>عدد المستخدمين في قاعدة البيانات: " . $users[0]['count'] . "</p>";
    
    // اختبار جدول services
    $services = fetchAll("SELECT COUNT(*) as count FROM services");
    echo "<p>عدد الخدمات في قاعدة البيانات: " . $services[0]['count'] . "</p>";
    
    // إنشاء طلب تجريبي إذا لم يكن موجوداً
    $test_request = fetchOne("SELECT * FROM service_requests WHERE id = 1");
    if (!$test_request) {
        // التحقق من وجود مستخدم وخدمة
        $user = fetchOne("SELECT * FROM users WHERE role = 'client' LIMIT 1");
        $service = fetchOne("SELECT * FROM services WHERE is_active = 1 LIMIT 1");
        
        if ($user && $service) {
            global $database;
            $request_data = [
                'user_id' => $user['id'],
                'service_id' => $service['id'],
                'request_title' => 'طلب تجريبي لاختبار النظام',
                'description' => 'هذا طلب تجريبي لاختبار صفحة تفاصيل الطلب',
                'priority' => 'medium',
                'status' => 'pending',
                'total_amount' => 1500.00,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $request_id = $database->insert('service_requests', $request_data);
            echo "<p style='color: blue;'>✅ تم إنشاء طلب تجريبي برقم: $request_id</p>";
        } else {
            echo "<p style='color: orange;'>⚠️ لا يوجد مستخدمين أو خدمات في قاعدة البيانات</p>";
        }
    } else {
        echo "<p style='color: green;'>✅ يوجد طلب تجريبي برقم: " . $test_request['id'] . "</p>";
        echo "<p><strong>عنوان الطلب:</strong> " . htmlspecialchars($test_request['request_title']) . "</p>";
        echo "<p><strong>الحالة:</strong> " . $test_request['status'] . "</p>";
        echo "<p><strong>المبلغ:</strong> " . $test_request['total_amount'] . " د.ج</p>";
    }
    
    echo "<hr>";
    echo "<h2>روابط الاختبار:</h2>";
    echo "<ul>";
    echo "<li><a href='dashboard/client/request-details.php?id=1' target='_blank'>اختبار صفحة تفاصيل الطلب</a></li>";
    echo "<li><a href='dashboard/client/requests.php' target='_blank'>صفحة طلبات العميل</a></li>";
    echo "<li><a href='dashboard/client/' target='_blank'>لوحة تحكم العميل</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ خطأ: " . $e->getMessage() . "</p>";
}
?>
