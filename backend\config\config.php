<?php
/**
 * Main Configuration File
 * ملف الإعدادات الرئيسي
 */

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// إعدادات عامة للموقع
define('SITE_NAME', 'محاسبك الرقمي');
define('SITE_URL', 'http://localhost/Mohassebk');
define('SITE_EMAIL', '<EMAIL>');
define('SITE_PHONE', '+213123456789');

// إعدادات المجلدات
define('ROOT_PATH', dirname(dirname(__DIR__)));
define('BACKEND_PATH', ROOT_PATH . '/backend');
define('FRONTEND_PATH', ROOT_PATH . '/frontend');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('ASSETS_PATH', ROOT_PATH . '/assets');

// إعدادات رفع الملفات
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10 MB
define('ALLOWED_FILE_TYPES', ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'jpg', 'jpeg', 'png', 'gif']);
define('UPLOAD_DIR', UPLOADS_PATH . '/documents');

// إعدادات OpenRouter.ai API
define('OPENROUTER_API_KEY', '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');
define('OPENROUTER_API_URL', 'https://openrouter.ai/api/v1/chat/completions');
define('AI_MODEL', 'deepseek/deepseek-chat-v3-0324:free');

// إعدادات الأمان
define('HASH_ALGO', 'sha256');
define('ENCRYPTION_KEY', 'mohassebk_2024_secret_key');
define('JWT_SECRET', 'mohassebk_jwt_secret_2024');

// إعدادات البريد الإلكتروني (للتطوير)
define('SMTP_HOST', 'localhost');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');

// إعدادات التطبيق
define('DEBUG_MODE', true);
define('TIMEZONE', 'Africa/Algiers');
define('LANGUAGE', 'ar');
define('CURRENCY', 'DZD');

// تعيين المنطقة الزمنية
date_default_timezone_set(TIMEZONE);

// إعدادات عرض الأخطاء (للتطوير فقط)
if (DEBUG_MODE) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// تضمين ملف قاعدة البيانات
require_once BACKEND_PATH . '/config/db.php';

/**
 * دالة للحصول على إعدادات النظام من قاعدة البيانات
 */
function getSystemSetting($key, $default = null) {
    try {
        $sql = "SELECT setting_value FROM system_settings WHERE setting_key = :key";
        $result = fetchOne($sql, ['key' => $key]);
        return $result ? $result['setting_value'] : $default;
    } catch (Exception $e) {
        return $default;
    }
}

/**
 * دالة لتحديث إعدادات النظام
 */
function updateSystemSetting($key, $value) {
    try {
        global $database;
        $sql = "UPDATE system_settings SET setting_value = :value WHERE setting_key = :key";
        return $database->query($sql, ['key' => $key, 'value' => $value]);
    } catch (Exception $e) {
        return false;
    }
}

/**
 * دالة للحصول على URL كامل
 */
function getUrl($path = '') {
    return SITE_URL . '/' . ltrim($path, '/');
}

/**
 * دالة للحصول على مسار الأصول
 */
function getAssetUrl($path = '') {
    return SITE_URL . '/assets/' . ltrim($path, '/');
}

/**
 * دالة لتنظيف البيانات
 */
function sanitize($data) {
    if (is_array($data)) {
        return array_map('sanitize', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * دالة للتحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * دالة لتوليد رمز عشوائي
 */
function generateToken($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * دالة لتشفير كلمة المرور
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * دالة للتحقق من كلمة المرور
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * دالة لإعادة التوجيه
 */
function redirect($url) {
    header("Location: " . $url);
    exit();
}

/**
 * دالة للتحقق من تسجيل الدخول
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * دالة للحصول على المستخدم الحالي
 */
function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    $sql = "SELECT * FROM users WHERE id = :id AND is_active = 1";
    return fetchOne($sql, ['id' => $_SESSION['user_id']]);
}

/**
 * دالة للتحقق من صلاحيات المستخدم
 */
function hasRole($role) {
    $user = getCurrentUser();
    return $user && $user['role'] === $role;
}

/**
 * دالة لتسجيل الخروج
 */
function logout() {
    session_destroy();
    redirect(getUrl('login.php'));
}

/**
 * دالة لعرض الرسائل
 */
function showMessage($message, $type = 'info') {
    $_SESSION['flash_message'] = [
        'message' => $message,
        'type' => $type
    ];
}

/**
 * دالة للحصول على الرسائل المؤقتة
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        unset($_SESSION['flash_message']);
        return $message;
    }
    return null;
}

/**
 * دالة لتحويل التاريخ إلى التنسيق العربي
 */
function formatArabicDate($date, $format = 'Y-m-d H:i') {
    $arabicMonths = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = is_string($date) ? strtotime($date) : $date;
    $day = date('d', $timestamp);
    $month = $arabicMonths[(int)date('m', $timestamp)];
    $year = date('Y', $timestamp);
    $time = date('H:i', $timestamp);
    
    return "$day $month $year - $time";
}

/**
 * دالة لإنشاء مجلد إذا لم يكن موجوداً
 */
function createDirectoryIfNotExists($path) {
    if (!is_dir($path)) {
        mkdir($path, 0755, true);
    }
}

// إنشاء المجلدات المطلوبة
createDirectoryIfNotExists(UPLOAD_DIR);
createDirectoryIfNotExists(UPLOADS_PATH . '/profiles');
createDirectoryIfNotExists(UPLOADS_PATH . '/temp');

/**
 * دالة لتنسيق العملة الجزائرية
 */
function formatCurrency($amount, $showSymbol = true) {
    $formatted = number_format($amount, 2, '.', ',');
    return $showSymbol ? $formatted . ' د.ج' : $formatted;
}

/**
 * دالة لتسجيل الأخطاء
 */
function logError($message, $file = 'error.log') {
    $logPath = ROOT_PATH . '/logs/' . $file;
    createDirectoryIfNotExists(dirname($logPath));

    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message" . PHP_EOL;

    file_put_contents($logPath, $logMessage, FILE_APPEND | LOCK_EX);
}
?>
