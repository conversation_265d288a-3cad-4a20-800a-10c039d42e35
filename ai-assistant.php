<?php
/**
 * AI Assistant Page - Simple Version
 * صفحة المساعد الذكي - نسخة بسيطة
 */

require_once 'backend/config/config.php';
require_once 'backend/ai/ai-helper.php';

// دالة تسجيل الأخطاء إذا لم تكن موجودة
if (!function_exists('logError')) {
    function logError($message) {
        error_log(date('Y-m-d H:i:s') . " - " . $message . "\n", 3, 'ai_errors.log');
    }
}

// دالة API مباشرة كبديل
function callDirectAPI($prompt, $max_tokens = 800, $temperature = 0.7) {
    if (!defined('OPENROUTER_API_KEY') || empty(OPENROUTER_API_KEY)) {
        logError("Direct API: No API key");
        return false;
    }

    $data = [
        'model' => defined('AI_MODEL') ? AI_MODEL : 'deepseek/deepseek-chat-v3-0324:free',
        'messages' => [
            ['role' => 'user', 'content' => $prompt]
        ],
        'max_tokens' => $max_tokens,
        'temperature' => $temperature
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, defined('OPENROUTER_API_URL') ? OPENROUTER_API_URL : 'https://openrouter.ai/api/v1/chat/completions');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . OPENROUTER_API_KEY,
        'HTTP-Referer: http://localhost/Mohassebk',
        'X-Title: Mohassebk AI Assistant'
    ]);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curl_error = curl_error($ch);
    curl_close($ch);

    logError("Direct API: HTTP Code: $http_code, cURL Error: $curl_error");

    if ($response === false || $http_code !== 200) {
        logError("Direct API: Failed - HTTP: $http_code, Response: " . substr($response, 0, 200));
        return false;
    }

    $decoded = json_decode($response, true);
    if (isset($decoded['choices'][0]['message']['content'])) {
        $content = trim($decoded['choices'][0]['message']['content']);
        logError("Direct API: Success - Response length: " . strlen($content));
        return $content;
    }

    logError("Direct API: No content in response: " . substr($response, 0, 200));
    return false;
}

// إنشاء جدول المحادثات إذا لم يكن موجوداً
try {
    $create_table_sql = "CREATE TABLE IF NOT EXISTS `ai_conversations` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `user_id` int(11) DEFAULT NULL,
      `session_id` varchar(255) NOT NULL,
      `message` text NOT NULL,
      `response` text NOT NULL,
      `model_used` varchar(100) DEFAULT NULL,
      `tokens_used` int(11) DEFAULT 0,
      `processing_time` int(11) DEFAULT 0,
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `user_id` (`user_id`),
      KEY `session_id` (`session_id`),
      KEY `created_at` (`created_at`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    global $database;
    $database->query($create_table_sql);
} catch (Exception $e) {
    // تجاهل الخطأ إذا كان الجدول موجود بالفعل
}

// معالجة الرسائل
$response = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['message'])) {
    $message = trim($_POST['message']);

    if (!empty($message)) {
        try {
            $start_time = microtime(true);

            // إنشاء prompt مخصص للمحاسبة الجزائرية
            $prompt = createAccountingPrompt($message);

            // استدعاء AI API مباشرة
            $ai_response = false;

            // التحقق من إعدادات API
            if (defined('OPENROUTER_API_KEY') && !empty(OPENROUTER_API_KEY) && OPENROUTER_API_KEY !== 'your_openrouter_api_key_here') {
                // استخدام الدالة المباشرة دائماً لضمان العمل
                $ai_response = callDirectAPI($prompt, 800, 0.7);
            }

            if ($ai_response !== false && !empty($ai_response)) {
                $response = $ai_response;
                // حساب وقت المعالجة
                $processing_time = round((microtime(true) - $start_time) * 1000);
            } else {
                // في حالة فشل AI، استخدم الردود الجاهزة كـ fallback
                $response = getFallbackResponse($message);
            }

        } catch (Exception $e) {
            $error_message = 'عذراً، حدث خطأ أثناء التواصل مع المساعد الذكي. يرجى المحاولة مرة أخرى.';
            logError("AI Assistant error: " . $e->getMessage());

            // استخدام الردود الجاهزة كـ fallback
            $response = getFallbackResponse($message);

            // إضافة معلومات إضافية في وضع التطوير
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                $response .= "\n\n[وضع التطوير] خطأ: " . $e->getMessage();
            }
        }
    } else {
        $error_message = 'يرجى كتابة رسالة قبل الإرسال.';
    }
}

/**
 * إنشاء prompt مخصص للمحاسبة الجزائرية
 */
function createAccountingPrompt($user_message) {
    $prompt = "أنت مساعد محاسبي ذكي متخصص في المحاسبة والضرائب في الجزائر. لديك خبرة واسعة في:\n\n";

    $prompt .= "المجالات المحاسبية:\n";
    $prompt .= "- القوانين المحاسبية الجزائرية\n";
    $prompt .= "- النظام المحاسبي المالي (SCF)\n";
    $prompt .= "- ضريبة القيمة المضافة (TVA) 19%\n";
    $prompt .= "- ضريبة الدخل الإجمالي (IRG)\n";
    $prompt .= "- ضريبة أرباح الشركات (IBS)\n";
    $prompt .= "- الرسم على النشاط المهني (TAP)\n";
    $prompt .= "- أنواع الشركات: SARL, SPA, EURL, المؤسسة الفردية\n";
    $prompt .= "- إعداد القوائم المالية والميزانيات\n";
    $prompt .= "- التصريحات الضريبية والجمركية\n";
    $prompt .= "- المراجعة والتدقيق المحاسبي\n\n";

    $prompt .= "تعليمات الرد:\n";
    $prompt .= "1. اجعل إجابتك باللغة العربية\n";
    $prompt .= "2. كن دقيقاً ومهنياً\n";
    $prompt .= "3. اذكر المراجع القانونية عند الضرورة\n";
    $prompt .= "4. قدم أمثلة عملية عند الإمكان\n";
    $prompt .= "5. اجعل الرد مفهوماً وواضحاً\n";
    $prompt .= "6. لا تتجاوز 500 كلمة\n\n";

    $prompt .= "سؤال العميل: " . $user_message . "\n\n";
    $prompt .= "الرد:";

    return $prompt;
}

/**
 * الردود الجاهزة كـ fallback
 */
function getFallbackResponse($message) {
    // محاولة API مرة أخيرة قبل استخدام الردود المحلية
    if (defined('OPENROUTER_API_KEY') && !empty(OPENROUTER_API_KEY)) {
        $api_response = callDirectAPI("أنت مساعد محاسبي ذكي متخصص في المحاسبة والضرائب في الجزائر. أجب على السؤال التالي باللغة العربية بشكل مفصل ومفيد:\n\n" . $message, 500, 0.7);
        if ($api_response !== false && !empty($api_response)) {
            return $api_response;
        }
    }

    $responses = [
        'مرحبا' => '🤖 مرحباً بك في المساعد المحاسبي الذكي!\n\nأنا هنا لمساعدتك في:\n• الأسئلة المحاسبية والضريبية\n• قوانين الشركات في الجزائر\n• حساب الضرائب والرسوم\n• إجراءات التأسيس والتسجيل\n\nكيف يمكنني مساعدتك اليوم؟',

        'ضريبة القيمة المضافة' => '💰 ضريبة القيمة المضافة في الجزائر:\n\n📊 المعدل: 19%\n🧮 طريقة الحساب:\n• المبلغ الخاضع للضريبة × 0.19 = قيمة الضريبة\n• المبلغ الإجمالي = المبلغ الأساسي + الضريبة\n\n📝 مثال عملي:\nإذا كان المبلغ 10,000 د.ج\n• الضريبة = 10,000 × 0.19 = 1,900 د.ج\n• المبلغ الإجمالي = 10,000 + 1,900 = 11,900 د.ج\n\n⚠️ ملاحظة: بعض السلع والخدمات معفاة من هذه الضريبة',

        'أنواع الشركات' => '🏢 أنواع الشركات في الجزائر:\n\n1️⃣ SARL - الشركة ذات المسؤولية المحدودة\n• رأس المال الأدنى: 100,000 د.ج\n• عدد الشركاء: 2-50\n\n2️⃣ EURL - الشركة ذات الشخص الواحد\n• رأس المال الأدنى: 100,000 د.ج\n• شريك واحد فقط\n\n3️⃣ SPA - شركة المساهمة\n• رأس المال الأدنى: 5,000,000 د.ج\n• عدد المساهمين: 7 على الأقل\n\n4️⃣ المؤسسة الفردية\n• بدون رأس مال أدنى\n• مسؤولية غير محدودة',

        'تسجيل شركة' => '📋 خطوات تسجيل شركة في الجزائر:\n\n1️⃣ إعداد القانون الأساسي\n• عند الموثق المعتمد\n• تحديد نوع الشركة والنشاط\n\n2️⃣ إيداع رأس المال\n• في بنك معتمد\n• الحصول على شهادة الإيداع\n\n3️⃣ التسجيل في السجل التجاري\n• في المركز الوطني للسجل التجاري\n• تقديم الوثائق المطلوبة\n\n4️⃣ النشر في الجريدة الرسمية\n• إعلان تأسيس الشركة\n• دفع رسوم النشر\n\n5️⃣ الحصول على البطاقة الجبائية\n• من مديرية الضرائب\n• تسجيل النشاط الضريبي\n\n⏱️ المدة الإجمالية: 15-30 يوم عمل',

        'الضرائب' => '💼 الضرائب الرئيسية في الجزائر:\n\n1️⃣ IRG - ضريبة الدخل الإجمالي\n• على الأجور والمرتبات\n• معدلات تصاعدية 0%-35%\n\n2️⃣ IBS - ضريبة أرباح الشركات\n• معدل عام: 25%\n• معدل مخفض للمؤسسات الصغيرة: 19%\n\n3️⃣ TVA - ضريبة القيمة المضافة\n• معدل عام: 19%\n• معدل مخفض: 9%\n\n4️⃣ TAP - الرسم على النشاط المهني\n• 2% من رقم الأعمال\n• حد أدنى حسب النشاط\n\n📅 مواعيد التصريح:\n• شهرياً: TVA, IRG\n• سنوياً: IBS, TAP'
    ];

    // البحث عن كلمات مفتاحية
    $message_lower = strtolower($message);
    foreach ($responses as $keyword => $reply) {
        if (strpos($message_lower, $keyword) !== false) {
            return $reply;
        }
    }

    // رد افتراضي ذكي
    if (strpos($message, '؟') !== false) {
        return '🤔 سؤال ممتاز!\n\nكمساعد محاسبي متخصص، أنصحك بالتواصل مع أحد محاسبينا المعتمدين للحصول على استشارة مفصلة ودقيقة.\n\n📞 يمكنك:\n• طلب استشارة من خلال الموقع\n• الاتصال بنا مباشرة\n• تصفح خدماتنا المتخصصة\n\n💡 نحن هنا لمساعدتك في جميع احتياجاتك المحاسبية والضريبية!';
    }

    return '👋 مرحباً!\n\nأنا المساعد المحاسبي الذكي، متخصص في:\n• المحاسبة والضرائب الجزائرية\n• تأسيس وإدارة الشركات\n• القوائم المالية والتقارير\n• الاستشارات المالية\n\n❓ يمكنك سؤالي عن أي موضوع محاسبي أو ضريبي!';
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المساعد المحاسبي الذكي - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <!-- Navigation -->
    <?php include 'frontend/components/navbar.php'; ?>
    
    <div class="container mx-auto px-4 py-8">
        
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-earth-green rounded-full mb-4">
                <i class="fas fa-robot text-white text-3xl"></i>
            </div>
            <h1 class="text-4xl font-bold text-earth-green mb-4">المساعد المحاسبي الذكي</h1>
            <p class="text-gray-600 text-lg max-w-2xl mx-auto mb-4">
                احصل على إجابات فورية لأسئلتك المحاسبية والضريبية من الذكاء الاصطناعي المتخصص في القوانين الجزائرية.
            </p>
            <div class="flex items-center justify-center space-x-4 space-x-reverse text-sm text-gray-500">
                <div class="flex items-center">
                    <i class="fas fa-robot text-earth-green ml-1"></i>
                    <span>مدعوم بالذكاء الاصطناعي</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-clock text-earth-green ml-1"></i>
                    <span>متاح 24/7</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-shield-alt text-earth-green ml-1"></i>
                    <span>معلومات محدثة</span>
                </div>
                <div class="flex items-center">
                    <?php
                    $api_status = (defined('OPENROUTER_API_KEY') && !empty(OPENROUTER_API_KEY) && OPENROUTER_API_KEY !== 'your_openrouter_api_key_here');
                    if ($api_status): ?>
                        <i class="fas fa-check-circle text-green-500 ml-1"></i>
                        <span>API متصل</span>
                    <?php else: ?>
                        <i class="fas fa-exclamation-triangle text-yellow-500 ml-1"></i>
                        <span>وضع محلي</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Chat Container -->
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg">
                
                <!-- Chat Messages -->
                <div class="p-6 h-96 overflow-y-auto border-b border-gray-200">
                    
                    <!-- Welcome Message -->
                    <div class="flex items-start space-x-3 space-x-reverse mb-6">
                        <div class="w-10 h-10 bg-earth-green rounded-full flex items-center justify-center">
                            <i class="fas fa-robot text-white"></i>
                        </div>
                        <div class="bg-gray-100 rounded-lg p-4 max-w-md">
                            <p class="text-gray-800">مرحباً! أنا المساعد المحاسبي الذكي المدعوم بالذكاء الاصطناعي 🤖</p>
                            <p class="text-gray-600 text-sm mt-2">
                                متخصص في المحاسبة والضرائب الجزائرية • أجيب على أسئلتك فوراً • متاح 24/7
                            </p>
                            <p class="text-gray-800 mt-3">كيف يمكنني مساعدتك اليوم؟</p>
                        </div>
                    </div>
                    
                    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['message'])): ?>
                        <!-- User Message -->
                        <div class="flex items-start space-x-3 space-x-reverse mb-6 justify-end">
                            <div class="bg-earth-green text-white rounded-lg p-4 max-w-md">
                                <p><?php echo nl2br(htmlspecialchars($_POST['message'])); ?></p>
                            </div>
                            <div class="w-10 h-10 bg-sand-brown rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                        </div>
                        
                        <!-- AI Response -->
                        <?php if ($response): ?>
                            <div class="flex items-start space-x-3 space-x-reverse mb-6">
                                <div class="w-10 h-10 bg-earth-green rounded-full flex items-center justify-center">
                                    <i class="fas fa-robot text-white"></i>
                                </div>
                                <div class="bg-gray-100 rounded-lg p-4 max-w-md">
                                    <p class="text-gray-800 whitespace-pre-line"><?php echo htmlspecialchars($response); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                        
                        <!-- Error Message -->
                        <?php if ($error_message): ?>
                            <div class="flex items-start space-x-3 space-x-reverse mb-6">
                                <div class="w-10 h-10 bg-red-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-exclamation-triangle text-white"></i>
                                </div>
                                <div class="bg-red-100 border border-red-300 rounded-lg p-4 max-w-md">
                                    <p class="text-red-700"><?php echo htmlspecialchars($error_message); ?></p>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
                
                <!-- Chat Input -->
                <div class="p-6">
                    <form method="POST" class="flex items-end space-x-4 space-x-reverse">
                        <div class="flex-1">
                            <textarea 
                                name="message" 
                                id="messageInput"
                                placeholder="اكتب سؤالك هنا..."
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-earth-green focus:border-earth-green resize-none"
                                rows="3"
                                required></textarea>
                        </div>
                        <button
                            type="submit"
                            id="submitBtn"
                            class="bg-earth-green text-white px-6 py-3 rounded-lg hover:bg-sand-brown transition-colors flex items-center">
                            <span id="submitText">
                                <i class="fas fa-paper-plane ml-2"></i>
                                إرسال
                            </span>
                            <span id="loadingText" class="hidden">
                                <i class="fas fa-spinner fa-spin ml-2"></i>
                                جاري المعالجة...
                            </span>
                        </button>
                    </form>
                </div>
            </div>

            <!-- Quick Questions -->
            <div class="mt-8">
                <h3 class="text-xl font-semibold text-earth-green mb-4 text-center">أسئلة شائعة</h3>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">

                    <button onclick="askQuestion('كيف أحسب ضريبة القيمة المضافة في الجزائر؟')"
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-calculator text-earth-green ml-2"></i>
                        كيف أحسب ضريبة القيمة المضافة؟
                    </button>

                    <button onclick="askQuestion('ما هي أنواع الشركات في الجزائر؟')"
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-building text-earth-green ml-2"></i>
                        ما هي أنواع الشركات؟
                    </button>

                    <button onclick="askQuestion('كيف أسجل شركة جديدة؟')"
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-file-alt text-earth-green ml-2"></i>
                        كيف أسجل شركة جديدة؟
                    </button>

                    <button onclick="askQuestion('ما هي الضرائب المطلوبة؟')"
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-receipt text-earth-green ml-2"></i>
                        ما هي الضرائب المطلوبة؟
                    </button>

                    <button onclick="askQuestion('كيف أعد القوائم المالية؟')"
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-chart-line text-earth-green ml-2"></i>
                        كيف أعد القوائم المالية؟
                    </button>

                    <button onclick="askQuestion('كيف أختار محاسب مناسب؟')"
                            class="text-right p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                        <i class="fas fa-user-tie text-earth-green ml-2"></i>
                        كيف أختار محاسب مناسب؟
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <?php include 'frontend/components/footer.php'; ?>
    
    <!-- Scripts -->
    <script src="assets/js/main.js"></script>
    
    <script>
        // Ask predefined question
        function askQuestion(question) {
            document.getElementById('messageInput').value = question;
            document.getElementById('messageInput').focus();
        }

        // Auto-resize textarea
        document.getElementById('messageInput').addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 120) + 'px';
        });

        // Handle Enter key (Shift+Enter for new line)
        document.getElementById('messageInput').addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                document.querySelector('form').dispatchEvent(new Event('submit'));
            }
        });

        // Auto-scroll to bottom
        function scrollToBottom() {
            const chatContainer = document.querySelector('.overflow-y-auto');
            if (chatContainer) {
                chatContainer.scrollTop = chatContainer.scrollHeight;
            }
        }

        // Handle form submission with AJAX
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault(); // منع الإرسال العادي

            const submitBtn = document.getElementById('submitBtn');
            const submitText = document.getElementById('submitText');
            const loadingText = document.getElementById('loadingText');
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();

            if (!message) return;

            // Show loading state
            submitText.classList.add('hidden');
            loadingText.classList.remove('hidden');
            submitBtn.disabled = true;
            messageInput.disabled = true;

            // Add user message to chat immediately
            addUserMessage(message);
            messageInput.value = '';

            // Send AJAX request
            const formData = new FormData();
            formData.append('message', message);

            fetch('ai-assistant.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                // Extract AI response from HTML
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');

                // Find the AI response in the returned HTML
                const aiResponseElements = doc.querySelectorAll('.bg-gray-100 p');
                let aiResponse = '';

                if (aiResponseElements.length > 1) {
                    // Get the last AI response (newest one)
                    aiResponse = aiResponseElements[aiResponseElements.length - 1].textContent;
                } else {
                    aiResponse = 'عذراً، لم أتمكن من معالجة طلبك. يرجى المحاولة مرة أخرى.';
                }

                // Add AI response to chat
                addAIMessage(aiResponse);
            })
            .catch(error => {
                console.error('Error:', error);
                addAIMessage('عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.');
            })
            .finally(() => {
                // Reset form state
                submitText.classList.remove('hidden');
                loadingText.classList.add('hidden');
                submitBtn.disabled = false;
                messageInput.disabled = false;
                messageInput.focus();
            });
        });

        // Add user message to chat
        function addUserMessage(message) {
            const chatContainer = document.querySelector('.overflow-y-auto');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex items-start space-x-3 space-x-reverse mb-6 justify-end';
            messageDiv.innerHTML = `
                <div class="bg-earth-green text-white rounded-lg p-4 max-w-md">
                    <p>${escapeHtml(message)}</p>
                </div>
                <div class="w-10 h-10 bg-sand-brown rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-white"></i>
                </div>
            `;
            chatContainer.appendChild(messageDiv);
            scrollToBottom();
        }

        // Add AI message to chat
        function addAIMessage(response) {
            const chatContainer = document.querySelector('.overflow-y-auto');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex items-start space-x-3 space-x-reverse mb-6';
            messageDiv.innerHTML = `
                <div class="w-10 h-10 bg-earth-green rounded-full flex items-center justify-center">
                    <i class="fas fa-robot text-white"></i>
                </div>
                <div class="bg-gray-100 rounded-lg p-4 max-w-md">
                    <p class="text-gray-800 whitespace-pre-line">${escapeHtml(response)}</p>
                </div>
            `;
            chatContainer.appendChild(messageDiv);

            // Add highlight effect
            const responseElement = messageDiv.querySelector('.bg-gray-100');
            responseElement.style.backgroundColor = '#e8f5e8';
            setTimeout(() => {
                responseElement.style.backgroundColor = '';
            }, 2000);

            scrollToBottom();
        }

        // Escape HTML to prevent XSS
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML.replace(/\n/g, '<br>');
        }

        // Scroll to bottom on page load
        window.addEventListener('load', function() {
            scrollToBottom();
            // Focus on input field
            document.getElementById('messageInput').focus();
        });

        // Add some visual feedback for AI responses
        <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($response)): ?>
        // Highlight the AI response briefly
        setTimeout(function() {
            const aiResponses = document.querySelectorAll('.bg-gray-100');
            if (aiResponses.length > 1) {
                const lastResponse = aiResponses[aiResponses.length - 1];
                lastResponse.style.backgroundColor = '#e8f5e8';
                setTimeout(function() {
                    lastResponse.style.backgroundColor = '';
                }, 2000);
            }
        }, 500);
        <?php endif; ?>
    </script>
</body>
</html>
