<?php
/**
 * Payment Success Page
 * صفحة نجاح الدفع
 */

require_once '../backend/config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'client') {
    redirect(getUrl('login.php'));
}

// الحصول على معرف الطلب
$request_id = (int)($_GET['request_id'] ?? 0);

if (!$request_id) {
    showMessage('معرف الطلب مطلوب', 'error');
    redirect(getUrl('dashboard/client'));
}

// الحصول على تفاصيل الطلب والدفع
$request = null;
$payment = null;

try {
    $sql = "SELECT sr.*, s.service_name_ar, u.full_name as client_name 
            FROM service_requests sr 
            LEFT JOIN services s ON sr.service_id = s.id 
            LEFT JOIN users u ON sr.user_id = u.id 
            WHERE sr.id = :id AND sr.user_id = :user_id";
    
    $request = fetchOne($sql, ['id' => $request_id, 'user_id' => $user['id']]);
    
    if ($request) {
        // الحصول على تفاصيل الدفع
        $payment = fetchOne(
            "SELECT * FROM payments WHERE request_id = :request_id ORDER BY created_at DESC LIMIT 1",
            ['request_id' => $request_id]
        );
    }
} catch (Exception $e) {
    logError("Payment success page error: " . $e->getMessage());
}

if (!$request) {
    showMessage('الطلب غير موجود أو غير مصرح لك بالوصول إليه', 'error');
    redirect(getUrl('dashboard/client'));
}

if ($request['payment_status'] !== 'paid') {
    showMessage('لم يتم دفع هذا الطلب بعد', 'error');
    redirect(getUrl('payment/checkout.php?request_id=' . $request_id));
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تم الدفع بنجاح - <?php echo SITE_NAME; ?></title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
    
    <!-- Arabic Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'cairo': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        'muted-green': '#CAD2C5',
                        'warm-beige': '#FFE5B4',
                        'earth-green': '#52796F',
                        'sand-brown': '#8D7B68',
                        'light-gray': '#F0F0F0',
                    }
                }
            }
        }
    </script>
</head>
<body class="font-cairo bg-light-gray">
    
    <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-2xl w-full space-y-8">
            
            <!-- Success Animation -->
            <div class="text-center">
                <div class="w-24 h-24 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6 animate-pulse">
                    <i class="fas fa-check text-white text-4xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-green-600 mb-2">
                    تم الدفع بنجاح! 🎉
                </h1>
                <p class="text-gray-600 text-lg">
                    شكراً لك، تم استلام دفعتك وسيتم البدء في معالجة طلبك قريباً
                </p>
            </div>
            
            <!-- Payment Details -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-green-500 to-green-600 text-white p-6">
                    <h2 class="text-xl font-semibold flex items-center">
                        <i class="fas fa-receipt ml-2"></i>
                        تفاصيل الدفع
                        <?php if ($payment && $payment['payment_method'] === 'golden_card'): ?>
                            <span class="mr-auto flex items-center bg-yellow-500 text-yellow-900 px-3 py-1 rounded-full text-sm">
                                <i class="fas fa-star ml-1"></i>
                                البطاقة الذهبية
                            </span>
                        <?php endif; ?>
                    </h2>
                </div>
                
                <div class="p-6 space-y-4">
                    <div class="grid md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">رقم الطلب:</label>
                            <p class="text-lg font-semibold text-earth-green">#<?php echo $request['id']; ?></p>
                        </div>
                        
                        <?php if ($payment): ?>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">رقم المعاملة:</label>
                                <p class="text-lg font-semibold text-earth-green"><?php echo htmlspecialchars($payment['transaction_id']); ?></p>
                            </div>
                        <?php endif; ?>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">اسم الخدمة:</label>
                            <p class="text-gray-900"><?php echo htmlspecialchars($request['service_name_ar']); ?></p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">عنوان الطلب:</label>
                            <p class="text-gray-900"><?php echo htmlspecialchars($request['request_title']); ?></p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">المبلغ المدفوع:</label>
                            <p class="text-2xl font-bold text-green-600"><?php echo formatCurrency($request['total_amount'] * 1.19); ?></p>
                            <p class="text-sm text-gray-500">شامل ضريبة القيمة المضافة (19%)</p>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">تاريخ الدفع:</label>
                            <p class="text-gray-900"><?php echo date('Y-m-d H:i', strtotime($payment['created_at'] ?? 'now')); ?></p>
                        </div>
                        
                        <?php if ($payment): ?>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">طريقة الدفع:</label>
                                <p class="text-gray-900">
                                    <?php
                                    $payment_methods = [
                                        'golden_card' => 'البطاقة الذهبية',
                                        'credit_card' => 'بطاقة ائتمان',
                                        'bank_transfer' => 'تحويل بنكي',
                                        'paypal' => 'PayPal'
                                    ];
                                    echo $payment_methods[$payment['payment_method']] ?? $payment['payment_method'];
                                    ?>
                                </p>
                            </div>
                        <?php endif; ?>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">حالة الدفع:</label>
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                <i class="fas fa-check-circle ml-1"></i>
                                مكتمل
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Next Steps -->
            <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-earth-green mb-4">
                    <i class="fas fa-list-check ml-2"></i>
                    الخطوات التالية
                </h3>
                
                <div class="space-y-4">
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="w-8 h-8 bg-earth-green rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">1</span>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">تأكيد الطلب</h4>
                            <p class="text-sm text-gray-600">سيتم مراجعة طلبك وتأكيده خلال 24 ساعة</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="w-8 h-8 bg-earth-green rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">2</span>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">تعيين محاسب</h4>
                            <p class="text-sm text-gray-600">سيتم تعيين محاسب متخصص للعمل على طلبك</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="w-8 h-8 bg-earth-green rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">3</span>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">البدء في العمل</h4>
                            <p class="text-sm text-gray-600">سيبدأ المحاسب في العمل على طلبك وسيتواصل معك عند الحاجة</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start space-x-3 space-x-reverse">
                        <div class="w-8 h-8 bg-earth-green rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-bold">4</span>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900">تسليم النتائج</h4>
                            <p class="text-sm text-gray-600">ستحصل على النتائج النهائية حسب الجدول الزمني المتفق عليه</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Actions -->
            <div class="flex flex-col sm:flex-row gap-4">
                <a href="<?php echo getUrl('dashboard/client/request-details.php?id=' . $request_id); ?>" 
                   class="flex-1 bg-earth-green text-white px-6 py-3 rounded-lg font-semibold text-center hover:bg-sand-brown transition-all duration-300">
                    <i class="fas fa-eye ml-2"></i>
                    عرض تفاصيل الطلب
                </a>
                
                <a href="<?php echo getUrl('dashboard/client'); ?>" 
                   class="flex-1 bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold text-center hover:bg-gray-700 transition-all duration-300">
                    <i class="fas fa-home ml-2"></i>
                    العودة للوحة التحكم
                </a>
            </div>
            
            <!-- Support -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-start space-x-3 space-x-reverse">
                    <i class="fas fa-info-circle text-blue-600 mt-1"></i>
                    <div>
                        <h4 class="font-semibold text-blue-800 mb-2">تحتاج مساعدة؟</h4>
                        <p class="text-blue-700 text-sm mb-3">
                            فريق الدعم الفني متاح لمساعدتك في أي وقت
                        </p>
                        <div class="flex flex-col sm:flex-row gap-2 text-sm">
                            <a href="tel:<?php echo SITE_PHONE; ?>" 
                               class="text-blue-600 hover:text-blue-800 transition-colors">
                                <i class="fas fa-phone ml-1"></i>
                                <?php echo SITE_PHONE; ?>
                            </a>
                            <a href="mailto:<?php echo SITE_EMAIL; ?>" 
                               class="text-blue-600 hover:text-blue-800 transition-colors">
                                <i class="fas fa-envelope ml-1"></i>
                                <?php echo SITE_EMAIL; ?>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Print Receipt -->
            <div class="text-center">
                <button onclick="window.print()" 
                        class="text-gray-600 hover:text-earth-green transition-colors">
                    <i class="fas fa-print ml-2"></i>
                    طباعة الإيصال
                </button>
            </div>
        </div>
    </div>
    
    <!-- Scripts -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        // Auto-redirect after 30 seconds (optional)
        setTimeout(function() {
            if (confirm('هل تريد الانتقال إلى لوحة التحكم؟')) {
                window.location.href = '<?php echo getUrl("dashboard/client"); ?>';
            }
        }, 30000);
        
        // Confetti animation (optional)
        function createConfetti() {
            const colors = ['#52796F', '#CAD2C5', '#FFE5B4', '#8D7B68'];
            const confettiCount = 50;
            
            for (let i = 0; i < confettiCount; i++) {
                const confetti = document.createElement('div');
                confetti.style.position = 'fixed';
                confetti.style.width = '10px';
                confetti.style.height = '10px';
                confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
                confetti.style.left = Math.random() * 100 + 'vw';
                confetti.style.top = '-10px';
                confetti.style.zIndex = '9999';
                confetti.style.pointerEvents = 'none';
                confetti.style.borderRadius = '50%';
                
                document.body.appendChild(confetti);
                
                const animation = confetti.animate([
                    { transform: 'translateY(-10px) rotate(0deg)', opacity: 1 },
                    { transform: 'translateY(100vh) rotate(360deg)', opacity: 0 }
                ], {
                    duration: Math.random() * 2000 + 1000,
                    easing: 'ease-out'
                });
                
                animation.onfinish = () => confetti.remove();
            }
        }
        
        // Trigger confetti on page load
        window.addEventListener('load', function() {
            setTimeout(createConfetti, 500);
        });
    </script>
</body>
</html>
