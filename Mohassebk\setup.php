<?php
/**
 * محاسبك الرقمي - معالج الإعداد
 * Digital Accountant - Setup Wizard
 *
 * This file provides a beautiful UI for setting up the database and configuration
 *
 * @version 1.0.0
 * <AUTHOR> Team
 * @since 2025
 */

// Application Version
define('APP_VERSION', '1.0.0');
define('APP_BUILD', '2025.07.23');

// Start session for storing setup progress
session_start();

// Initialize setup steps
$steps = [
    1 => 'مرحباً بك',
    2 => 'متطلبات النظام',
    3 => 'إعداد قاعدة البيانات',
    4 => 'إعداد الموقع',
    5 => 'البيانات التجريبية',
    6 => 'إنهاء الإعداد'
];

$current_step = isset($_GET['step']) ? (int)$_GET['step'] : 1;
$current_step = max(1, min(6, $current_step));

// Handle form submissions
$message = '';
$error = '';

// التحقق من رسائل النجاح المحفوظة في الجلسة
if (isset($_SESSION['db_setup_success'])) {
    $message = $_SESSION['db_setup_success'];
    unset($_SESSION['db_setup_success']);
}

if (isset($_SESSION['site_setup_success'])) {
    $message = $_SESSION['site_setup_success'];
    unset($_SESSION['site_setup_success']);
}

if (isset($_SESSION['seed_setup_message'])) {
    $message = $_SESSION['seed_setup_message'];
    unset($_SESSION['seed_setup_message']);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($current_step) {
        case 3: // Database setup
            $db_host = $_POST['db_host'] ?? 'localhost';
            $db_port = $_POST['db_port'] ?? '3306';
            $db_name = $_POST['db_name'] ?? 'mohassebk';
            $db_user = $_POST['db_user'] ?? 'root';
            $db_pass = $_POST['db_pass'] ?? '';

            // Validate port number
            if (!is_numeric($db_port) || $db_port < 1 || $db_port > 65535) {
                $error = 'رقم المنفذ يجب أن يكون بين 1 و 65535';
                break;
            }

            // Validate database name
            if (empty($db_name) || !preg_match('/^[a-zA-Z0-9_]+$/', $db_name)) {
                $error = 'اسم قاعدة البيانات يجب أن يحتوي على أحرف وأرقام و _ فقط';
                break;
            }

            // Build connection string with port
            $dsn = "mysql:host=$db_host";
            if (!empty($db_port) && $db_port != '3306') {
                $dsn .= ";port=$db_port";
            }

            // Test database connection
            try {
                $pdo = new PDO($dsn, $db_user, $db_pass);
                $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                
                // Read and execute SQL file
                $sql_file = __DIR__ . '/database/mohassebk_db.sql';
                if (file_exists($sql_file)) {
                    $sql = file_get_contents($sql_file);

                    // Replace database name in SQL with user input
                    $sql = str_replace('mohassebk_db', $db_name, $sql);

                    $pdo->exec($sql);
                    
                    // Store database config in session
                    $_SESSION['db_config'] = [
                        'host' => $db_host,
                        'port' => $db_port,
                        'name' => $db_name,
                        'user' => $db_user,
                        'pass' => $db_pass
                    ];
                    
                    $_SESSION['db_setup_success'] = "تم إنشاء قاعدة البيانات '$db_name' بنجاح!";
                    header('Location: ?step=4');
                    exit;
                } else {
                    $error = 'ملف قاعدة البيانات غير موجود!';
                }
            } catch (PDOException $e) {
                $error = 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage();
            }
            break;
            
        case 4: // Site configuration
            $site_name = $_POST['site_name'] ?? 'المحاسبك الرقمي';
            $site_email = $_POST['site_email'] ?? '<EMAIL>';
            $site_phone = $_POST['site_phone'] ?? '+213123456789';
            $admin_email = $_POST['admin_email'] ?? '<EMAIL>';
            $admin_password = $_POST['admin_password'] ?? '';
            
            // التحقق من صحة البيانات
            if (empty($admin_password)) {
                $error = 'يرجى إدخال كلمة مرور المدير!';
            } elseif (!isset($_SESSION['db_config'])) {
                $error = 'لم يتم العثور على إعدادات قاعدة البيانات! يرجى العودة للخطوة السابقة.';
            } elseif (strlen($admin_password) < 6) {
                $error = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل!';
            } elseif (!filter_var($admin_email, FILTER_VALIDATE_EMAIL)) {
                $error = 'البريد الإلكتروني للمدير غير صحيح!';
            } elseif (!filter_var($site_email, FILTER_VALIDATE_EMAIL)) {
                $error = 'البريد الإلكتروني للموقع غير صحيح!';
            } else {
                try {
                    // Create config.php file
                    $config_content = generateConfigFile($_SESSION['db_config'], [
                        'site_name' => $site_name,
                        'site_email' => $site_email,
                        'site_phone' => $site_phone
                    ]);

                    if (file_put_contents(__DIR__ . '/config.php', $config_content)) {
                        // Update admin user in database
                        try {
                            $db = $_SESSION['db_config'];
                            $dsn = "mysql:host={$db['host']}";
                            if (!empty($db['port']) && $db['port'] != '3306') {
                                $dsn .= ";port={$db['port']}";
                            }
                            $dsn .= ";dbname={$db['name']}";
                            $pdo = new PDO($dsn, $db['user'], $db['pass']);
                            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                            $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
                            $stmt = $pdo->prepare("UPDATE users SET email = ?, password = ? WHERE username = 'admin'");
                            $result = $stmt->execute([$admin_email, $hashed_password]);

                            if ($result) {
                                // حفظ بيانات المدير في الجلسة
                                $_SESSION['admin_email'] = $admin_email;
                                $_SESSION['site_configured'] = true;

                                $_SESSION['site_setup_success'] = 'تم إعداد الموقع بنجاح!';
                                header('Location: ?step=5');
                                exit;
                            } else {
                                $error = 'فشل في تحديث بيانات المدير!';
                            }
                        } catch (PDOException $e) {
                            $error = 'خطأ في تحديث بيانات المدير: ' . $e->getMessage();
                        }
                    } else {
                        $error = 'فشل في إنشاء ملف الإعدادات! تأكد من صلاحيات الكتابة.';
                    }
                } catch (Exception $e) {
                    $error = 'حدث خطأ غير متوقع: ' . $e->getMessage();
                }
            }
            break;

        case 5: // Test data seeding
            $seed_data = isset($_POST['seed_data']) ? $_POST['seed_data'] : 'no';

            if ($seed_data === 'yes') {
                try {
                    // Include the seeder file
                    require_once __DIR__ . '/database/seed_data.php';

                    // Create seeder instance and run
                    $db = $_SESSION['db_config'];
                    $dsn = "mysql:host={$db['host']}";
                    if (!empty($db['port']) && $db['port'] != '3306') {
                        $dsn .= ";port={$db['port']}";
                    }
                    $dsn .= ";dbname={$db['name']}";
                    $pdo = new PDO($dsn, $db['user'], $db['pass']);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

                    $seeder = new DataSeeder($pdo);
                    $seeder->run();

                    $_SESSION['seed_data_added'] = true;
                    $message = 'تم إضافة البيانات التجريبية بنجاح!';
                } catch (Exception $e) {
                    $error = 'خطأ في إضافة البيانات التجريبية: ' . $e->getMessage();
                }
            } else {
                $_SESSION['seed_data_added'] = false;
                $message = 'تم تخطي إضافة البيانات التجريبية.';
            }

            $_SESSION['seed_setup_message'] = $message;
            header('Location: ?step=6');
            exit;
    }
}

function generateConfigFile($db_config, $site_config) {
    return "<?php
/**
 * ملف إعدادات محاسبك الرقمي
 * Digital Accountant Configuration File
 * Generated on: " . date('Y-m-d H:i:s') . "
 * Version: " . APP_VERSION . "
 * Build: " . APP_BUILD . "
 */

// Application Information
define('APP_VERSION', '" . APP_VERSION . "');
define('APP_BUILD', '" . APP_BUILD . "');
define('APP_NAME', 'محاسبك الرقمي');
define('APP_NAME_EN', 'Digital Accountant');

// Database Configuration
define('DB_HOST', '" . $db_config['host'] . "');
define('DB_PORT', '" . $db_config['port'] . "');
define('DB_NAME', '" . $db_config['name'] . "');
define('DB_USER', '" . $db_config['user'] . "');
define('DB_PASS', '" . $db_config['pass'] . "');

// Site Configuration
define('SITE_NAME', '" . $site_config['site_name'] . "');
define('SITE_EMAIL', '" . $site_config['site_email'] . "');
define('SITE_PHONE', '" . $site_config['site_phone'] . "');
define('SITE_URL', 'http://' . \$_SERVER['HTTP_HOST'] . dirname(\$_SERVER['SCRIPT_NAME']));

// Security
define('SECRET_KEY', '" . bin2hex(random_bytes(32)) . "');

// Timezone
date_default_timezone_set('Africa/Algiers');

// Error Reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Database Connection Function
function getDBConnection() {
    try {
        \$dsn = 'mysql:host=' . DB_HOST;
        if (defined('DB_PORT') && DB_PORT != '3306') {
            \$dsn .= ';port=' . DB_PORT;
        }
        \$dsn .= ';dbname=' . DB_NAME . ';charset=utf8mb4';

        \$pdo = new PDO(\$dsn, DB_USER, DB_PASS);
        \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return \$pdo;
    } catch (PDOException \$e) {
        die('Database connection failed: ' . \$e->getMessage());
    }
}

// Get application version info
function getAppVersion() {
    return [
        'version' => APP_VERSION,
        'build' => APP_BUILD,
        'name' => APP_NAME,
        'name_en' => APP_NAME_EN
    ];
}
?>";
}

// Check if setup is already completed
if (file_exists(__DIR__ . '/config.php') && $current_step === 1) {
    $setup_completed = true;
}
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد محاسبك الرقمي v<?php echo APP_VERSION; ?> - Setup Wizard</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #52796F;
            --secondary-color: #CAD2C5;
            --accent-color: #FFE5B4;
            --success-color: #28a745;
            --error-color: #dc3545;
            --warning-color: #ffc107;
            --text-dark: #333333;
            --text-light: #666666;
            --bg-light: #F0F0F0;
            --white: #FFFFFF;
            --shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            direction: rtl;
            text-align: right;
        }

        .setup-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .setup-card {
            background: var(--white);
            border-radius: 20px;
            box-shadow: var(--shadow);
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .setup-header {
            background: var(--primary-color);
            color: var(--white);
            padding: 30px;
            text-align: center;
        }

        .setup-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .setup-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.2);
            height: 8px;
            border-radius: 4px;
            margin-top: 20px;
            overflow: hidden;
        }

        .progress-fill {
            background: var(--accent-color);
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
            width: <?= ($current_step / 5) * 100 ?>%;
        }

        .steps-nav {
            display: flex;
            justify-content: space-between;
            padding: 20px 30px;
            background: var(--bg-light);
            border-bottom: 1px solid #ddd;
        }

        .step {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
            color: var(--text-light);
            transition: color 0.3s ease;
        }

        .step.active {
            color: var(--primary-color);
            font-weight: 600;
        }

        .step.completed {
            color: var(--success-color);
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #ddd;
            color: var(--white);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .step.active .step-number {
            background: var(--primary-color);
        }

        .step.completed .step-number {
            background: var(--success-color);
        }

        .setup-content {
            padding: 40px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-dark);
        }

        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1rem;
            font-family: inherit;
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .form-help {
            display: block;
            margin-top: 5px;
            font-size: 0.85rem;
            color: var(--text-light);
            font-style: italic;
        }

        .port-btn {
            background: var(--secondary-color);
            border: none;
            padding: 4px 8px;
            margin: 0 4px;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .port-btn:hover {
            background: var(--primary-color);
            color: white;
        }

        .setup-footer {
            margin-top: 20px;
        }

        .setup-footer span {
            white-space: nowrap;
        }

        @media (max-width: 768px) {
            .setup-footer div:last-child {
                flex-direction: column;
                gap: 10px !important;
            }
        }

        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: var(--primary-color);
            color: var(--white);
        }

        .btn-primary:hover {
            background: #3d5a50;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: var(--secondary-color);
            color: var(--text-dark);
        }

        .btn-secondary:hover {
            background: #b8c4b8;
        }

        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border-color: var(--success-color);
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border-color: var(--error-color);
        }

        .requirements-list {
            list-style: none;
            padding: 0;
        }

        .requirements-list li {
            padding: 10px 0;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .requirement-status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-ok {
            background: var(--success-color);
            color: var(--white);
        }

        .status-error {
            background: var(--error-color);
            color: var(--white);
        }

        .welcome-content {
            text-align: center;
            padding: 40px 0;
        }

        .welcome-icon {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 20px;
        }

        .setup-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        @media (max-width: 768px) {
            .setup-container {
                padding: 10px;
            }
            
            .setup-header h1 {
                font-size: 2rem;
            }
            
            .steps-nav {
                flex-direction: column;
                gap: 10px;
            }
            
            .setup-actions {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <!-- Header -->
            <div class="setup-header">
                <h1><i class="fas fa-calculator"></i> محاسبك الرقمي</h1>
                <p>معالج إعداد النظام - Setup Wizard</p>
                <div class="version-info" style="margin: 10px 0; font-size: 0.9rem; opacity: 0.8;">
                    <i class="fas fa-code-branch"></i> الإصدار <?php echo APP_VERSION; ?>
                    <span style="margin: 0 10px;">|</span>
                    <i class="fas fa-calendar"></i> <?php echo APP_BUILD; ?>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>

            <!-- Steps Navigation -->
            <div class="steps-nav">
                <?php foreach ($steps as $step_num => $step_name): ?>
                    <div class="step <?= $step_num === $current_step ? 'active' : ($step_num < $current_step ? 'completed' : '') ?>">
                        <div class="step-number">
                            <?= $step_num < $current_step ? '<i class="fas fa-check"></i>' : $step_num ?>
                        </div>
                        <span><?= $step_name ?></span>
                    </div>
                <?php endforeach; ?>
            </div>

            <!-- Content -->
            <div class="setup-content">
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?= htmlspecialchars($message) ?>
                        <?php if ($current_step < 6): ?>
                            <br><small style="margin-top: 10px; display: block;">
                                <i class="fas fa-info-circle"></i> سيتم الانتقال تلقائياً للخطوة التالية خلال 3 ثوانٍ، أو يمكنك النقر على "التالي"
                            </small>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error) ?>
                        <?php if ($current_step === 4): ?>
                            <div style="margin-top: 10px; font-size: 0.9em;">
                                <strong>نصائح لحل المشكلة:</strong>
                                <ul style="margin: 10px 0; padding-right: 20px;">
                                    <li>تأكد من أن كلمة المرور تحتوي على 6 أحرف على الأقل</li>
                                    <li>تأكد من صحة عناوين البريد الإلكتروني</li>
                                    <li>تأكد من أن المجلد قابل للكتابة</li>
                                    <li>تأكد من إتمام الخطوة السابقة بنجاح</li>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($setup_completed) && $setup_completed): ?>
                    <!-- Setup Already Completed -->
                    <div class="welcome-content">
                        <div class="welcome-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h2>تم إعداد النظام مسبقاً!</h2>
                        <p>يبدو أن النظام تم إعداده مسبقاً. يمكنك الآن الوصول إلى الموقع.</p>
                        <div style="margin-top: 30px;">
                            <a href="index.php" class="btn btn-primary">
                                <i class="fas fa-home"></i> الذهاب إلى الموقع
                            </a>
                            <a href="?step=1&force=1" class="btn btn-secondary">
                                <i class="fas fa-redo"></i> إعادة الإعداد
                            </a>
                        </div>
                    </div>

                <?php elseif ($current_step === 1): ?>
                    <!-- Welcome Step -->
                    <div class="welcome-content">
                        <div class="welcome-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h2>مرحباً بك في محاسبك الرقمي!</h2>
                        <p>سيساعدك هذا المعالج على إعداد النظام خطوة بخطوة. العملية بسيطة وستستغرق بضع دقائق فقط.</p>

                        <div class="version-card" style="background: var(--secondary-color); padding: 15px; border-radius: 10px; margin: 20px 0; display: inline-block;">
                            <div style="display: flex; align-items: center; justify-content: center; gap: 15px;">
                                <div>
                                    <i class="fas fa-code-branch" style="color: var(--primary-color);"></i>
                                    <strong>الإصدار:</strong> <?php echo APP_VERSION; ?>
                                </div>
                                <div>
                                    <i class="fas fa-calendar" style="color: var(--primary-color);"></i>
                                    <strong>التاريخ:</strong> <?php echo APP_BUILD; ?>
                                </div>
                            </div>
                        </div>

                        <div style="margin: 30px 0;">
                            <h3>ما ستحتاجه:</h3>
                            <ul style="text-align: right; margin: 20px 0;">
                                <li>✅ خادم ويب يدعم PHP 7.4 أو أحدث</li>
                                <li>✅ قاعدة بيانات MySQL 5.7 أو أحدث</li>
                                <li>✅ بيانات الاتصال بقاعدة البيانات</li>
                                <li>✅ عنوان بريد إلكتروني للمدير</li>
                            </ul>

                            <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin: 20px 0;">
                                <h4>💡 نصيحة:</h4>
                                <p>إذا كنت تواجه مشاكل في الإعداد، يمكنك اختبار النظام أولاً للتأكد من أن جميع المتطلبات متوفرة.</p>
                                <a href="test-setup.php" target="_blank"
                                   style="display: inline-block; background: var(--warning-color); color: white; padding: 8px 16px; text-decoration: none; border-radius: 5px; font-size: 0.9em; margin-top: 10px;">
                                    <i class="fas fa-flask"></i> اختبار النظام
                                </a>
                            </div>
                        </div>
                    </div>

                <?php elseif ($current_step === 2): ?>
                    <!-- System Requirements -->
                    <h2><i class="fas fa-cogs"></i> فحص متطلبات النظام</h2>
                    <p>يتم فحص متطلبات النظام للتأكد من توافق الخادم...</p>

                    <ul class="requirements-list">
                        <li>
                            <span>إصدار PHP (7.4 أو أحدث)</span>
                            <span class="requirement-status <?= version_compare(PHP_VERSION, '7.4.0', '>=') ? 'status-ok' : 'status-error' ?>">
                                <?= PHP_VERSION ?>
                            </span>
                        </li>
                        <li>
                            <span>امتداد PDO MySQL</span>
                            <span class="requirement-status <?= extension_loaded('pdo_mysql') ? 'status-ok' : 'status-error' ?>">
                                <?= extension_loaded('pdo_mysql') ? 'متوفر' : 'غير متوفر' ?>
                            </span>
                        </li>
                        <li>
                            <span>امتداد JSON</span>
                            <span class="requirement-status <?= extension_loaded('json') ? 'status-ok' : 'status-error' ?>">
                                <?= extension_loaded('json') ? 'متوفر' : 'غير متوفر' ?>
                            </span>
                        </li>
                        <li>
                            <span>امتداد cURL</span>
                            <span class="requirement-status <?= extension_loaded('curl') ? 'status-ok' : 'status-error' ?>">
                                <?= extension_loaded('curl') ? 'متوفر' : 'غير متوفر' ?>
                            </span>
                        </li>
                        <li>
                            <span>صلاحيات الكتابة</span>
                            <span class="requirement-status <?= is_writable(__DIR__) ? 'status-ok' : 'status-error' ?>">
                                <?= is_writable(__DIR__) ? 'متوفرة' : 'غير متوفرة' ?>
                            </span>
                        </li>
                    </ul>

                <?php elseif ($current_step === 3): ?>
                    <!-- Database Setup -->
                    <h2><i class="fas fa-database"></i> إعداد قاعدة البيانات</h2>
                    <p>يرجى إدخال بيانات الاتصال بقاعدة البيانات:</p>

                    <form method="POST">
                        <div class="form-group">
                            <label class="form-label">عنوان الخادم:</label>
                            <input type="text" name="db_host" class="form-input" value="localhost" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                منفذ قاعدة البيانات:
                                <i class="fas fa-info-circle" title="المنفذ الذي يعمل عليه خادم قاعدة البيانات" style="color: var(--primary-color); margin-right: 5px;"></i>
                            </label>
                            <input type="number" name="db_port" class="form-input" value="3306" min="1" max="65535" required>
                            <small class="form-help">
                                المنافذ الشائعة: MySQL/MariaDB (3306)، PostgreSQL (5432)، SQL Server (1433)
                                <br>إذا كنت تستخدم XAMPP أو WAMP، فالمنفذ عادة 3306.
                            </small>
                            <div class="port-suggestions" style="margin-top: 8px;">
                                <small style="color: var(--text-light);">منافذ سريعة:</small>
                                <button type="button" onclick="setPort(3306)" class="port-btn">3306</button>
                                <button type="button" onclick="setPort(3307)" class="port-btn">3307</button>
                                <button type="button" onclick="setPort(5432)" class="port-btn">5432</button>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">اسم قاعدة البيانات:</label>
                            <input type="text" name="db_name" class="form-input" value="mohassebk" required
                                   oninput="updateDbPreview(this.value)">
                            <small class="form-help">
                                يجب أن يحتوي على أحرف إنجليزية وأرقام و _ فقط (مثال: mohassebk, accounting_db)
                                <br><span id="db-preview" style="color: var(--primary-color); font-weight: bold;">
                                    سيتم إنشاء قاعدة البيانات: mohassebk
                                </span>
                            </small>
                        </div>

                        <div class="form-group">
                            <label class="form-label">اسم المستخدم:</label>
                            <input type="text" name="db_user" class="form-input" value="root" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">كلمة المرور:</label>
                            <input type="password" name="db_pass" class="form-input" placeholder="اتركه فارغاً إذا لم تكن هناك كلمة مرور">
                        </div>

                        <div class="setup-actions">
                            <a href="?step=2" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> السابق
                            </a>
                            <?php if ($message && strpos($message, 'تم إنشاء قاعدة البيانات') !== false): ?>
                                <a href="?step=4" class="btn btn-primary">
                                    <i class="fas fa-arrow-left"></i> التالي
                                </a>
                            <?php else: ?>
                                <button type="submit" class="btn btn-primary" onclick="return confirmDatabaseCreation()">
                                    <i class="fas fa-database"></i> إنشاء قاعدة البيانات
                                </button>
                            <?php endif; ?>
                        </div>
                    </form>

                <?php elseif ($current_step === 4): ?>
                    <!-- Site Configuration -->
                    <h2><i class="fas fa-cog"></i> إعداد الموقع</h2>
                    <p>يرجى إدخال المعلومات الأساسية للموقع:</p>

                    <form method="POST">
                        <div class="form-group">
                            <label class="form-label">اسم الموقع:</label>
                            <input type="text" name="site_name" class="form-input" value="المحاسبك الرقمي" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني للموقع:</label>
                            <input type="email" name="site_email" class="form-input" value="<EMAIL>" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">رقم الهاتف:</label>
                            <input type="text" name="site_phone" class="form-input" value="+213123456789" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">البريد الإلكتروني للمدير:</label>
                            <input type="email" name="admin_email" class="form-input" value="<EMAIL>" required>
                        </div>

                        <div class="form-group">
                            <label class="form-label">كلمة مرور المدير:</label>
                            <input type="password" name="admin_password" class="form-input" placeholder="أدخل كلمة مرور قوية (6 أحرف على الأقل)" required minlength="6">
                            <small style="color: var(--text-light); font-size: 0.85em; margin-top: 5px; display: block;">
                                يجب أن تكون كلمة المرور 6 أحرف على الأقل وتحتوي على أحرف وأرقام
                            </small>
                        </div>

                        <div class="setup-actions">
                            <a href="?step=3" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> السابق
                            </a>
                            <?php if ($message && strpos($message, 'تم إعداد الموقع') !== false): ?>
                                <a href="?step=5" class="btn btn-primary">
                                    <i class="fas fa-arrow-left"></i> التالي
                                </a>
                            <?php else: ?>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ الإعدادات
                                </button>
                            <?php endif; ?>
                        </div>

                        <?php if ($error): ?>
                            <div style="margin-top: 20px; text-align: center;">
                                <p style="color: var(--text-light); font-size: 0.9em;">
                                    إذا استمرت المشكلة، يمكنك اختبار النظام للتأكد من المتطلبات
                                </p>
                                <a href="test-setup.php" target="_blank"
                                   class="btn btn-secondary" style="margin-top: 10px;">
                                    <i class="fas fa-flask"></i> اختبار النظام
                                </a>
                            </div>
                        <?php endif; ?>
                    </form>

                <?php elseif ($current_step === 5): ?>
                    <!-- Test Data Seeding -->
                    <div class="welcome-content">
                        <div class="welcome-icon">
                            <i class="fas fa-database" style="color: var(--primary-color);"></i>
                        </div>
                        <h2>إضافة البيانات التجريبية</h2>
                        <p>هل تريد إضافة بيانات تجريبية لاختبار النظام؟</p>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; text-align: right;">
                            <h4>البيانات التجريبية تشمل:</h4>
                            <ul style="margin: 15px 0;">
                                <li>✅ مستخدمين تجريبيين (عملاء ومحاسبين)</li>
                                <li>✅ خدمات محاسبية متنوعة</li>
                                <li>✅ طلبات خدمات عشوائية</li>
                                <li>✅ إشعارات ومدفوعات</li>
                                <li>✅ محادثات الذكاء الاصطناعي</li>
                                <li>✅ إعدادات النظام</li>
                            </ul>

                            <div style="background: #fff3cd; padding: 15px; border-radius: 8px; margin-top: 15px;">
                                <strong>ملاحظة:</strong> البيانات التجريبية مفيدة للاختبار والتطوير فقط.
                            </div>
                        </div>
                    </div>

                    <form method="POST">
                        <div class="form-group">
                            <label class="form-label">إضافة البيانات التجريبية:</label>
                            <div style="margin: 15px 0;">
                                <label style="display: flex; align-items: flex-start; margin-bottom: 15px; cursor: pointer; padding: 15px; border: 2px solid #e9ecef; border-radius: 10px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='var(--primary-color)'" onmouseout="this.style.borderColor='#e9ecef'">
                                    <input type="radio" name="seed_data" value="yes" style="margin-left: 10px; margin-top: 5px;">
                                    <div>
                                        <span style="font-weight: 600; color: var(--primary-color);">نعم، أضف البيانات التجريبية (مُوصى به للتطوير)</span>
                                        <div style="font-size: 0.9em; color: var(--text-light); margin-top: 5px;">
                                            سيتم إضافة: 18 مستخدم، 6 خدمات، 30 طلب، 50 إشعار، مدفوعات، ومحادثات ذكاء اصطناعي
                                        </div>
                                    </div>
                                </label>
                                <label style="display: flex; align-items: flex-start; cursor: pointer; padding: 15px; border: 2px solid #e9ecef; border-radius: 10px; transition: all 0.3s ease;" onmouseover="this.style.borderColor='var(--primary-color)'" onmouseout="this.style.borderColor='#e9ecef'">
                                    <input type="radio" name="seed_data" value="no" checked style="margin-left: 10px; margin-top: 5px;">
                                    <div>
                                        <span style="font-weight: 600;">لا، ابدأ بقاعدة بيانات فارغة</span>
                                        <div style="font-size: 0.9em; color: var(--text-light); margin-top: 5px;">
                                            مناسب للاستخدام الفعلي في الإنتاج
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <div class="setup-actions">
                            <a href="?step=4" class="btn btn-secondary">
                                <i class="fas fa-arrow-right"></i> السابق
                            </a>
                            <?php if ($message): ?>
                                <a href="?step=6" class="btn btn-primary">
                                    <i class="fas fa-arrow-left"></i> إنهاء الإعداد
                                </a>
                            <?php else: ?>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-arrow-left"></i> التالي
                                </button>
                            <?php endif; ?>
                        </div>
                    </form>

                <?php elseif ($current_step === 6): ?>
                    <!-- Setup Complete -->
                    <div class="welcome-content">
                        <div class="welcome-icon">
                            <i class="fas fa-check-circle" style="color: var(--success-color);"></i>
                        </div>
                        <h2>تم إنهاء الإعداد بنجاح! 🎉</h2>
                        <p>تم إعداد نظام محاسبك الرقمي بنجاح. يمكنك الآن البدء في استخدام النظام.</p>

                        <div class="success-info" style="background: var(--success-color); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
                            <h3 style="margin-bottom: 15px;"><i class="fas fa-info-circle"></i> معلومات النظام</h3>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; text-align: right;">
                                <div>
                                    <strong><i class="fas fa-tag"></i> الإصدار:</strong><br>
                                    <?php echo APP_VERSION; ?>
                                </div>
                                <div>
                                    <strong><i class="fas fa-calendar"></i> تاريخ البناء:</strong><br>
                                    <?php echo APP_BUILD; ?>
                                </div>
                                <div>
                                    <strong><i class="fas fa-server"></i> قاعدة البيانات:</strong><br>
                                    <?php echo $_SESSION['db_config']['name'] ?? 'mohassebk'; ?>
                                </div>
                                <div>
                                    <strong><i class="fas fa-clock"></i> تاريخ الإعداد:</strong><br>
                                    <?php echo date('Y-m-d H:i:s'); ?>
                                </div>
                            </div>
                        </div>

                        <div style="margin: 30px 0; text-align: right;">
                            <h3>معلومات مهمة:</h3>
                            <ul style="margin: 20px 0;">
                                <li>✅ تم إنشاء قاعدة البيانات وجداولها</li>
                                <li>✅ تم إنشاء ملف الإعدادات (config.php)</li>
                                <li>✅ تم إنشاء حساب المدير</li>
                                <li>✅ تم إدراج البيانات الأساسية</li>
                                <?php if (isset($_SESSION['seed_data_added']) && $_SESSION['seed_data_added']): ?>
                                <li>✅ تم إضافة البيانات التجريبية</li>
                                <?php endif; ?>
                            </ul>

                            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
                                <h4>بيانات تسجيل الدخول:</h4>
                                <p><strong>البريد الإلكتروني:</strong> <?= htmlspecialchars($_SESSION['admin_email'] ?? '<EMAIL>') ?></p>
                                <p><strong>كلمة المرور:</strong> التي قمت بإدخالها في الخطوة السابقة</p>
                            </div>
                        </div>

                        <div style="margin-top: 30px;">
                            <a href="index.php" class="btn btn-primary">
                                <i class="fas fa-home"></i> الذهاب إلى الموقع
                            </a>
                            <a href="admin/" class="btn btn-secondary">
                                <i class="fas fa-user-shield"></i> لوحة الإدارة
                            </a>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Navigation for steps 1 and 2 -->
                <?php if ($current_step === 1): ?>
                    <div class="setup-actions">
                        <div></div>
                        <a href="?step=2" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> التالي
                        </a>
                    </div>
                <?php elseif ($current_step === 2): ?>
                    <div class="setup-actions">
                        <a href="?step=1" class="btn btn-secondary">
                            <i class="fas fa-arrow-right"></i> السابق
                        </a>
                        <?php
                        $can_continue = version_compare(PHP_VERSION, '7.4.0', '>=') &&
                                       extension_loaded('pdo_mysql') &&
                                       extension_loaded('json') &&
                                       is_writable(__DIR__);
                        ?>
                        <a href="?step=3" class="btn btn-primary <?= $can_continue ? '' : 'disabled' ?>">
                            <i class="fas fa-arrow-left"></i> التالي
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Footer with version info -->
        <div class="setup-footer" style="text-align: center; padding: 20px; color: rgba(255,255,255,0.8); font-size: 0.9rem;">
            <div style="margin-bottom: 10px;">
                <i class="fas fa-calculator"></i> محاسبك الرقمي - Digital Accountant
            </div>
            <div style="display: flex; justify-content: center; align-items: center; gap: 20px; flex-wrap: wrap;">
                <span><i class="fas fa-code-branch"></i> الإصدار <?php echo APP_VERSION; ?></span>
                <span><i class="fas fa-calendar"></i> <?php echo APP_BUILD; ?></span>
                <span><i class="fas fa-copyright"></i> <?php echo date('Y'); ?> جميع الحقوق محفوظة</span>
            </div>
        </div>
    </div>

    <script>
        // Add some interactive effects
        document.addEventListener('DOMContentLoaded', function() {
            // Animate progress bar
            const progressFill = document.querySelector('.progress-fill');
            if (progressFill) {
                setTimeout(() => {
                    progressFill.style.width = '<?= ($current_step / 6) * 100 ?>%';
                }, 300);
            }

            // Form validation
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const requiredFields = form.querySelectorAll('[required]');
                    let isValid = true;
                    let errorMessage = '';

                    requiredFields.forEach(field => {
                        if (!field.value.trim()) {
                            field.style.borderColor = 'var(--error-color)';
                            isValid = false;
                        } else {
                            field.style.borderColor = '#ddd';

                            // Additional validation for specific fields
                            if (field.type === 'email' && !isValidEmail(field.value)) {
                                field.style.borderColor = 'var(--error-color)';
                                isValid = false;
                                errorMessage = 'البريد الإلكتروني غير صحيح';
                            }

                            if (field.type === 'password' && field.value.length < 6) {
                                field.style.borderColor = 'var(--error-color)';
                                isValid = false;
                                errorMessage = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                            }

                            if (field.name === 'db_port') {
                                const port = parseInt(field.value);
                                if (isNaN(port) || port < 1 || port > 65535) {
                                    field.style.borderColor = 'var(--error-color)';
                                    isValid = false;
                                    errorMessage = 'رقم المنفذ يجب أن يكون بين 1 و 65535';
                                }
                            }

                            if (field.name === 'db_name') {
                                const dbNameRegex = /^[a-zA-Z0-9_]+$/;
                                if (!dbNameRegex.test(field.value)) {
                                    field.style.borderColor = 'var(--error-color)';
                                    isValid = false;
                                    errorMessage = 'اسم قاعدة البيانات يجب أن يحتوي على أحرف وأرقام و _ فقط';
                                }
                            }
                        }
                    });

                    if (!isValid) {
                        e.preventDefault();
                        alert(errorMessage || 'يرجى ملء جميع الحقول المطلوبة بشكل صحيح');
                    }
                });
            });

            // Email validation function
            function isValidEmail(email) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return emailRegex.test(email);
            }
        });

        // Port selection function
        function setPort(port) {
            const portInput = document.querySelector('input[name="db_port"]');
            if (portInput) {
                portInput.value = port;
                portInput.style.borderColor = '#ddd';

                // Add visual feedback
                const portBtns = document.querySelectorAll('.port-btn');
                portBtns.forEach(btn => btn.style.background = 'var(--secondary-color)');
                event.target.style.background = 'var(--primary-color)';
                event.target.style.color = 'white';
            }
        }

        // Database name preview function
        function updateDbPreview(dbName) {
            const preview = document.getElementById('db-preview');
            if (preview) {
                if (dbName.trim()) {
                    preview.textContent = `سيتم إنشاء قاعدة البيانات: ${dbName}`;
                    preview.style.color = 'var(--primary-color)';
                } else {
                    preview.textContent = 'يرجى إدخال اسم قاعدة البيانات';
                    preview.style.color = 'var(--error-color)';
                }
            }
        }

        // Confirm database creation
        function confirmDatabaseCreation() {
            const dbName = document.querySelector('input[name="db_name"]').value;
            const dbHost = document.querySelector('input[name="db_host"]').value;
            const dbPort = document.querySelector('input[name="db_port"]').value;

            return confirm(`هل أنت متأكد من إنشاء قاعدة البيانات؟\n\nالخادم: ${dbHost}:${dbPort}\nاسم قاعدة البيانات: ${dbName}\n\nسيتم إنشاء قاعدة البيانات وجميع الجداول المطلوبة.`);
        }

            // Auto-redirect after success message
            const successAlert = document.querySelector('.alert-success');
            if (successAlert) {
                const currentStep = <?= $current_step ?>;
                let nextStep = currentStep + 1;

                // Don't auto-redirect on final step
                if (currentStep < 6) {
                    setTimeout(() => {
                        // Add loading indicator
                        successAlert.innerHTML += '<br><small><i class="fas fa-spinner fa-spin"></i> جاري الانتقال للخطوة التالية...</small>';

                        setTimeout(() => {
                            window.location.href = `?step=${nextStep}`;
                        }, 1000);
                    }, 2000);
                }
            }
        });
    </script>
</body>
</html>
