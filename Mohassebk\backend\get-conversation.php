<?php
/**
 * Get AI Conversation Details
 * الحصول على تفاصيل محادثة الذكاء الاصطناعي
 */

require_once 'config/config.php';

// Set JSON response header
header('Content-Type: application/json');

// التحقق من تسجيل الدخول والصلاحيات
if (!isLoggedIn()) {
    echo json_encode([
        'success' => false,
        'error' => 'غير مصرح بالوصول'
    ]);
    exit;
}

$user = getCurrentUser();
if (!$user || $user['role'] !== 'accountant') {
    echo json_encode([
        'success' => false,
        'error' => 'غير مصرح بالوصول'
    ]);
    exit;
}

// الحصول على معرف المحادثة
$conversation_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($conversation_id <= 0) {
    echo json_encode([
        'success' => false,
        'error' => 'معرف المحادثة غير صحيح'
    ]);
    exit;
}

try {
    // الحصول على تفاصيل المحادثة
    $conversation = fetchOne(
        "SELECT * FROM ai_conversations 
         WHERE id = :id AND user_id = :user_id",
        ['id' => $conversation_id, 'user_id' => $user['id']]
    );
    
    if (!$conversation) {
        echo json_encode([
            'success' => false,
            'error' => 'المحادثة غير موجودة أو غير مصرح لك بالوصول إليها'
        ]);
        exit;
    }
    
    // تنسيق التاريخ
    $conversation['created_at'] = formatArabicDate($conversation['created_at']);
    
    echo json_encode([
        'success' => true,
        'conversation' => $conversation
    ]);
    
} catch (Exception $e) {
    logError("Get conversation error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'حدث خطأ أثناء جلب المحادثة'
    ]);
}
?>
