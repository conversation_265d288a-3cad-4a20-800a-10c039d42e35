# دليل النشر - نظام المحاسبك
## Deployment Guide - Mohassebk System

### متطلبات النظام / System Requirements

#### الخادم / Server Requirements
- **PHP**: 7.4 أو أحدث / 7.4 or newer
- **MySQL**: 5.7 أو أحدث / 5.7 or newer  
- **Apache/Nginx**: مع mod_rewrite / with mod_rewrite
- **SSL Certificate**: مطلوب للإنتاج / Required for production
- **Storage**: 5GB على الأقل / minimum 5GB

#### PHP Extensions المطلوبة / Required PHP Extensions
```
- mysqli
- pdo_mysql
- curl
- json
- mbstring
- fileinfo
- gd
- zip
```

### خطوات النشر / Deployment Steps

#### 1. تحضير الخادم / Server Preparation

```bash
# تحديث النظام / Update system
sudo apt update && sudo apt upgrade -y

# تثبيت Apache و PHP و MySQL
sudo apt install apache2 php php-mysql mysql-server php-curl php-json php-mbstring php-gd php-zip -y

# تفعيل mod_rewrite
sudo a2enmod rewrite
sudo systemctl restart apache2
```

#### 2. إعداد قاعدة البيانات / Database Setup

```sql
-- إنشاء قاعدة البيانات
CREATE DATABASE mohassebk_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- إنشاء مستخدم قاعدة البيانات
CREATE USER 'mohassebk_user'@'localhost' IDENTIFIED BY 'strong_password_here';
GRANT ALL PRIVILEGES ON mohassebk_db.* TO 'mohassebk_user'@'localhost';
FLUSH PRIVILEGES;

-- استيراد هيكل قاعدة البيانات
mysql -u mohassebk_user -p mohassebk_db < database/mohassebk_db.sql
```

#### 3. رفع الملفات / File Upload

```bash
# رفع الملفات إلى الخادم
scp -r * user@server:/var/www/html/mohassebk/

# أو استخدام Git
git clone https://github.com/your-repo/mohassebk.git /var/www/html/mohassebk/
```

#### 4. إعداد الصلاحيات / Set Permissions

```bash
# الانتقال لمجلد المشروع
cd /var/www/html/mohassebk/

# إعداد صلاحيات المجلدات
sudo chown -R www-data:www-data .
sudo chmod -R 755 .

# صلاحيات خاصة للمجلدات القابلة للكتابة
sudo chmod -R 777 uploads/
sudo chmod -R 777 logs/
sudo chmod -R 755 backend/config/
```

#### 5. تكوين النظام / System Configuration

```php
// نسخ وتعديل ملف التكوين
cp backend/config/config.example.php backend/config/config.php

// تعديل المتغيرات في config.php:
define('DB_HOST', 'localhost');
define('DB_NAME', 'mohassebk_db');
define('DB_USER', 'mohassebk_user');
define('DB_PASS', 'your_database_password');

define('SITE_URL', 'https://yourdomain.com');
define('SITE_NAME', 'المحاسبك');

// إعداد الذكاء الاصطناعي
define('OPENROUTER_API_KEY', 'your_openrouter_api_key');
define('AI_MODEL', 'anthropic/claude-3-haiku');
```

#### 6. إعداد Apache Virtual Host

```apache
<VirtualHost *:80>
    ServerName yourdomain.com
    DocumentRoot /var/www/html/mohassebk
    
    <Directory /var/www/html/mohassebk>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/mohassebk_error.log
    CustomLog ${APACHE_LOG_DIR}/mohassebk_access.log combined
</VirtualHost>

<VirtualHost *:443>
    ServerName yourdomain.com
    DocumentRoot /var/www/html/mohassebk
    
    SSLEngine on
    SSLCertificateFile /path/to/certificate.crt
    SSLCertificateKeyFile /path/to/private.key
    
    <Directory /var/www/html/mohassebk>
        AllowOverride All
        Require all granted
    </Directory>
    
    ErrorLog ${APACHE_LOG_DIR}/mohassebk_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/mohassebk_ssl_access.log combined
</VirtualHost>
```

#### 7. إعداد SSL Certificate

```bash
# استخدام Let's Encrypt
sudo apt install certbot python3-certbot-apache -y
sudo certbot --apache -d yourdomain.com

# أو رفع شهادة SSL يدوياً
sudo cp certificate.crt /etc/ssl/certs/
sudo cp private.key /etc/ssl/private/
```

#### 8. إنشاء المستخدم الإداري / Create Admin User

```php
// تشغيل سكريبت إنشاء المدير
php backend/scripts/create-admin.php
```

أو إدخال البيانات يدوياً في قاعدة البيانات:

```sql
INSERT INTO users (username, email, password, full_name, role, is_active, email_verified) 
VALUES (
    'admin',
    '<EMAIL>',
    '$2y$10$example_hashed_password',
    'مدير النظام',
    'admin',
    1,
    1
);
```

### اختبار النظام / System Testing

#### تشغيل اختبارات النظام
```bash
# الوصول لصفحة الاختبار
https://yourdomain.com/tests/system-test.php
```

#### اختبارات يدوية / Manual Tests
1. **تسجيل الدخول**: تجربة تسجيل الدخول بحساب المدير
2. **إنشاء حساب**: تجربة إنشاء حساب عميل جديد
3. **طلب خدمة**: تجربة طلب خدمة محاسبية
4. **رفع مستندات**: تجربة رفع ملف
5. **نظام الدفع**: تجربة عملية دفع تجريبية
6. **الذكاء الاصطناعي**: تجربة المساعد الذكي

### الأمان / Security

#### إعدادات الأمان المطلوبة
```apache
# إخفاء معلومات الخادم
ServerTokens Prod
ServerSignature Off

# حماية الملفات الحساسة
<Files "config.php">
    Require all denied
</Files>

<Files "*.log">
    Require all denied
</Files>
```

#### تحديث كلمات المرور
```bash
# تغيير كلمة مرور قاعدة البيانات
mysql -u root -p
ALTER USER 'mohassebk_user'@'localhost' IDENTIFIED BY 'new_strong_password';

# تحديث config.php بكلمة المرور الجديدة
```

### النسخ الاحتياطي / Backup

#### نسخ احتياطي لقاعدة البيانات
```bash
# إنشاء نسخة احتياطية يومية
mysqldump -u mohassebk_user -p mohassebk_db > backup_$(date +%Y%m%d).sql

# أتمتة النسخ الاحتياطي (crontab)
0 2 * * * mysqldump -u mohassebk_user -p'password' mohassebk_db > /backups/mohassebk_$(date +\%Y\%m\%d).sql
```

#### نسخ احتياطي للملفات
```bash
# نسخ احتياطي للملفات المرفوعة
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz uploads/

# نسخ احتياطي شامل
tar -czf mohassebk_full_backup_$(date +%Y%m%d).tar.gz /var/www/html/mohassebk/
```

### المراقبة / Monitoring

#### مراقبة السجلات
```bash
# مراقبة سجلات Apache
tail -f /var/log/apache2/mohassebk_error.log

# مراقبة سجلات النظام
tail -f /var/www/html/mohassebk/logs/system.log
```

#### مراقبة الأداء
- استخدام أدوات مثل htop لمراقبة استخدام الموارد
- مراقبة استخدام قاعدة البيانات
- مراقبة مساحة التخزين

### استكشاف الأخطاء / Troubleshooting

#### مشاكل شائعة وحلولها

**1. خطأ في الاتصال بقاعدة البيانات**
```bash
# التحقق من حالة MySQL
sudo systemctl status mysql

# إعادة تشغيل MySQL
sudo systemctl restart mysql
```

**2. مشاكل الصلاحيات**
```bash
# إعادة تعيين الصلاحيات
sudo chown -R www-data:www-data /var/www/html/mohassebk/
sudo chmod -R 755 /var/www/html/mohassebk/
sudo chmod -R 777 /var/www/html/mohassebk/uploads/
```

**3. مشاكل SSL**
```bash
# تجديد شهادة Let's Encrypt
sudo certbot renew

# اختبار تكوين SSL
sudo apache2ctl configtest
```

### التحديثات / Updates

#### تحديث النظام
```bash
# نسخ احتياطي قبل التحديث
cp -r /var/www/html/mohassebk /backups/mohassebk_before_update

# تحديث الملفات
git pull origin main

# تشغيل سكريبت التحديث إذا وجد
php backend/scripts/update.php
```

### الدعم / Support

للحصول على الدعم الفني:
- البريد الإلكتروني: <EMAIL>
- الهاتف: +966-XX-XXX-XXXX
- الموقع: https://mohassebk.com/support

---

**ملاحظة**: تأكد من اختبار جميع الوظائف في بيئة تطوير قبل النشر في الإنتاج.

**Note**: Make sure to test all functionality in a development environment before deploying to production.
