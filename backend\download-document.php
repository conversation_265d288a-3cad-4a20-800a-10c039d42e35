<?php
/**
 * Download Document
 * تحميل المستند
 */

require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    redirect(getUrl('login.php'));
}

$user = getCurrentUser();
$document_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($document_id <= 0) {
    die('معرف المستند غير صحيح');
}

try {
    // الحصول على تفاصيل المستند والتحقق من الصلاحيات
    $sql = "SELECT d.*, sr.user_id as request_user_id, sr.assigned_accountant_id
            FROM documents d 
            INNER JOIN service_requests sr ON d.request_id = sr.id 
            WHERE d.id = :id";
    
    $document = fetchOne($sql, ['id' => $document_id]);
    
    if (!$document) {
        die('المستند غير موجود');
    }
    
    // التحقق من الصلاحيات
    $has_access = false;
    
    if ($user['role'] === 'admin') {
        $has_access = true;
    } elseif ($user['role'] === 'accountant' && $document['assigned_accountant_id'] == $user['id']) {
        $has_access = true;
    } elseif ($user['role'] === 'client' && $document['request_user_id'] == $user['id']) {
        $has_access = true;
    }
    
    if (!$has_access) {
        die('غير مصرح لك بتحميل هذا المستند');
    }
    
    // التحقق من وجود الملف
    $file_path = __DIR__ . '/../' . $document['file_path'];
    
    if (!file_exists($file_path)) {
        die('الملف غير موجود على الخادم');
    }
    
    // تحديد نوع المحتوى
    $mime_type = $document['mime_type'] ?? 'application/octet-stream';
    
    // إعداد الهيدرز للتحميل
    header('Content-Type: ' . $mime_type);
    header('Content-Length: ' . filesize($file_path));
    header('Content-Disposition: attachment; filename="' . $document['original_filename'] . '"');
    header('Cache-Control: private, max-age=0');
    header('Pragma: private');
    header('Expires: 0');
    
    // إرسال الملف
    readfile($file_path);
    
} catch (Exception $e) {
    logError("Download document error: " . $e->getMessage());
    die('حدث خطأ أثناء تحميل المستند');
}
?>
