<?php
/**
 * Document Download Handler
 * معالج تحميل المستندات
 */

require_once 'config/config.php';

// التحقق من تسجيل الدخول
if (!isLoggedIn()) {
    http_response_code(401);
    die('غير مصرح لك بالوصول');
}

$user = getCurrentUser();
$document_id = (int)($_GET['id'] ?? 0);

if (!$document_id) {
    http_response_code(400);
    die('معرف المستند مطلوب');
}

try {
    // الحصول على معلومات المستند
    $sql = "SELECT d.*, sr.user_id as request_user_id 
            FROM documents d 
            LEFT JOIN service_requests sr ON d.request_id = sr.id 
            WHERE d.id = :id";
    
    $document = fetchOne($sql, ['id' => $document_id]);
    
    if (!$document) {
        http_response_code(404);
        die('المستند غير موجود');
    }
    
    // التحقق من الصلاحيات
    $can_access = false;
    
    if ($user['role'] === 'admin' || $user['role'] === 'accountant') {
        // الإدارة والمحاسبين يمكنهم الوصول لجميع المستندات
        $can_access = true;
    } elseif ($user['role'] === 'client') {
        // العملاء يمكنهم الوصول لمستنداتهم فقط
        if ($document['user_id'] == $user['id'] || $document['request_user_id'] == $user['id']) {
            $can_access = true;
        }
    }
    
    if (!$can_access) {
        http_response_code(403);
        die('غير مصرح لك بالوصول لهذا المستند');
    }
    
    // التحقق من وجود الملف
    if (!file_exists($document['file_path'])) {
        http_response_code(404);
        die('الملف غير موجود على الخادم');
    }
    
    // تسجيل عملية التحميل
    try {
        global $database;
        $log_data = [
            'user_id' => $user['id'],
            'document_id' => $document_id,
            'action' => 'download',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        // يمكن إضافة جدول للسجلات إذا لزم الأمر
        logError("Document download: User {$user['id']} downloaded document {$document_id}");
    } catch (Exception $e) {
        // تجاهل أخطاء التسجيل
    }
    
    // إعداد headers للتحميل
    $file_size = filesize($document['file_path']);
    $file_name = $document['original_filename'];
    
    // تنظيف اسم الملف
    $file_name = preg_replace('/[^a-zA-Z0-9\._\-\u0600-\u06FF]/', '_', $file_name);
    
    // تحديد نوع المحتوى
    $mime_types = [
        'pdf' => 'application/pdf',
        'doc' => 'application/msword',
        'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls' => 'application/vnd.ms-excel',
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'gif' => 'image/gif'
    ];
    
    $content_type = $mime_types[$document['file_type']] ?? 'application/octet-stream';
    
    // إرسال headers
    header('Content-Type: ' . $content_type);
    header('Content-Length: ' . $file_size);
    header('Content-Disposition: attachment; filename="' . $file_name . '"');
    header('Cache-Control: private, max-age=0, must-revalidate');
    header('Pragma: public');
    
    // قراءة وإرسال الملف
    $handle = fopen($document['file_path'], 'rb');
    if ($handle) {
        while (!feof($handle)) {
            echo fread($handle, 8192);
            flush();
        }
        fclose($handle);
    } else {
        http_response_code(500);
        die('خطأ في قراءة الملف');
    }
    
} catch (Exception $e) {
    logError("Document download error: " . $e->getMessage());
    http_response_code(500);
    die('حدث خطأ أثناء تحميل المستند');
}
?>
