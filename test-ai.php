<?php
/**
 * اختبار الذكاء الاصطناعي
 * AI Test File
 */

require_once 'backend/config/config.php';

echo "<h1>اختبار الذكاء الاصطناعي</h1>";

// 1. فحص الثوابت المطلوبة
echo "<h2>1. فحص الثوابت المطلوبة</h2>";

$required_constants = [
    'OPENROUTER_API_KEY' => 'مفتاح OpenRouter API',
    'OPENROUTER_API_URL' => 'رابط OpenRouter API',
    'AI_MODEL' => 'نموذج الذكاء الاصطناعي'
];

foreach ($required_constants as $constant => $description) {
    if (defined($constant)) {
        $value = constant($constant);
        if (!empty($value) && $value !== 'your_openrouter_api_key_here') {
            echo "✅ <strong>$description</strong>: " . (strlen($value) > 20 ? substr($value, 0, 20) . '...' : $value) . "<br>";
        } else {
            echo "❌ <strong>$description</strong>: غير محدد أو فارغ<br>";
        }
    } else {
        echo "❌ <strong>$description</strong>: غير موجود<br>";
    }
}

// 2. فحص cURL
echo "<h2>2. فحص cURL</h2>";
if (function_exists('curl_init')) {
    echo "✅ cURL متوفر<br>";
    
    // اختبار الاتصال بـ OpenRouter
    if (defined('OPENROUTER_API_URL')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, OPENROUTER_API_URL);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
        
        $result = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "❌ خطأ في الاتصال بـ OpenRouter: $error<br>";
        } else {
            echo "✅ الاتصال بـ OpenRouter ناجح (HTTP: $http_code)<br>";
        }
    }
} else {
    echo "❌ cURL غير متوفر<br>";
}

// 3. اختبار الذكاء الاصطناعي
echo "<h2>3. اختبار الذكاء الاصطناعي</h2>";

if (defined('OPENROUTER_API_KEY') && !empty(OPENROUTER_API_KEY) && OPENROUTER_API_KEY !== 'your_openrouter_api_key_here') {
    
    // محاولة إرسال رسالة تجريبية
    $test_message = "مرحبا، هل يمكنك مساعدتي في المحاسبة؟";
    
    echo "<p><strong>الرسالة التجريبية:</strong> $test_message</p>";
    echo "<p><strong>النموذج المستخدم:</strong> " . AI_MODEL . "</p>";
    echo "<p><strong>مفتاح API:</strong> " . substr(OPENROUTER_API_KEY, 0, 20) . "...</p>";
    
    try {
        // إعداد البيانات
        $data = [
            'model' => AI_MODEL,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'أنت مساعد محاسبي ذكي متخصص في المحاسبة والضرائب في الجزائر.'
                ],
                [
                    'role' => 'user',
                    'content' => $test_message
                ]
            ],
            'max_tokens' => 200,
            'temperature' => 0.7
        ];
        
        $headers = [
            'Authorization: Bearer ' . OPENROUTER_API_KEY,
            'Content-Type: application/json',
            'HTTP-Referer: ' . (defined('SITE_URL') ? SITE_URL : 'http://localhost'),
            'X-Title: ' . (defined('SITE_NAME') ? SITE_NAME : 'Mohassebk Test')
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, OPENROUTER_API_URL);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "❌ <strong>خطأ cURL:</strong> $error<br>";
        } elseif ($http_code !== 200) {
            echo "❌ <strong>خطأ HTTP:</strong> $http_code<br>";

            // تحليل نوع الخطأ
            $error_response = json_decode($response, true);
            if ($error_response && isset($error_response['error'])) {
                $error_msg = $error_response['error']['message'] ?? 'خطأ غير محدد';
                $error_code = $error_response['error']['code'] ?? $http_code;

                echo "<div style='background: #ffe6e6; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo "<strong>تفاصيل الخطأ:</strong><br>";
                echo "الكود: $error_code<br>";
                echo "الرسالة: $error_msg<br>";

                // اقتراحات للحل
                if ($http_code == 401) {
                    echo "<br><strong>الحلول المقترحة:</strong><br>";
                    echo "• تحقق من صحة مفتاح API<br>";
                    echo "• تأكد من أن المفتاح لم ينته صلاحيته<br>";
                    echo "• تحقق من رصيد الحساب في OpenRouter<br>";
                } elseif ($http_code == 429) {
                    echo "<br><strong>الحلول المقترحة:</strong><br>";
                    echo "• تم تجاوز الحد المسموح من الطلبات<br>";
                    echo "• انتظر قليلاً ثم حاول مرة أخرى<br>";
                } elseif ($http_code == 400) {
                    echo "<br><strong>الحلول المقترحة:</strong><br>";
                    echo "• تحقق من صحة النموذج المستخدم<br>";
                    echo "• تحقق من صيغة الطلب<br>";
                }
                echo "</div>";
            }

            echo "<details><summary>عرض الاستجابة الكاملة</summary>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
            echo "</details>";
        } else {
            $result = json_decode($response, true);
            
            if ($result && isset($result['choices'][0]['message']['content'])) {
                $ai_response = trim($result['choices'][0]['message']['content']);
                echo "✅ <strong>رد الذكاء الاصطناعي:</strong><br>";
                echo "<div style='background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 10px 0; border-left: 4px solid #007bff;'>";
                echo nl2br(htmlspecialchars($ai_response));
                echo "</div>";
                
                // معلومات إضافية
                if (isset($result['usage'])) {
                    echo "<small><strong>الاستخدام:</strong> ";
                    echo "Tokens: " . ($result['usage']['total_tokens'] ?? 'غير محدد');
                    echo "</small><br>";
                }
                
            } else {
                echo "❌ <strong>استجابة غير صحيحة:</strong><br>";
                echo "<pre>" . htmlspecialchars($response) . "</pre>";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ <strong>خطأ في الاختبار:</strong> " . $e->getMessage() . "<br>";
    }
    
} else {
    echo "❌ لا يمكن اختبار الذكاء الاصطناعي - مفتاح API غير صحيح<br>";
}

// 4. فحص ملفات الذكاء الاصطناعي
echo "<h2>4. فحص ملفات الذكاء الاصطناعي</h2>";

$ai_files = [
    'ai-assistant.php' => 'المساعد الذكي الرئيسي',
    'backend/ai/ai-helper.php' => 'مساعد الذكاء الاصطناعي',
    'backend/ai/analyze-document.php' => 'تحليل المستندات',
    'backend/ai/service-recommendations.php' => 'توصيات الخدمات',
    'dashboard/client/ai-recommendations.php' => 'صفحة التوصيات الذكية'
];

foreach ($ai_files as $file => $description) {
    if (file_exists($file)) {
        echo "✅ <strong>$description</strong>: $file<br>";
    } else {
        echo "❌ <strong>$description</strong>: $file (غير موجود)<br>";
    }
}

echo "<hr>";
echo "<h2>الخلاصة</h2>";
echo "<p>إذا كانت جميع الفحوصات ✅، فالذكاء الاصطناعي يعمل بشكل صحيح.</p>";
echo "<p>إذا كان هناك أي ❌، يرجى إصلاح المشاكل المذكورة أعلاه.</p>";

echo "<br><div style='background: #f8f9fa; padding: 15px; border-radius: 8px;'>";
echo "<h3>روابط الاختبار:</h3>";
echo "<ul>";
echo "<li><a href='ai-assistant.php' target='_blank'>المساعد الذكي الرئيسي</a></li>";
echo "<li><a href='dashboard/client/ai-recommendations.php' target='_blank'>التوصيات الذكية</a></li>";
echo "<li><a href='test-ai.php' onclick='location.reload()'>إعادة تشغيل الاختبار</a></li>";
echo "</ul>";
echo "</div>";

echo "<br><div style='background: #fff3cd; padding: 15px; border-radius: 8px;'>";
echo "<h3>ملاحظات مهمة:</h3>";
echo "<ul>";
echo "<li>تأكد من أن مفتاح OpenRouter API صحيح وفعال</li>";
echo "<li>تأكد من اتصال الإنترنت</li>";
echo "<li>قد تحتاج إلى رصيد في حساب OpenRouter للاستخدام</li>";
echo "<li>النموذج المستخدم: " . (defined('AI_MODEL') ? AI_MODEL : 'غير محدد') . "</li>";
echo "</ul>";
echo "</div>";

// إضافة قسم النماذج المقترحة
echo "<br><div style='background: #e8f5e8; padding: 15px; border-radius: 8px;'>";
echo "<h3>نماذج مجانية مقترحة:</h3>";
echo "<ul>";
echo "<li><code>deepseek/deepseek-chat-v3-0324:free</code> - النموذج الحالي</li>";
echo "<li><code>deepseek/deepseek-chat:free</code> - النموذج القديم</li>";
echo "<li><code>google/gemma-2-9b-it:free</code> - نموذج Google</li>";
echo "<li><code>microsoft/phi-3-mini-128k-instruct:free</code> - نموذج Microsoft</li>";
echo "<li><code>meta-llama/llama-3.2-3b-instruct:free</code> - نموذج Meta</li>";
echo "</ul>";
echo "<p><small>يمكنك تغيير النموذج في ملف <code>backend/config/config.php</code></small></p>";
echo "</div>";

// إضافة قسم استكشاف الأخطاء
echo "<br><div style='background: #f0f8ff; padding: 15px; border-radius: 8px;'>";
echo "<h3>استكشاف الأخطاء الشائعة:</h3>";
echo "<ul>";
echo "<li><strong>خطأ 401:</strong> مفتاح API غير صحيح أو منتهي الصلاحية</li>";
echo "<li><strong>خطأ 429:</strong> تجاوز الحد المسموح من الطلبات</li>";
echo "<li><strong>خطأ 400:</strong> خطأ في صيغة الطلب أو النموذج غير صحيح</li>";
echo "<li><strong>خطأ 404:</strong> النموذج غير موجود أو غير متاح</li>";
echo "<li><strong>خطأ 500:</strong> خطأ في خادم OpenRouter</li>";
echo "</ul>";
echo "</div>";
?>
